# DateRangePicker Integration Guide

## Overview

The DateRangePicker component has been enhanced to automatically integrate with the new Accommodation Unavailability API. It now fetches comprehensive unavailability data and applies appropriate restrictions to the Vue3HotelDatePicker component.

## Features

### Automatic Unavailability Integration
- **Disabled Dates**: Automatically disables dates that are unavailable due to:
  - Existing bookings
  - Accommodation unavailable periods (date ranges and recurring days)
  - Minimum booking notice requirements
- **Minimum Stay**: Enforces accommodation-specific minimum night requirements
- **Date Range Limits**: Sets appropriate min/max date boundaries

### Loading States
- Shows loading spinner while fetching unavailability data
- Displays error messages with retry functionality
- Graceful handling of API failures

### Real-time Updates
- Automatically refetches data when accommodation changes
- Watches for accommodation ID changes and updates restrictions accordingly

## Props

| Prop | Type | Required | Default | Description |
|------|------|----------|---------|-------------|
| `accommodation` | Object | Yes | - | Accommodation object with ID |
| `apiBaseUrl` | String | Yes | - | Base URL for API calls |
| `authToken` | String | Yes | - | Authentication token |
| `componentVersion` | String | Yes | - | Component version for API headers |
| `customMinDate` | String/Date | No | null | Override default minimum date |
| `customMaxDate` | String/Date | No | null | Override default maximum date |
| `dateRangeMonths` | Number | No | 12 | Months ahead to fetch data for |

## Events

| Event | Payload | Description |
|-------|---------|-------------|
| `date-range-selected` | `{ start: Date, end: Date }` | Emitted when user selects date range |
| `error` | `{ error: String, originalError: Error }` | Emitted on API or validation errors |
| `unavailability-loaded` | `Object` | Emitted when unavailability data is loaded |

## Usage Examples

### Basic Usage

```vue
<template>
  <DateRangePicker 
    :accommodation="accommodation"
    :api-base-url="apiBaseUrl"
    :auth-token="authToken"
    :component-version="componentVersion"
    @date-range-selected="handleDateSelection"
    @error="handleError"
  />
</template>

<script setup>
import DateRangePicker from './DateRangePicker.vue';

const accommodation = {
  id: 1,
  name: "Beach House",
  minimum_stay: 2,
  minimum_booking_notice: 1
};

const handleDateSelection = (dateRange) => {
  console.log('Selected dates:', dateRange);
  // dateRange = { start: Date, end: Date }
};

const handleError = (errorData) => {
  console.error('Date picker error:', errorData.error);
};
</script>
```

### Advanced Usage with Custom Restrictions

```vue
<template>
  <DateRangePicker 
    :accommodation="accommodation"
    :api-base-url="apiBaseUrl"
    :auth-token="authToken"
    :component-version="componentVersion"
    :custom-min-date="customMinDate"
    :custom-max-date="customMaxDate"
    :date-range-months="6"
    @date-range-selected="handleDateSelection"
    @error="handleError"
    @unavailability-loaded="handleUnavailabilityLoaded"
  />
</template>

<script setup>
import { ref, computed } from 'vue';
import DateRangePicker from './DateRangePicker.vue';

// Custom date restrictions
const customMinDate = computed(() => {
  // Allow bookings starting 3 days from now
  const date = new Date();
  date.setDate(date.getDate() + 3);
  return date.toISOString().split('T')[0];
});

const customMaxDate = computed(() => {
  // Allow bookings up to 6 months ahead
  const date = new Date();
  date.setMonth(date.getMonth() + 6);
  return date.toISOString().split('T')[0];
});

const handleUnavailabilityLoaded = (data) => {
  console.log('Unavailability data loaded:', data);
  // Access: data.unavailable_dates, data.minimum_stay, etc.
};
</script>
```

### Integration with AvailabilityChecker

The DateRangePicker is automatically integrated into the AvailabilityChecker component:

```vue
<template>
  <AvailabilityChecker
    :accommodation="accommodation"
    :api-base-url="apiBaseUrl"
    :auth-token="authToken"
    :component-version="componentVersion"
    @availability-result="handleAvailabilityResult"
    @error="handleError"
    @unavailability-loaded="handleUnavailabilityLoaded"
  />
</template>
```

## API Integration Details

### Data Fetching
The component automatically fetches unavailability data using:
```
GET /api/accommodations/{id}/unavailability-data?format=dates_array&start_date=YYYY-MM-DD&end_date=YYYY-MM-DD
```

### Response Processing
The component processes the API response and applies restrictions:

```javascript
// API Response
{
  "data": {
    "accommodation_id": 1,
    "unavailable_dates": ["2024-01-01", "2024-01-02", "2024-01-15"],
    "minimum_stay": 2,
    "minimum_booking_notice": 1,
    "minimum_booking_notice_cutoff": "2024-01-02"
  }
}

// Applied to Vue3HotelDatePicker
{
  disabledDates: ["2024-01-01", "2024-01-02", "2024-01-15"],
  minNights: 2,
  minDate: "2024-01-02", // Based on minimum_booking_notice_cutoff
  maxDate: "2025-01-01"  // Default 2 years or customMaxDate
}
```

## Vue3HotelDatePicker Integration

The component leverages these Vue3HotelDatePicker props:

- **`disabledDates`**: Array of unavailable dates from API
- **`minDate`**: Calculated from minimum booking notice
- **`maxDate`**: Custom or default (2 years ahead)
- **`minNights`**: From accommodation minimum_stay
- **`format`**: Set to 'YYYY-MM-DD' for consistency

## Error Handling

### API Errors
```javascript
// Network or server errors
{
  error: "Failed to fetch unavailability data: 404",
  originalError: Error
}
```

### Validation Errors
```javascript
// Invalid accommodation or missing data
{
  error: "Accommodation ID is required",
  originalError: null
}
```

### Retry Mechanism
- Automatic retry button on API failures
- Manual refresh capability
- Graceful degradation when API is unavailable

## Performance Considerations

### Optimizations
1. **Date Range Limiting**: Only fetches data for specified months ahead
2. **Caching**: Component caches data until accommodation changes
3. **Debouncing**: Prevents excessive API calls on rapid prop changes

### Best Practices
```javascript
// Limit data fetching range for better performance
<DateRangePicker 
  :date-range-months="6"  // Only fetch 6 months ahead
  :accommodation="accommodation"
  // ... other props
/>

// Use custom date limits to reduce API load
const customMaxDate = computed(() => {
  const date = new Date();
  date.setMonth(date.getMonth() + 3); // Limit to 3 months
  return date.toISOString().split('T')[0];
});
```

## Styling

### CSS Classes
- `.loading-state`: Loading spinner and message
- `.error-state`: Error message and retry button
- `.date-picker-container`: Main container

### Customization
```css
/* Override loading state */
.date-picker-container .loading-state {
  background: your-color;
  border: your-border;
}

/* Override error state */
.date-picker-container .error-state {
  background: your-error-color;
}

/* Style the underlying Vue3HotelDatePicker */
.date-picker-container :deep(.vue3-hotel-datepicker) {
  /* Your custom styles */
}
```

## Migration from Old Component

### Before (Simple DateRangePicker)
```vue
<DateRangePicker @date-range-selected="handleSelection" />
```

### After (Enhanced with Unavailability)
```vue
<DateRangePicker 
  :accommodation="accommodation"
  :api-base-url="apiBaseUrl"
  :auth-token="authToken"
  :component-version="componentVersion"
  @date-range-selected="handleSelection"
  @error="handleError"
/>
```

### Required Changes
1. Add accommodation prop
2. Add API configuration props
3. Add error handling
4. Update parent component to handle new events

## Troubleshooting

### Common Issues

1. **No dates are selectable**
   - Check if accommodation has valid ID
   - Verify API endpoint is accessible
   - Check minimum booking notice settings

2. **Loading state persists**
   - Verify API authentication
   - Check network connectivity
   - Review browser console for errors

3. **Incorrect date restrictions**
   - Verify accommodation minimum_stay setting
   - Check unavailable periods configuration
   - Review existing bookings data

### Debug Mode
Use the DateRangePickerExample component for debugging:
```vue
<DateRangePickerExample 
  :accommodation="accommodation"
  :api-base-url="apiBaseUrl"
  :auth-token="authToken"
  :component-version="componentVersion"
/>
```

This provides detailed debug information and real-time data inspection.
