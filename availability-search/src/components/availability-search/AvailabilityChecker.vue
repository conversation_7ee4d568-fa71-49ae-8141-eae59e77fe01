<template>
  <div class="availability-check">
    <h3>Check Availability</h3>

    <DateRangePicker @date-range-selected="setDateRange" />

    <OccupancySelector
      v-model="numberOfPersons"
      :minOccupancy="accommodation?.min_occupancy || 1"
      :maxOccupancy="accommodation?.max_occupancy || 10"
      :showLimits="!!accommodation"
    />

    <button
      class="check-availability-button"
      @click="checkAvailability"
      :disabled="!dateRange.start || !dateRange.end || isChecking"
    >
      {{ isChecking ? 'Checking...' : 'Check Availability' }}
    </button>

    <div v-if="availabilityResult" class="availability-result">
      <div v-if="availabilityResult.available && !bookingSuccess" class="available">
        <div class="availability-result-container">
          <!-- Left column: Booking form (2/3 width) -->
          <BookingForm
            :accommodationId="accommodation.id"
            :isSubmitting="bookingInProgress"
            @submit="submitBooking"
            @show-terms="$emit('show-terms')"
          />

          <!-- Right column: Pricing breakdown (1/3 width) -->
          <PricingBreakdown
            :availabilityResult="availabilityResult"
            :dateRange="dateRange"
            :numberOfPersons="numberOfPersons"
          />
        </div>
      </div>

      <!-- Booking Success View -->
      <BookingSuccess
        v-if="bookingSuccess"
        :accommodation="accommodation"
        :dateRange="dateRange"
        :numberOfPersons="numberOfPersons"
        :totalPrice="availabilityResult.total_price"
        :bookingReference="bookingReference"
        :notes="bookingForm.notes"
        @reset="$emit('reset')"
      />

      <!-- Not Available View -->
      <div v-else-if="!availabilityResult.available" class="not-available">
        <div class="availability-icon">✗</div>
        <div class="availability-message">
          <p>Sorry, not available for the selected dates.</p>
          <p v-if="availabilityResult.message">{{ availabilityResult.message }}</p>
          <p v-if="availabilityResult.reason === 'minimum_notice' && availabilityResult.details">
            This accommodation requires bookings to be made at least {{ availabilityResult.details.minimum_booking_notice }} days in advance.
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import DateRangePicker from './DateRangePicker.vue';
import OccupancySelector from './OccupancySelector.vue';
import BookingForm from './BookingForm.vue';
import PricingBreakdown from './PricingBreakdown.vue';
import BookingSuccess from './BookingSuccess.vue';

const props = defineProps({
  accommodation: {
    type: Object,
    required: true
  },
  apiBaseUrl: {
    type: String,
    required: true
  },
  authToken: {
    type: String,
    required: true
  },
  componentVersion: {
    type: String,
    required: true
  }
});

const emit = defineEmits([
  'check-availability',
  'availability-result',
  'booking-created',
  'error',
  'show-terms',
  'reset'
]);

// State
const dateRange = ref({ start: null, end: null });
const numberOfPersons = ref(props.accommodation?.min_occupancy || 1);
const availabilityResult = ref(null);
const isChecking = ref(false);
const bookingForm = ref({
  first_name: '',
  last_name: '',
  email: '',
  contact_number: '',
  notes: '',
  terms: false
});
const bookingInProgress = ref(false);
const bookingSuccess = ref(false);
const bookingReference = ref('');

// Methods
const setDateRange = (range) => {
  // Update date range (this will trigger the watcher to clear availability results)
  dateRange.value = range;
};

const getAuthHeaders = () => {
  return {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Widget-Version': props.componentVersion,
    'Authorization': `Bearer ${props.authToken}`
  };
};

const normalizeToLocalMidnight = (date) => {
  const localDate = new Date(date);
  localDate.setMinutes(localDate.getMinutes() - localDate.getTimezoneOffset());
  return localDate.toISOString().split('T')[0];
};

// Add a new method to fetch comprehensive unavailability data for calendar widgets
const getUnavailabilityData = async (format = 'detailed') => {
  if (!props.accommodation) {
    return null;
  }

  try {
    const url = `${props.apiBaseUrl}/api/accommodations/{id}/unavailability-data`.replace('{id}', props.accommodation.id);
    const params = new URLSearchParams({
      format: format,
      // Optional: add date range filters
      // start_date: '2024-01-01',
      // end_date: '2024-12-31'
    });

    const response = await fetch(`${url}?${params}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    const result = await response.json();
    return result.data;
  } catch (err) {
    emit('error', { error: `Failed to fetch unavailability data: ${err.message}`, originalError: err });
    return null;
  }
};

const checkAvailability = async () => {
  if (!props.accommodation || !dateRange.value.start || !dateRange.value.end) {
    return;
  }

  isChecking.value = true;
  availabilityResult.value = null;

  try {
    const url = `${props.apiBaseUrl}/api/accommodations/{id}/check-availability`.replace('{id}', props.accommodation.id);
    const requestData = {
      start_date: normalizeToLocalMidnight(dateRange.value.start),
      end_date: normalizeToLocalMidnight(dateRange.value.end),
      number_of_persons: numberOfPersons.value
    };

    const response = await fetch(url, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    const result = await response.json();
    availabilityResult.value = result;
    emit('availability-result', { result });
  } catch (err) {
    emit('error', { error: `Failed to check availability: ${err.message}`, originalError: err });
  } finally {
    isChecking.value = false;
  }
};

const submitBooking = async (formData) => {
  bookingInProgress.value = true;
  bookingForm.value = formData;

  try {
    // Prepare the booking data
    const bookingData = {
      accommodation_id: props.accommodation.id,
      first_name: formData.first_name,
      last_name: formData.last_name,
      email: formData.email,
      contact_number: formData.contact_number,
      start_date: normalizeToLocalMidnight(dateRange.value.start),
      end_date: normalizeToLocalMidnight(dateRange.value.end),
      occupancy: numberOfPersons.value,
      booking_status_id: 2, // Pending status
      notes: formData.notes
    };

    // Make the API request
    const bookingsEndpoint = `${props.apiBaseUrl}/api/bookings`;
    const response = await fetch(bookingsEndpoint, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(bookingData)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to create booking');
    }

    const result = await response.json();
    processBookingResult(result);
  } catch (err) {
    emit('error', { error: `Failed to create booking: ${err.message}`, originalError: err });
  } finally {
    bookingInProgress.value = false;
  }
};

const processBookingResult = (result) => {
  // Set booking reference from the response
  bookingReference.value = `#${result.booking.id}`;

  // Show success view
  bookingSuccess.value = true;

  // Emit event
  emit('booking-created', { booking: result.booking });
};

// Watch for changes in date range and number of persons
// Clear availability results when either changes
watch(dateRange, () => {
  if (availabilityResult.value) {
    availabilityResult.value = null;
    bookingSuccess.value = false;
  }
}, { deep: true });

watch(numberOfPersons, () => {
  if (availabilityResult.value) {
    availabilityResult.value = null;
    bookingSuccess.value = false;
  }
});

// Expose methods for parent components
defineExpose({
  getUnavailabilityData,
  checkAvailability
});
</script>

<style>
/* Styles moved to separate CSS file */
</style>
