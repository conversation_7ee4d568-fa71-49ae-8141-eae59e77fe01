<template>
  <div class="date-range-picker-example">
    <h3>Enhanced Date Range Picker Example</h3>
    
    <!-- Accommodation Info -->
    <div v-if="accommodation" class="accommodation-info">
      <h4>{{ accommodation.name }}</h4>
      <p>Testing the enhanced date picker with unavailability restrictions</p>
    </div>

    <!-- Date Range Picker with Unavailability Integration -->
    <div class="picker-section">
      <h5>Select Your Dates</h5>
      <DateRangePicker 
        :accommodation="accommodation"
        :api-base-url="apiBaseUrl"
        :auth-token="authToken"
        :component-version="componentVersion"
        :date-range-months="6"
        @date-range-selected="handleDateRangeSelected"
        @error="handleError"
        @unavailability-loaded="handleUnavailabilityLoaded"
      />
    </div>

    <!-- Selected Date Range Display -->
    <div v-if="selectedDateRange" class="selected-dates">
      <h5>Selected Date Range:</h5>
      <p><strong>Check-in:</strong> {{ formatDate(selectedDateRange.start) }}</p>
      <p><strong>Check-out:</strong> {{ formatDate(selectedDateRange.end) }}</p>
      <p><strong>Nights:</strong> {{ calculateNights(selectedDateRange.start, selectedDateRange.end) }}</p>
    </div>

    <!-- Unavailability Information -->
    <div v-if="unavailabilityInfo" class="unavailability-info">
      <h5>Accommodation Restrictions:</h5>
      <div class="restrictions-grid">
        <div class="restriction-item">
          <span class="label">Minimum Stay:</span>
          <span class="value">{{ unavailabilityInfo.minimum_stay || 1 }} night(s)</span>
        </div>
        <div class="restriction-item">
          <span class="label">Minimum Booking Notice:</span>
          <span class="value">{{ unavailabilityInfo.minimum_booking_notice || 0 }} day(s)</span>
        </div>
        <div v-if="unavailabilityInfo.minimum_booking_notice_cutoff" class="restriction-item">
          <span class="label">Cannot book before:</span>
          <span class="value">{{ unavailabilityInfo.minimum_booking_notice_cutoff }}</span>
        </div>
        <div class="restriction-item">
          <span class="label">Unavailable Dates:</span>
          <span class="value">{{ unavailabilityInfo.unavailable_dates?.length || 0 }} date(s) blocked</span>
        </div>
      </div>
    </div>

    <!-- Error Display -->
    <div v-if="error" class="error-message">
      <h5>Error:</h5>
      <p>{{ error }}</p>
    </div>

    <!-- Debug Information (for development) -->
    <div v-if="showDebug" class="debug-section">
      <h5>Debug Information:</h5>
      <details>
        <summary>Unavailability Data</summary>
        <pre>{{ JSON.stringify(unavailabilityInfo, null, 2) }}</pre>
      </details>
      <details>
        <summary>Selected Date Range</summary>
        <pre>{{ JSON.stringify(selectedDateRange, null, 2) }}</pre>
      </details>
    </div>

    <!-- Controls -->
    <div class="controls">
      <button @click="showDebug = !showDebug" class="debug-toggle">
        {{ showDebug ? 'Hide' : 'Show' }} Debug Info
      </button>
      <button @click="clearSelection" class="clear-button">
        Clear Selection
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import DateRangePicker from './DateRangePicker.vue';

const props = defineProps({
  accommodation: {
    type: Object,
    required: true
  },
  apiBaseUrl: {
    type: String,
    required: true
  },
  authToken: {
    type: String,
    required: true
  },
  componentVersion: {
    type: String,
    required: true
  }
});

const emit = defineEmits(['date-range-selected', 'error']);

// State
const selectedDateRange = ref(null);
const unavailabilityInfo = ref(null);
const error = ref(null);
const showDebug = ref(false);

// Methods
const handleDateRangeSelected = (dateRange) => {
  selectedDateRange.value = dateRange;
  error.value = null;
  emit('date-range-selected', dateRange);
};

const handleUnavailabilityLoaded = (data) => {
  unavailabilityInfo.value = data;
  error.value = null;
};

const handleError = (errorData) => {
  error.value = errorData.error;
  emit('error', errorData);
};

const formatDate = (date) => {
  if (!date) return '';
  return new Date(date).toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

const calculateNights = (startDate, endDate) => {
  if (!startDate || !endDate) return 0;
  const start = new Date(startDate);
  const end = new Date(endDate);
  const diffTime = Math.abs(end - start);
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

const clearSelection = () => {
  selectedDateRange.value = null;
  error.value = null;
};
</script>

<style scoped>
.date-range-picker-example {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.accommodation-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 4px solid #007bff;
}

.accommodation-info h4 {
  margin: 0 0 5px 0;
  color: #333;
}

.accommodation-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.picker-section {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
}

.picker-section h5 {
  margin: 0 0 15px 0;
  color: #333;
}

.selected-dates {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 8px;
  padding: 15px;
  margin: 20px 0;
}

.selected-dates h5 {
  margin: 0 0 10px 0;
  color: #155724;
}

.selected-dates p {
  margin: 5px 0;
  color: #155724;
}

.unavailability-info {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 15px;
  margin: 20px 0;
}

.unavailability-info h5 {
  margin: 0 0 15px 0;
  color: #856404;
}

.restrictions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 10px;
}

.restriction-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #ffeaa7;
}

.restriction-item:last-child {
  border-bottom: none;
}

.restriction-item .label {
  font-weight: 500;
  color: #856404;
}

.restriction-item .value {
  color: #533f03;
  font-weight: 600;
}

.error-message {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  padding: 15px;
  margin: 20px 0;
}

.error-message h5 {
  margin: 0 0 10px 0;
  color: #721c24;
}

.error-message p {
  margin: 0;
  color: #721c24;
}

.debug-section {
  background: #e2e3e5;
  border: 1px solid #d6d8db;
  border-radius: 8px;
  padding: 15px;
  margin: 20px 0;
}

.debug-section h5 {
  margin: 0 0 15px 0;
  color: #383d41;
}

.debug-section details {
  margin: 10px 0;
}

.debug-section summary {
  cursor: pointer;
  font-weight: 500;
  color: #383d41;
  padding: 5px 0;
}

.debug-section pre {
  background: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  color: #495057;
}

.controls {
  display: flex;
  gap: 10px;
  margin: 20px 0;
}

.debug-toggle, .clear-button {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.debug-toggle {
  background: #6c757d;
  color: white;
}

.debug-toggle:hover {
  background: #5a6268;
}

.clear-button {
  background: #dc3545;
  color: white;
}

.clear-button:hover {
  background: #c82333;
}
</style>
