<template>
  <div class="date-picker-container">
    <!-- Loading state -->
    <div v-if="loading" class="loading-state">
      <div class="loading-spinner"></div>
      <span>Loading available dates...</span>
    </div>

    <!-- Error state -->
    <div v-if="error && !loading" class="error-state">
      <span>{{ error }}</span>
      <button @click="fetchUnavailabilityData" class="retry-button">Retry</button>
    </div>

    <!-- Date picker -->
    <Vue3HotelDatePicker
      v-if="!loading"
      :disabled-dates="disabledDates"
      :min-date="minDate"
      :max-date="maxDate"
      :min-nights="minNights"
      :max-nights="maxNights"
      :format="dateFormat"
      @selected="handleDateSelection"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import Vue3HotelDatePicker from "@sebarkar/vue3-hotel-datepicker";

const props = defineProps({
  accommodation: {
    type: Object,
    required: true
  },
  apiBaseUrl: {
    type: String,
    required: true
  },
  authToken: {
    type: String,
    required: true
  },
  componentVersion: {
    type: String,
    required: true
  },
  // Optional props to override default behavior
  customMinDate: {
    type: [String, Date],
    default: null
  },
  customMaxDate: {
    type: [String, Date],
    default: null
  },
  dateRangeMonths: {
    type: Number,
    default: 12 // How many months ahead to fetch unavailability data
  }
});

const emit = defineEmits(['date-range-selected', 'error', 'unavailability-loaded']);

// State
const unavailabilityData = ref(null);
const disabledDates = ref([]);
const loading = ref(false);
const error = ref(null);

// Configuration
const dateFormat = 'YYYY-MM-DD';

// Computed properties
const minDate = computed(() => {
  if (props.customMinDate) {
    return props.customMinDate;
  }

  // Default to today, but respect minimum booking notice
  const today = new Date();
  const minBookingNotice = unavailabilityData.value?.minimum_booking_notice || 0;

  if (minBookingNotice > 0) {
    const minDate = new Date(today);
    minDate.setDate(today.getDate() + minBookingNotice);
    return minDate.toISOString().split('T')[0];
  }

  return today.toISOString().split('T')[0];
});

const maxDate = computed(() => {
  if (props.customMaxDate) {
    return props.customMaxDate;
  }

  // Default to 2 years from now
  const maxDate = new Date();
  maxDate.setFullYear(maxDate.getFullYear() + 2);
  return maxDate.toISOString().split('T')[0];
});

const minNights = computed(() => {
  return unavailabilityData.value?.minimum_stay || 1;
});

const maxNights = computed(() => {
  // Optional: set a reasonable maximum, or leave unlimited (0)
  return 0; // 0 means no maximum limit
});

// Methods
const getAuthHeaders = () => {
  return {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Widget-Version': props.componentVersion,
    'Authorization': `Bearer ${props.authToken}`
  };
};

const fetchUnavailabilityData = async () => {
  if (!props.accommodation?.id) {
    return;
  }

  loading.value = true;
  error.value = null;

  try {
    // Calculate date range for fetching data
    const startDate = new Date();
    const endDate = new Date();
    endDate.setMonth(endDate.getMonth() + props.dateRangeMonths);

    const url = `${props.apiBaseUrl}/api/accommodations/${props.accommodation.id}/unavailability-data`;
    const params = new URLSearchParams({
      format: 'dates_array',
      start_date: startDate.toISOString().split('T')[0],
      end_date: endDate.toISOString().split('T')[0]
    });

    const response = await fetch(`${url}?${params}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch unavailability data: ${response.status}`);
    }

    const result = await response.json();

    // Store the full data for computed properties
    unavailabilityData.value = result.data;

    // Set disabled dates for the date picker
    disabledDates.value = result.data.unavailable_dates || [];

    emit('unavailability-loaded', result.data);
  } catch (err) {
    error.value = err.message;
    emit('error', { error: err.message, originalError: err });
  } finally {
    loading.value = false;
  }
};

const handleDateSelection = (dates) => {
  // Convert Unix timestamps to Date objects if they're not already
  const startDate = typeof dates.start === 'number' ? new Date(dates.start * 1000) : new Date(dates.start);
  const endDate = typeof dates.end === 'number' ? new Date(dates.end * 1000) : new Date(dates.end);

  const dateRange = {
    start: startDate,
    end: endDate
  };

  emit('date-range-selected', dateRange);
};

// Watchers
watch(() => props.accommodation?.id, (newId, oldId) => {
  if (newId && newId !== oldId) {
    fetchUnavailabilityData();
  }
});

// Lifecycle
onMounted(() => {
  if (props.accommodation?.id) {
    fetchUnavailabilityData();
  }
});
</script>

<style scoped>
.date-picker-container {
  position: relative;
}

.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  color: #6c757d;
  font-size: 14px;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e9ecef;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-state {
  padding: 20px;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  color: #721c24;
  text-align: center;
}

.error-state span {
  display: block;
  margin-bottom: 10px;
  font-size: 14px;
}

.retry-button {
  background: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background: #c82333;
}

.retry-button:active {
  transform: translateY(1px);
}

/* Ensure the date picker takes full width when loaded */
.date-picker-container :deep(.vue3-hotel-datepicker) {
  width: 100%;
}
</style>
