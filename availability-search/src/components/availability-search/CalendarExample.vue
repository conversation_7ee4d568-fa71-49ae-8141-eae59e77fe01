<template>
  <div class="calendar-example">
    <h3>Accommodation Calendar</h3>
    
    <!-- Display accommodation info -->
    <div v-if="accommodation" class="accommodation-info">
      <h4>{{ accommodation.name }}</h4>
      <p>Minimum Stay: {{ unavailabilityData?.minimum_stay || 1 }} nights</p>
      <p>Minimum Booking Notice: {{ unavailabilityData?.minimum_booking_notice || 0 }} days</p>
      <p v-if="unavailabilityData?.minimum_booking_notice_cutoff">
        Cannot book before: {{ unavailabilityData.minimum_booking_notice_cutoff }}
      </p>
    </div>

    <!-- Loading state -->
    <div v-if="loading" class="loading">
      Loading calendar data...
    </div>

    <!-- Error state -->
    <div v-if="error" class="error">
      {{ error }}
    </div>

    <!-- Calendar display (simplified example) -->
    <div v-if="unavailabilityData && !loading" class="calendar-data">
      <h5>Unavailability Summary:</h5>
      
      <!-- Unavailable Periods -->
      <div v-if="unavailabilityData.unavailable_periods?.length" class="unavailable-periods">
        <h6>Blocked Periods:</h6>
        <ul>
          <li v-for="period in unavailabilityData.unavailable_periods" :key="period.id">
            <span v-if="period.type === 'date_range'">
              {{ period.start_date }} to {{ period.end_date }}
            </span>
            <span v-else-if="period.type === 'recurring_days'">
              Recurring: {{ formatDaysOfWeek(period.days_of_week) }}
            </span>
            <span v-if="period.reason"> - {{ period.reason }}</span>
          </li>
        </ul>
      </div>

      <!-- Existing Bookings -->
      <div v-if="unavailabilityData.existing_bookings?.length" class="existing-bookings">
        <h6>Existing Bookings:</h6>
        <ul>
          <li v-for="booking in unavailabilityData.existing_bookings" :key="booking.id">
            {{ booking.start_date }} to {{ booking.end_date }} 
            ({{ booking.occupancy }} guests, Status: {{ booking.status?.name || 'Unknown' }})
          </li>
        </ul>
      </div>

      <!-- Simple date array format example -->
      <div class="format-toggle">
        <button @click="toggleFormat" class="toggle-button">
          {{ format === 'detailed' ? 'Show Simple Date List' : 'Show Detailed Data' }}
        </button>
      </div>

      <!-- Simple unavailable dates list -->
      <div v-if="format === 'dates_array' && unavailableDatesArray?.length" class="unavailable-dates-array">
        <h6>Unavailable Dates ({{ unavailableDatesArray.length }} dates):</h6>
        <div class="dates-grid">
          <span v-for="date in unavailableDatesArray.slice(0, 20)" :key="date" class="date-item">
            {{ date }}
          </span>
          <span v-if="unavailableDatesArray.length > 20" class="more-dates">
            ... and {{ unavailableDatesArray.length - 20 }} more dates
          </span>
        </div>
      </div>
    </div>

    <!-- Refresh button -->
    <button @click="fetchUnavailabilityData" :disabled="loading" class="refresh-button">
      {{ loading ? 'Loading...' : 'Refresh Calendar Data' }}
    </button>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';

const props = defineProps({
  accommodation: {
    type: Object,
    required: true
  },
  apiBaseUrl: {
    type: String,
    required: true
  },
  authToken: {
    type: String,
    required: true
  },
  componentVersion: {
    type: String,
    required: true
  }
});

const emit = defineEmits(['error']);

// State
const unavailabilityData = ref(null);
const unavailableDatesArray = ref([]);
const loading = ref(false);
const error = ref(null);
const format = ref('detailed');

// Computed
const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

// Methods
const getAuthHeaders = () => {
  return {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Widget-Version': props.componentVersion,
    'Authorization': `Bearer ${props.authToken}`
  };
};

const formatDaysOfWeek = (daysArray) => {
  if (!Array.isArray(daysArray)) return '';
  return daysArray.map(day => dayNames[day]).join(', ');
};

const fetchUnavailabilityData = async () => {
  if (!props.accommodation) {
    return;
  }

  loading.value = true;
  error.value = null;

  try {
    const url = `${props.apiBaseUrl}/api/accommodations/${props.accommodation.id}/unavailability-data`;
    const params = new URLSearchParams({
      format: format.value,
      // Optional: add date range filters for better performance
      start_date: new Date().toISOString().split('T')[0], // Today
      end_date: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // One year from now
    });

    const response = await fetch(`${url}?${params}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    const result = await response.json();
    
    if (format.value === 'dates_array') {
      unavailableDatesArray.value = result.data.unavailable_dates || [];
      unavailabilityData.value = null;
    } else {
      unavailabilityData.value = result.data;
      unavailableDatesArray.value = [];
    }
  } catch (err) {
    error.value = `Failed to fetch unavailability data: ${err.message}`;
    emit('error', { error: error.value, originalError: err });
  } finally {
    loading.value = false;
  }
};

const toggleFormat = async () => {
  format.value = format.value === 'detailed' ? 'dates_array' : 'detailed';
  await fetchUnavailabilityData();
};

// Lifecycle
onMounted(() => {
  fetchUnavailabilityData();
});
</script>

<style scoped>
.calendar-example {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.accommodation-info {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.accommodation-info h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.accommodation-info p {
  margin: 5px 0;
  color: #666;
}

.loading, .error {
  text-align: center;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
}

.loading {
  background: #e3f2fd;
  color: #1976d2;
}

.error {
  background: #ffebee;
  color: #c62828;
}

.calendar-data {
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
}

.unavailable-periods, .existing-bookings {
  margin: 15px 0;
}

.unavailable-periods h6, .existing-bookings h6 {
  color: #333;
  margin: 0 0 10px 0;
}

.unavailable-periods ul, .existing-bookings ul {
  list-style-type: none;
  padding: 0;
}

.unavailable-periods li, .existing-bookings li {
  background: #fff3e0;
  padding: 8px 12px;
  margin: 5px 0;
  border-left: 4px solid #ff9800;
  border-radius: 4px;
}

.format-toggle {
  margin: 20px 0;
  text-align: center;
}

.toggle-button {
  background: #2196f3;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.toggle-button:hover {
  background: #1976d2;
}

.unavailable-dates-array h6 {
  color: #333;
  margin: 0 0 15px 0;
}

.dates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 8px;
}

.date-item {
  background: #ffcdd2;
  padding: 6px 8px;
  border-radius: 4px;
  text-align: center;
  font-size: 12px;
  color: #c62828;
}

.more-dates {
  grid-column: 1 / -1;
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 10px;
}

.refresh-button {
  background: #4caf50;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  width: 100%;
  margin-top: 20px;
}

.refresh-button:hover:not(:disabled) {
  background: #45a049;
}

.refresh-button:disabled {
  background: #cccccc;
  cursor: not-allowed;
}
</style>
