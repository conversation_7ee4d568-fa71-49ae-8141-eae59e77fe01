<template>
  <div class="debug-container">
    <h3>DateRangePicker Debug Component</h3>
    
    <!-- Debug Information -->
    <div class="debug-info">
      <h4>Component State:</h4>
      <div class="debug-grid">
        <div class="debug-item">
          <strong>Loading:</strong> {{ loading }}
        </div>
        <div class="debug-item">
          <strong>Error:</strong> {{ error || 'None' }}
        </div>
        <div class="debug-item">
          <strong>Accommodation ID:</strong> {{ accommodation?.id || 'Not set' }}
        </div>
        <div class="debug-item">
          <strong>Min Date:</strong> {{ minDate || 'Not calculated' }}
        </div>
        <div class="debug-item">
          <strong>Max Date:</strong> {{ maxDate || 'Not calculated' }}
        </div>
        <div class="debug-item">
          <strong>Min Nights:</strong> {{ minNights }}
        </div>
        <div class="debug-item">
          <strong>Disabled Dates Count:</strong> {{ validatedDisabledDates.length }}
        </div>
      </div>
    </div>

    <!-- Raw Data Display -->
    <div class="debug-data">
      <details>
        <summary>Unavailability Data</summary>
        <pre>{{ JSON.stringify(unavailabilityData, null, 2) }}</pre>
      </details>
      
      <details>
        <summary>Disabled Dates (First 10)</summary>
        <pre>{{ JSON.stringify(validatedDisabledDates.slice(0, 10), null, 2) }}</pre>
      </details>
      
      <details>
        <summary>Selected Date Range</summary>
        <pre>{{ JSON.stringify(selectedDateRange, null, 2) }}</pre>
      </details>
    </div>

    <!-- Test Controls -->
    <div class="test-controls">
      <button @click="testFetchData" :disabled="loading">
        Test Fetch Data
      </button>
      <button @click="clearData">
        Clear Data
      </button>
      <button @click="testWithMockData">
        Test with Mock Data
      </button>
    </div>

    <!-- Date Picker Component -->
    <div class="picker-container">
      <h4>Date Picker:</h4>
      
      <!-- Show loading state -->
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <span>Loading available dates...</span>
      </div>
      
      <!-- Show error state -->
      <div v-if="error && !loading" class="error-state">
        <span>{{ error }}</span>
        <button @click="testFetchData" class="retry-button">Retry</button>
      </div>
      
      <!-- Show date picker when ready -->
      <Vue3HotelDatePicker
        v-if="!loading && accommodation?.id && minDate && maxDate"
        :disabled-dates="validatedDisabledDates"
        :min-date="minDate"
        :max-date="maxDate"
        :min-nights="minNights"
        :max-nights="maxNights"
        :format="dateFormat"
        @selected="handleDateSelection"
      />
      
      <!-- Show when not ready -->
      <div v-else-if="!loading" class="not-ready">
        <p>Date picker not ready. Missing:</p>
        <ul>
          <li v-if="!accommodation?.id">Accommodation ID</li>
          <li v-if="!minDate">Min Date</li>
          <li v-if="!maxDate">Max Date</li>
        </ul>
      </div>
    </div>

    <!-- Selected Date Display -->
    <div v-if="selectedDateRange" class="selected-dates">
      <h4>Selected Dates:</h4>
      <p><strong>Start:</strong> {{ formatDate(selectedDateRange.start) }}</p>
      <p><strong>End:</strong> {{ formatDate(selectedDateRange.end) }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import Vue3HotelDatePicker from "@sebarkar/vue3-hotel-datepicker";

const props = defineProps({
  accommodation: {
    type: Object,
    required: true
  },
  apiBaseUrl: {
    type: String,
    required: true
  },
  authToken: {
    type: String,
    required: true
  },
  componentVersion: {
    type: String,
    required: true
  }
});

const emit = defineEmits(['date-range-selected', 'error']);

// State
const unavailabilityData = ref(null);
const disabledDates = ref([]);
const loading = ref(false);
const error = ref(null);
const selectedDateRange = ref(null);

// Configuration
const dateFormat = 'YYYY-MM-DD';

// Helper functions
const formatDateForPicker = (date) => {
  if (!date) return null;
  
  try {
    let dateObj;
    if (typeof date === 'string') {
      dateObj = new Date(date);
    } else if (date instanceof Date) {
      dateObj = date;
    } else {
      return null;
    }
    
    if (isNaN(dateObj.getTime())) {
      return null;
    }
    
    return dateObj.toISOString().split('T')[0];
  } catch (error) {
    console.warn('Invalid date provided to formatDateForPicker:', date);
    return null;
  }
};

const formatDate = (date) => {
  if (!date) return 'Invalid Date';
  try {
    return new Date(date).toLocaleDateString();
  } catch {
    return 'Invalid Date';
  }
};

// Computed properties
const minDate = computed(() => {
  const today = new Date();
  const minBookingNotice = unavailabilityData.value?.minimum_booking_notice || 0;
  
  if (minBookingNotice > 0) {
    const minDate = new Date(today);
    minDate.setDate(today.getDate() + minBookingNotice);
    return formatDateForPicker(minDate);
  }
  
  return formatDateForPicker(today);
});

const maxDate = computed(() => {
  const maxDate = new Date();
  maxDate.setFullYear(maxDate.getFullYear() + 2);
  return formatDateForPicker(maxDate);
});

const minNights = computed(() => {
  return unavailabilityData.value?.minimum_stay || 1;
});

const validatedDisabledDates = computed(() => {
  if (!disabledDates.value || !Array.isArray(disabledDates.value)) {
    return [];
  }
  
  return disabledDates.value
    .map(date => formatDateForPicker(date))
    .filter(date => date !== null);
});

// Methods
const getAuthHeaders = () => {
  return {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Widget-Version': props.componentVersion,
    'Authorization': `Bearer ${props.authToken}`
  };
};

const testFetchData = async () => {
  if (!props.accommodation?.id) {
    error.value = 'No accommodation ID provided';
    return;
  }

  loading.value = true;
  error.value = null;

  try {
    const startDate = new Date();
    const endDate = new Date();
    endDate.setMonth(endDate.getMonth() + 12);

    const url = `${props.apiBaseUrl}/api/accommodations/${props.accommodation.id}/unavailability-data`;
    const params = new URLSearchParams({
      format: 'dates_array',
      start_date: startDate.toISOString().split('T')[0],
      end_date: endDate.toISOString().split('T')[0]
    });

    console.log('Fetching from URL:', `${url}?${params}`);

    const response = await fetch(`${url}?${params}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    console.log('Response status:', response.status);

    if (!response.ok) {
      throw new Error(`Failed to fetch unavailability data: ${response.status}`);
    }

    const result = await response.json();
    console.log('API Response:', result);
    
    if (!result.data) {
      throw new Error('Invalid API response: missing data property');
    }
    
    unavailabilityData.value = result.data;
    
    const unavailableDates = result.data.unavailable_dates;
    if (Array.isArray(unavailableDates)) {
      disabledDates.value = unavailableDates;
      console.log('Set disabled dates:', unavailableDates.length, 'dates');
    } else {
      console.warn('API returned non-array unavailable_dates:', unavailableDates);
      disabledDates.value = [];
    }
    
  } catch (err) {
    console.error('Fetch error:', err);
    error.value = err.message;
  } finally {
    loading.value = false;
  }
};

const clearData = () => {
  unavailabilityData.value = null;
  disabledDates.value = [];
  selectedDateRange.value = null;
  error.value = null;
};

const testWithMockData = () => {
  unavailabilityData.value = {
    accommodation_id: props.accommodation?.id || 1,
    minimum_stay: 2,
    minimum_booking_notice: 1,
    minimum_booking_notice_cutoff: "2024-01-02",
    unavailable_dates: [
      "2024-01-01",
      "2024-01-15",
      "2024-01-16",
      "2024-02-14"
    ]
  };
  
  disabledDates.value = unavailabilityData.value.unavailable_dates;
  error.value = null;
};

const handleDateSelection = (dates) => {
  console.log('Date selection received:', dates);
  
  try {
    let startDate, endDate;
    
    if (typeof dates.start === 'number') {
      const startTimestamp = dates.start.toString().length === 10 ? dates.start * 1000 : dates.start;
      startDate = new Date(startTimestamp);
    } else {
      startDate = new Date(dates.start);
    }
    
    if (typeof dates.end === 'number') {
      const endTimestamp = dates.end.toString().length === 10 ? dates.end * 1000 : dates.end;
      endDate = new Date(endTimestamp);
    } else {
      endDate = new Date(dates.end);
    }
    
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      console.error('Invalid dates received:', dates);
      return;
    }

    selectedDateRange.value = { start: startDate, end: endDate };
    emit('date-range-selected', selectedDateRange.value);
  } catch (error) {
    console.error('Error processing date selection:', error);
    emit('error', { error: 'Invalid date selection', originalError: error });
  }
};

// Watchers
watch(() => props.accommodation?.id, (newId) => {
  if (newId) {
    testFetchData();
  }
});

// Lifecycle
onMounted(() => {
  if (props.accommodation?.id) {
    testFetchData();
  }
});
</script>

<style scoped>
.debug-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.debug-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin: 20px 0;
}

.debug-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 10px;
  margin-top: 10px;
}

.debug-item {
  padding: 8px;
  background: white;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.debug-data {
  margin: 20px 0;
}

.debug-data details {
  margin: 10px 0;
  background: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
}

.debug-data pre {
  background: white;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}

.test-controls {
  display: flex;
  gap: 10px;
  margin: 20px 0;
}

.test-controls button {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  background: #007bff;
  color: white;
}

.test-controls button:hover:not(:disabled) {
  background: #0056b3;
}

.test-controls button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.picker-container {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
}

.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #6c757d;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e9ecef;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-state {
  padding: 20px;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  color: #721c24;
  text-align: center;
}

.retry-button {
  background: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
}

.not-ready {
  padding: 20px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  color: #856404;
}

.selected-dates {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 8px;
  padding: 15px;
  margin: 20px 0;
}
</style>
