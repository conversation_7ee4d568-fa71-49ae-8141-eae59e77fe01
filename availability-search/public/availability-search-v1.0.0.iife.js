var AvailabilitySearch = function(exports) {
  "use strict";
  /**
  * @vue/shared v3.5.13
  * (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
  * @license MIT
  **/
  /*! #__NO_SIDE_EFFECTS__ */
  // @__NO_SIDE_EFFECTS__
  function makeMap(str) {
    const map2 = /* @__PURE__ */ Object.create(null);
    for (const key of str.split(",")) map2[key] = 1;
    return (val) => val in map2;
  }
  const EMPTY_OBJ = Object.freeze({});
  const EMPTY_ARR = Object.freeze([]);
  const NOOP = () => {
  };
  const NO = () => false;
  const isOn = (key) => key.charCodeAt(0) === 111 && key.charCodeAt(1) === 110 && // uppercase letter
  (key.charCodeAt(2) > 122 || key.charCodeAt(2) < 97);
  const isModelListener = (key) => key.startsWith("onUpdate:");
  const extend = Object.assign;
  const remove = (arr, el) => {
    const i = arr.indexOf(el);
    if (i > -1) {
      arr.splice(i, 1);
    }
  };
  const hasOwnProperty$1 = Object.prototype.hasOwnProperty;
  const hasOwn = (val, key) => hasOwnProperty$1.call(val, key);
  const isArray = Array.isArray;
  const isMap = (val) => toTypeString(val) === "[object Map]";
  const isSet = (val) => toTypeString(val) === "[object Set]";
  const isDate = (val) => toTypeString(val) === "[object Date]";
  const isFunction = (val) => typeof val === "function";
  const isString = (val) => typeof val === "string";
  const isSymbol = (val) => typeof val === "symbol";
  const isObject = (val) => val !== null && typeof val === "object";
  const isPromise = (val) => {
    return (isObject(val) || isFunction(val)) && isFunction(val.then) && isFunction(val.catch);
  };
  const objectToString = Object.prototype.toString;
  const toTypeString = (value) => objectToString.call(value);
  const toRawType = (value) => {
    return toTypeString(value).slice(8, -1);
  };
  const isPlainObject = (val) => toTypeString(val) === "[object Object]";
  const isIntegerKey = (key) => isString(key) && key !== "NaN" && key[0] !== "-" && "" + parseInt(key, 10) === key;
  const isReservedProp = /* @__PURE__ */ makeMap(
    // the leading comma is intentional so empty string "" is also included
    ",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"
  );
  const isBuiltInDirective = /* @__PURE__ */ makeMap(
    "bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo"
  );
  const cacheStringFunction = (fn) => {
    const cache = /* @__PURE__ */ Object.create(null);
    return (str) => {
      const hit = cache[str];
      return hit || (cache[str] = fn(str));
    };
  };
  const camelizeRE = /-(\w)/g;
  const camelize = cacheStringFunction(
    (str) => {
      return str.replace(camelizeRE, (_, c) => c ? c.toUpperCase() : "");
    }
  );
  const hyphenateRE = /\B([A-Z])/g;
  const hyphenate = cacheStringFunction(
    (str) => str.replace(hyphenateRE, "-$1").toLowerCase()
  );
  const capitalize = cacheStringFunction((str) => {
    return str.charAt(0).toUpperCase() + str.slice(1);
  });
  const toHandlerKey = cacheStringFunction(
    (str) => {
      const s = str ? `on${capitalize(str)}` : ``;
      return s;
    }
  );
  const hasChanged = (value, oldValue) => !Object.is(value, oldValue);
  const invokeArrayFns = (fns, ...arg) => {
    for (let i = 0; i < fns.length; i++) {
      fns[i](...arg);
    }
  };
  const def = (obj, key, value, writable = false) => {
    Object.defineProperty(obj, key, {
      configurable: true,
      enumerable: false,
      writable,
      value
    });
  };
  const looseToNumber = (val) => {
    const n = parseFloat(val);
    return isNaN(n) ? val : n;
  };
  const toNumber = (val) => {
    const n = isString(val) ? Number(val) : NaN;
    return isNaN(n) ? val : n;
  };
  let _globalThis;
  const getGlobalThis = () => {
    return _globalThis || (_globalThis = typeof globalThis !== "undefined" ? globalThis : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : typeof global !== "undefined" ? global : {});
  };
  function normalizeStyle(value) {
    if (isArray(value)) {
      const res = {};
      for (let i = 0; i < value.length; i++) {
        const item = value[i];
        const normalized = isString(item) ? parseStringStyle(item) : normalizeStyle(item);
        if (normalized) {
          for (const key in normalized) {
            res[key] = normalized[key];
          }
        }
      }
      return res;
    } else if (isString(value) || isObject(value)) {
      return value;
    }
  }
  const listDelimiterRE = /;(?![^(]*\))/g;
  const propertyDelimiterRE = /:([^]+)/;
  const styleCommentRE = /\/\*[^]*?\*\//g;
  function parseStringStyle(cssText) {
    const ret = {};
    cssText.replace(styleCommentRE, "").split(listDelimiterRE).forEach((item) => {
      if (item) {
        const tmp = item.split(propertyDelimiterRE);
        tmp.length > 1 && (ret[tmp[0].trim()] = tmp[1].trim());
      }
    });
    return ret;
  }
  function normalizeClass(value) {
    let res = "";
    if (isString(value)) {
      res = value;
    } else if (isArray(value)) {
      for (let i = 0; i < value.length; i++) {
        const normalized = normalizeClass(value[i]);
        if (normalized) {
          res += normalized + " ";
        }
      }
    } else if (isObject(value)) {
      for (const name in value) {
        if (value[name]) {
          res += name + " ";
        }
      }
    }
    return res.trim();
  }
  const HTML_TAGS = "html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot";
  const SVG_TAGS = "svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistantLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view";
  const MATH_TAGS = "annotation,annotation-xml,maction,maligngroup,malignmark,math,menclose,merror,mfenced,mfrac,mfraction,mglyph,mi,mlabeledtr,mlongdiv,mmultiscripts,mn,mo,mover,mpadded,mphantom,mprescripts,mroot,mrow,ms,mscarries,mscarry,msgroup,msline,mspace,msqrt,msrow,mstack,mstyle,msub,msubsup,msup,mtable,mtd,mtext,mtr,munder,munderover,none,semantics";
  const isHTMLTag = /* @__PURE__ */ makeMap(HTML_TAGS);
  const isSVGTag = /* @__PURE__ */ makeMap(SVG_TAGS);
  const isMathMLTag = /* @__PURE__ */ makeMap(MATH_TAGS);
  const specialBooleanAttrs = `itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly`;
  const isSpecialBooleanAttr = /* @__PURE__ */ makeMap(specialBooleanAttrs);
  function includeBooleanAttr(value) {
    return !!value || value === "";
  }
  function looseCompareArrays(a, b) {
    if (a.length !== b.length) return false;
    let equal = true;
    for (let i = 0; equal && i < a.length; i++) {
      equal = looseEqual(a[i], b[i]);
    }
    return equal;
  }
  function looseEqual(a, b) {
    if (a === b) return true;
    let aValidType = isDate(a);
    let bValidType = isDate(b);
    if (aValidType || bValidType) {
      return aValidType && bValidType ? a.getTime() === b.getTime() : false;
    }
    aValidType = isSymbol(a);
    bValidType = isSymbol(b);
    if (aValidType || bValidType) {
      return a === b;
    }
    aValidType = isArray(a);
    bValidType = isArray(b);
    if (aValidType || bValidType) {
      return aValidType && bValidType ? looseCompareArrays(a, b) : false;
    }
    aValidType = isObject(a);
    bValidType = isObject(b);
    if (aValidType || bValidType) {
      if (!aValidType || !bValidType) {
        return false;
      }
      const aKeysCount = Object.keys(a).length;
      const bKeysCount = Object.keys(b).length;
      if (aKeysCount !== bKeysCount) {
        return false;
      }
      for (const key in a) {
        const aHasKey = a.hasOwnProperty(key);
        const bHasKey = b.hasOwnProperty(key);
        if (aHasKey && !bHasKey || !aHasKey && bHasKey || !looseEqual(a[key], b[key])) {
          return false;
        }
      }
    }
    return String(a) === String(b);
  }
  function looseIndexOf(arr, val) {
    return arr.findIndex((item) => looseEqual(item, val));
  }
  const isRef$1 = (val) => {
    return !!(val && val["__v_isRef"] === true);
  };
  const toDisplayString = (val) => {
    return isString(val) ? val : val == null ? "" : isArray(val) || isObject(val) && (val.toString === objectToString || !isFunction(val.toString)) ? isRef$1(val) ? toDisplayString(val.value) : JSON.stringify(val, replacer, 2) : String(val);
  };
  const replacer = (_key, val) => {
    if (isRef$1(val)) {
      return replacer(_key, val.value);
    } else if (isMap(val)) {
      return {
        [`Map(${val.size})`]: [...val.entries()].reduce(
          (entries, [key, val2], i) => {
            entries[stringifySymbol(key, i) + " =>"] = val2;
            return entries;
          },
          {}
        )
      };
    } else if (isSet(val)) {
      return {
        [`Set(${val.size})`]: [...val.values()].map((v) => stringifySymbol(v))
      };
    } else if (isSymbol(val)) {
      return stringifySymbol(val);
    } else if (isObject(val) && !isArray(val) && !isPlainObject(val)) {
      return String(val);
    }
    return val;
  };
  const stringifySymbol = (v, i = "") => {
    var _a;
    return (
      // Symbol.description in es2019+ so we need to cast here to pass
      // the lib: es2016 check
      isSymbol(v) ? `Symbol(${(_a = v.description) != null ? _a : i})` : v
    );
  };
  /**
  * @vue/reactivity v3.5.13
  * (c) 2018-present Yuxi (Evan) You and Vue contributors
  * @license MIT
  **/
  function warn$2(msg, ...args) {
    console.warn(`[Vue warn] ${msg}`, ...args);
  }
  let activeEffectScope;
  class EffectScope {
    constructor(detached = false) {
      this.detached = detached;
      this._active = true;
      this.effects = [];
      this.cleanups = [];
      this._isPaused = false;
      this.parent = activeEffectScope;
      if (!detached && activeEffectScope) {
        this.index = (activeEffectScope.scopes || (activeEffectScope.scopes = [])).push(
          this
        ) - 1;
      }
    }
    get active() {
      return this._active;
    }
    pause() {
      if (this._active) {
        this._isPaused = true;
        let i, l;
        if (this.scopes) {
          for (i = 0, l = this.scopes.length; i < l; i++) {
            this.scopes[i].pause();
          }
        }
        for (i = 0, l = this.effects.length; i < l; i++) {
          this.effects[i].pause();
        }
      }
    }
    /**
     * Resumes the effect scope, including all child scopes and effects.
     */
    resume() {
      if (this._active) {
        if (this._isPaused) {
          this._isPaused = false;
          let i, l;
          if (this.scopes) {
            for (i = 0, l = this.scopes.length; i < l; i++) {
              this.scopes[i].resume();
            }
          }
          for (i = 0, l = this.effects.length; i < l; i++) {
            this.effects[i].resume();
          }
        }
      }
    }
    run(fn) {
      if (this._active) {
        const currentEffectScope = activeEffectScope;
        try {
          activeEffectScope = this;
          return fn();
        } finally {
          activeEffectScope = currentEffectScope;
        }
      } else {
        warn$2(`cannot run an inactive effect scope.`);
      }
    }
    /**
     * This should only be called on non-detached scopes
     * @internal
     */
    on() {
      activeEffectScope = this;
    }
    /**
     * This should only be called on non-detached scopes
     * @internal
     */
    off() {
      activeEffectScope = this.parent;
    }
    stop(fromParent) {
      if (this._active) {
        this._active = false;
        let i, l;
        for (i = 0, l = this.effects.length; i < l; i++) {
          this.effects[i].stop();
        }
        this.effects.length = 0;
        for (i = 0, l = this.cleanups.length; i < l; i++) {
          this.cleanups[i]();
        }
        this.cleanups.length = 0;
        if (this.scopes) {
          for (i = 0, l = this.scopes.length; i < l; i++) {
            this.scopes[i].stop(true);
          }
          this.scopes.length = 0;
        }
        if (!this.detached && this.parent && !fromParent) {
          const last = this.parent.scopes.pop();
          if (last && last !== this) {
            this.parent.scopes[this.index] = last;
            last.index = this.index;
          }
        }
        this.parent = void 0;
      }
    }
  }
  function getCurrentScope() {
    return activeEffectScope;
  }
  let activeSub;
  const pausedQueueEffects = /* @__PURE__ */ new WeakSet();
  class ReactiveEffect {
    constructor(fn) {
      this.fn = fn;
      this.deps = void 0;
      this.depsTail = void 0;
      this.flags = 1 | 4;
      this.next = void 0;
      this.cleanup = void 0;
      this.scheduler = void 0;
      if (activeEffectScope && activeEffectScope.active) {
        activeEffectScope.effects.push(this);
      }
    }
    pause() {
      this.flags |= 64;
    }
    resume() {
      if (this.flags & 64) {
        this.flags &= -65;
        if (pausedQueueEffects.has(this)) {
          pausedQueueEffects.delete(this);
          this.trigger();
        }
      }
    }
    /**
     * @internal
     */
    notify() {
      if (this.flags & 2 && !(this.flags & 32)) {
        return;
      }
      if (!(this.flags & 8)) {
        batch(this);
      }
    }
    run() {
      if (!(this.flags & 1)) {
        return this.fn();
      }
      this.flags |= 2;
      cleanupEffect(this);
      prepareDeps(this);
      const prevEffect = activeSub;
      const prevShouldTrack = shouldTrack;
      activeSub = this;
      shouldTrack = true;
      try {
        return this.fn();
      } finally {
        if (activeSub !== this) {
          warn$2(
            "Active effect was not restored correctly - this is likely a Vue internal bug."
          );
        }
        cleanupDeps(this);
        activeSub = prevEffect;
        shouldTrack = prevShouldTrack;
        this.flags &= -3;
      }
    }
    stop() {
      if (this.flags & 1) {
        for (let link = this.deps; link; link = link.nextDep) {
          removeSub(link);
        }
        this.deps = this.depsTail = void 0;
        cleanupEffect(this);
        this.onStop && this.onStop();
        this.flags &= -2;
      }
    }
    trigger() {
      if (this.flags & 64) {
        pausedQueueEffects.add(this);
      } else if (this.scheduler) {
        this.scheduler();
      } else {
        this.runIfDirty();
      }
    }
    /**
     * @internal
     */
    runIfDirty() {
      if (isDirty(this)) {
        this.run();
      }
    }
    get dirty() {
      return isDirty(this);
    }
  }
  let batchDepth = 0;
  let batchedSub;
  let batchedComputed;
  function batch(sub, isComputed = false) {
    sub.flags |= 8;
    if (isComputed) {
      sub.next = batchedComputed;
      batchedComputed = sub;
      return;
    }
    sub.next = batchedSub;
    batchedSub = sub;
  }
  function startBatch() {
    batchDepth++;
  }
  function endBatch() {
    if (--batchDepth > 0) {
      return;
    }
    if (batchedComputed) {
      let e = batchedComputed;
      batchedComputed = void 0;
      while (e) {
        const next = e.next;
        e.next = void 0;
        e.flags &= -9;
        e = next;
      }
    }
    let error;
    while (batchedSub) {
      let e = batchedSub;
      batchedSub = void 0;
      while (e) {
        const next = e.next;
        e.next = void 0;
        e.flags &= -9;
        if (e.flags & 1) {
          try {
            ;
            e.trigger();
          } catch (err) {
            if (!error) error = err;
          }
        }
        e = next;
      }
    }
    if (error) throw error;
  }
  function prepareDeps(sub) {
    for (let link = sub.deps; link; link = link.nextDep) {
      link.version = -1;
      link.prevActiveLink = link.dep.activeLink;
      link.dep.activeLink = link;
    }
  }
  function cleanupDeps(sub) {
    let head;
    let tail = sub.depsTail;
    let link = tail;
    while (link) {
      const prev = link.prevDep;
      if (link.version === -1) {
        if (link === tail) tail = prev;
        removeSub(link);
        removeDep(link);
      } else {
        head = link;
      }
      link.dep.activeLink = link.prevActiveLink;
      link.prevActiveLink = void 0;
      link = prev;
    }
    sub.deps = head;
    sub.depsTail = tail;
  }
  function isDirty(sub) {
    for (let link = sub.deps; link; link = link.nextDep) {
      if (link.dep.version !== link.version || link.dep.computed && (refreshComputed(link.dep.computed) || link.dep.version !== link.version)) {
        return true;
      }
    }
    if (sub._dirty) {
      return true;
    }
    return false;
  }
  function refreshComputed(computed2) {
    if (computed2.flags & 4 && !(computed2.flags & 16)) {
      return;
    }
    computed2.flags &= -17;
    if (computed2.globalVersion === globalVersion) {
      return;
    }
    computed2.globalVersion = globalVersion;
    const dep = computed2.dep;
    computed2.flags |= 2;
    if (dep.version > 0 && !computed2.isSSR && computed2.deps && !isDirty(computed2)) {
      computed2.flags &= -3;
      return;
    }
    const prevSub = activeSub;
    const prevShouldTrack = shouldTrack;
    activeSub = computed2;
    shouldTrack = true;
    try {
      prepareDeps(computed2);
      const value = computed2.fn(computed2._value);
      if (dep.version === 0 || hasChanged(value, computed2._value)) {
        computed2._value = value;
        dep.version++;
      }
    } catch (err) {
      dep.version++;
      throw err;
    } finally {
      activeSub = prevSub;
      shouldTrack = prevShouldTrack;
      cleanupDeps(computed2);
      computed2.flags &= -3;
    }
  }
  function removeSub(link, soft = false) {
    const { dep, prevSub, nextSub } = link;
    if (prevSub) {
      prevSub.nextSub = nextSub;
      link.prevSub = void 0;
    }
    if (nextSub) {
      nextSub.prevSub = prevSub;
      link.nextSub = void 0;
    }
    if (dep.subsHead === link) {
      dep.subsHead = nextSub;
    }
    if (dep.subs === link) {
      dep.subs = prevSub;
      if (!prevSub && dep.computed) {
        dep.computed.flags &= -5;
        for (let l = dep.computed.deps; l; l = l.nextDep) {
          removeSub(l, true);
        }
      }
    }
    if (!soft && !--dep.sc && dep.map) {
      dep.map.delete(dep.key);
    }
  }
  function removeDep(link) {
    const { prevDep, nextDep } = link;
    if (prevDep) {
      prevDep.nextDep = nextDep;
      link.prevDep = void 0;
    }
    if (nextDep) {
      nextDep.prevDep = prevDep;
      link.nextDep = void 0;
    }
  }
  let shouldTrack = true;
  const trackStack = [];
  function pauseTracking() {
    trackStack.push(shouldTrack);
    shouldTrack = false;
  }
  function resetTracking() {
    const last = trackStack.pop();
    shouldTrack = last === void 0 ? true : last;
  }
  function cleanupEffect(e) {
    const { cleanup } = e;
    e.cleanup = void 0;
    if (cleanup) {
      const prevSub = activeSub;
      activeSub = void 0;
      try {
        cleanup();
      } finally {
        activeSub = prevSub;
      }
    }
  }
  let globalVersion = 0;
  class Link {
    constructor(sub, dep) {
      this.sub = sub;
      this.dep = dep;
      this.version = dep.version;
      this.nextDep = this.prevDep = this.nextSub = this.prevSub = this.prevActiveLink = void 0;
    }
  }
  class Dep {
    constructor(computed2) {
      this.computed = computed2;
      this.version = 0;
      this.activeLink = void 0;
      this.subs = void 0;
      this.map = void 0;
      this.key = void 0;
      this.sc = 0;
      {
        this.subsHead = void 0;
      }
    }
    track(debugInfo) {
      if (!activeSub || !shouldTrack || activeSub === this.computed) {
        return;
      }
      let link = this.activeLink;
      if (link === void 0 || link.sub !== activeSub) {
        link = this.activeLink = new Link(activeSub, this);
        if (!activeSub.deps) {
          activeSub.deps = activeSub.depsTail = link;
        } else {
          link.prevDep = activeSub.depsTail;
          activeSub.depsTail.nextDep = link;
          activeSub.depsTail = link;
        }
        addSub(link);
      } else if (link.version === -1) {
        link.version = this.version;
        if (link.nextDep) {
          const next = link.nextDep;
          next.prevDep = link.prevDep;
          if (link.prevDep) {
            link.prevDep.nextDep = next;
          }
          link.prevDep = activeSub.depsTail;
          link.nextDep = void 0;
          activeSub.depsTail.nextDep = link;
          activeSub.depsTail = link;
          if (activeSub.deps === link) {
            activeSub.deps = next;
          }
        }
      }
      if (activeSub.onTrack) {
        activeSub.onTrack(
          extend(
            {
              effect: activeSub
            },
            debugInfo
          )
        );
      }
      return link;
    }
    trigger(debugInfo) {
      this.version++;
      globalVersion++;
      this.notify(debugInfo);
    }
    notify(debugInfo) {
      startBatch();
      try {
        if (true) {
          for (let head = this.subsHead; head; head = head.nextSub) {
            if (head.sub.onTrigger && !(head.sub.flags & 8)) {
              head.sub.onTrigger(
                extend(
                  {
                    effect: head.sub
                  },
                  debugInfo
                )
              );
            }
          }
        }
        for (let link = this.subs; link; link = link.prevSub) {
          if (link.sub.notify()) {
            ;
            link.sub.dep.notify();
          }
        }
      } finally {
        endBatch();
      }
    }
  }
  function addSub(link) {
    link.dep.sc++;
    if (link.sub.flags & 4) {
      const computed2 = link.dep.computed;
      if (computed2 && !link.dep.subs) {
        computed2.flags |= 4 | 16;
        for (let l = computed2.deps; l; l = l.nextDep) {
          addSub(l);
        }
      }
      const currentTail = link.dep.subs;
      if (currentTail !== link) {
        link.prevSub = currentTail;
        if (currentTail) currentTail.nextSub = link;
      }
      if (link.dep.subsHead === void 0) {
        link.dep.subsHead = link;
      }
      link.dep.subs = link;
    }
  }
  const targetMap = /* @__PURE__ */ new WeakMap();
  const ITERATE_KEY = Symbol(
    "Object iterate"
  );
  const MAP_KEY_ITERATE_KEY = Symbol(
    "Map keys iterate"
  );
  const ARRAY_ITERATE_KEY = Symbol(
    "Array iterate"
  );
  function track(target, type, key) {
    if (shouldTrack && activeSub) {
      let depsMap = targetMap.get(target);
      if (!depsMap) {
        targetMap.set(target, depsMap = /* @__PURE__ */ new Map());
      }
      let dep = depsMap.get(key);
      if (!dep) {
        depsMap.set(key, dep = new Dep());
        dep.map = depsMap;
        dep.key = key;
      }
      {
        dep.track({
          target,
          type,
          key
        });
      }
    }
  }
  function trigger(target, type, key, newValue, oldValue, oldTarget) {
    const depsMap = targetMap.get(target);
    if (!depsMap) {
      globalVersion++;
      return;
    }
    const run = (dep) => {
      if (dep) {
        {
          dep.trigger({
            target,
            type,
            key,
            newValue,
            oldValue,
            oldTarget
          });
        }
      }
    };
    startBatch();
    if (type === "clear") {
      depsMap.forEach(run);
    } else {
      const targetIsArray = isArray(target);
      const isArrayIndex = targetIsArray && isIntegerKey(key);
      if (targetIsArray && key === "length") {
        const newLength = Number(newValue);
        depsMap.forEach((dep, key2) => {
          if (key2 === "length" || key2 === ARRAY_ITERATE_KEY || !isSymbol(key2) && key2 >= newLength) {
            run(dep);
          }
        });
      } else {
        if (key !== void 0 || depsMap.has(void 0)) {
          run(depsMap.get(key));
        }
        if (isArrayIndex) {
          run(depsMap.get(ARRAY_ITERATE_KEY));
        }
        switch (type) {
          case "add":
            if (!targetIsArray) {
              run(depsMap.get(ITERATE_KEY));
              if (isMap(target)) {
                run(depsMap.get(MAP_KEY_ITERATE_KEY));
              }
            } else if (isArrayIndex) {
              run(depsMap.get("length"));
            }
            break;
          case "delete":
            if (!targetIsArray) {
              run(depsMap.get(ITERATE_KEY));
              if (isMap(target)) {
                run(depsMap.get(MAP_KEY_ITERATE_KEY));
              }
            }
            break;
          case "set":
            if (isMap(target)) {
              run(depsMap.get(ITERATE_KEY));
            }
            break;
        }
      }
    }
    endBatch();
  }
  function reactiveReadArray(array) {
    const raw = toRaw(array);
    if (raw === array) return raw;
    track(raw, "iterate", ARRAY_ITERATE_KEY);
    return isShallow(array) ? raw : raw.map(toReactive);
  }
  function shallowReadArray(arr) {
    track(arr = toRaw(arr), "iterate", ARRAY_ITERATE_KEY);
    return arr;
  }
  const arrayInstrumentations = {
    __proto__: null,
    [Symbol.iterator]() {
      return iterator(this, Symbol.iterator, toReactive);
    },
    concat(...args) {
      return reactiveReadArray(this).concat(
        ...args.map((x) => isArray(x) ? reactiveReadArray(x) : x)
      );
    },
    entries() {
      return iterator(this, "entries", (value) => {
        value[1] = toReactive(value[1]);
        return value;
      });
    },
    every(fn, thisArg) {
      return apply(this, "every", fn, thisArg, void 0, arguments);
    },
    filter(fn, thisArg) {
      return apply(this, "filter", fn, thisArg, (v) => v.map(toReactive), arguments);
    },
    find(fn, thisArg) {
      return apply(this, "find", fn, thisArg, toReactive, arguments);
    },
    findIndex(fn, thisArg) {
      return apply(this, "findIndex", fn, thisArg, void 0, arguments);
    },
    findLast(fn, thisArg) {
      return apply(this, "findLast", fn, thisArg, toReactive, arguments);
    },
    findLastIndex(fn, thisArg) {
      return apply(this, "findLastIndex", fn, thisArg, void 0, arguments);
    },
    // flat, flatMap could benefit from ARRAY_ITERATE but are not straight-forward to implement
    forEach(fn, thisArg) {
      return apply(this, "forEach", fn, thisArg, void 0, arguments);
    },
    includes(...args) {
      return searchProxy(this, "includes", args);
    },
    indexOf(...args) {
      return searchProxy(this, "indexOf", args);
    },
    join(separator) {
      return reactiveReadArray(this).join(separator);
    },
    // keys() iterator only reads `length`, no optimisation required
    lastIndexOf(...args) {
      return searchProxy(this, "lastIndexOf", args);
    },
    map(fn, thisArg) {
      return apply(this, "map", fn, thisArg, void 0, arguments);
    },
    pop() {
      return noTracking(this, "pop");
    },
    push(...args) {
      return noTracking(this, "push", args);
    },
    reduce(fn, ...args) {
      return reduce(this, "reduce", fn, args);
    },
    reduceRight(fn, ...args) {
      return reduce(this, "reduceRight", fn, args);
    },
    shift() {
      return noTracking(this, "shift");
    },
    // slice could use ARRAY_ITERATE but also seems to beg for range tracking
    some(fn, thisArg) {
      return apply(this, "some", fn, thisArg, void 0, arguments);
    },
    splice(...args) {
      return noTracking(this, "splice", args);
    },
    toReversed() {
      return reactiveReadArray(this).toReversed();
    },
    toSorted(comparer) {
      return reactiveReadArray(this).toSorted(comparer);
    },
    toSpliced(...args) {
      return reactiveReadArray(this).toSpliced(...args);
    },
    unshift(...args) {
      return noTracking(this, "unshift", args);
    },
    values() {
      return iterator(this, "values", toReactive);
    }
  };
  function iterator(self2, method, wrapValue) {
    const arr = shallowReadArray(self2);
    const iter = arr[method]();
    if (arr !== self2 && !isShallow(self2)) {
      iter._next = iter.next;
      iter.next = () => {
        const result = iter._next();
        if (result.value) {
          result.value = wrapValue(result.value);
        }
        return result;
      };
    }
    return iter;
  }
  const arrayProto = Array.prototype;
  function apply(self2, method, fn, thisArg, wrappedRetFn, args) {
    const arr = shallowReadArray(self2);
    const needsWrap = arr !== self2 && !isShallow(self2);
    const methodFn = arr[method];
    if (methodFn !== arrayProto[method]) {
      const result2 = methodFn.apply(self2, args);
      return needsWrap ? toReactive(result2) : result2;
    }
    let wrappedFn = fn;
    if (arr !== self2) {
      if (needsWrap) {
        wrappedFn = function(item, index) {
          return fn.call(this, toReactive(item), index, self2);
        };
      } else if (fn.length > 2) {
        wrappedFn = function(item, index) {
          return fn.call(this, item, index, self2);
        };
      }
    }
    const result = methodFn.call(arr, wrappedFn, thisArg);
    return needsWrap && wrappedRetFn ? wrappedRetFn(result) : result;
  }
  function reduce(self2, method, fn, args) {
    const arr = shallowReadArray(self2);
    let wrappedFn = fn;
    if (arr !== self2) {
      if (!isShallow(self2)) {
        wrappedFn = function(acc, item, index) {
          return fn.call(this, acc, toReactive(item), index, self2);
        };
      } else if (fn.length > 3) {
        wrappedFn = function(acc, item, index) {
          return fn.call(this, acc, item, index, self2);
        };
      }
    }
    return arr[method](wrappedFn, ...args);
  }
  function searchProxy(self2, method, args) {
    const arr = toRaw(self2);
    track(arr, "iterate", ARRAY_ITERATE_KEY);
    const res = arr[method](...args);
    if ((res === -1 || res === false) && isProxy(args[0])) {
      args[0] = toRaw(args[0]);
      return arr[method](...args);
    }
    return res;
  }
  function noTracking(self2, method, args = []) {
    pauseTracking();
    startBatch();
    const res = toRaw(self2)[method].apply(self2, args);
    endBatch();
    resetTracking();
    return res;
  }
  const isNonTrackableKeys = /* @__PURE__ */ makeMap(`__proto__,__v_isRef,__isVue`);
  const builtInSymbols = new Set(
    /* @__PURE__ */ Object.getOwnPropertyNames(Symbol).filter((key) => key !== "arguments" && key !== "caller").map((key) => Symbol[key]).filter(isSymbol)
  );
  function hasOwnProperty(key) {
    if (!isSymbol(key)) key = String(key);
    const obj = toRaw(this);
    track(obj, "has", key);
    return obj.hasOwnProperty(key);
  }
  class BaseReactiveHandler {
    constructor(_isReadonly = false, _isShallow = false) {
      this._isReadonly = _isReadonly;
      this._isShallow = _isShallow;
    }
    get(target, key, receiver) {
      if (key === "__v_skip") return target["__v_skip"];
      const isReadonly2 = this._isReadonly, isShallow2 = this._isShallow;
      if (key === "__v_isReactive") {
        return !isReadonly2;
      } else if (key === "__v_isReadonly") {
        return isReadonly2;
      } else if (key === "__v_isShallow") {
        return isShallow2;
      } else if (key === "__v_raw") {
        if (receiver === (isReadonly2 ? isShallow2 ? shallowReadonlyMap : readonlyMap : isShallow2 ? shallowReactiveMap : reactiveMap).get(target) || // receiver is not the reactive proxy, but has the same prototype
        // this means the receiver is a user proxy of the reactive proxy
        Object.getPrototypeOf(target) === Object.getPrototypeOf(receiver)) {
          return target;
        }
        return;
      }
      const targetIsArray = isArray(target);
      if (!isReadonly2) {
        let fn;
        if (targetIsArray && (fn = arrayInstrumentations[key])) {
          return fn;
        }
        if (key === "hasOwnProperty") {
          return hasOwnProperty;
        }
      }
      const res = Reflect.get(
        target,
        key,
        // if this is a proxy wrapping a ref, return methods using the raw ref
        // as receiver so that we don't have to call `toRaw` on the ref in all
        // its class methods
        isRef(target) ? target : receiver
      );
      if (isSymbol(key) ? builtInSymbols.has(key) : isNonTrackableKeys(key)) {
        return res;
      }
      if (!isReadonly2) {
        track(target, "get", key);
      }
      if (isShallow2) {
        return res;
      }
      if (isRef(res)) {
        return targetIsArray && isIntegerKey(key) ? res : res.value;
      }
      if (isObject(res)) {
        return isReadonly2 ? readonly(res) : reactive(res);
      }
      return res;
    }
  }
  class MutableReactiveHandler extends BaseReactiveHandler {
    constructor(isShallow2 = false) {
      super(false, isShallow2);
    }
    set(target, key, value, receiver) {
      let oldValue = target[key];
      if (!this._isShallow) {
        const isOldValueReadonly = isReadonly(oldValue);
        if (!isShallow(value) && !isReadonly(value)) {
          oldValue = toRaw(oldValue);
          value = toRaw(value);
        }
        if (!isArray(target) && isRef(oldValue) && !isRef(value)) {
          if (isOldValueReadonly) {
            return false;
          } else {
            oldValue.value = value;
            return true;
          }
        }
      }
      const hadKey = isArray(target) && isIntegerKey(key) ? Number(key) < target.length : hasOwn(target, key);
      const result = Reflect.set(
        target,
        key,
        value,
        isRef(target) ? target : receiver
      );
      if (target === toRaw(receiver)) {
        if (!hadKey) {
          trigger(target, "add", key, value);
        } else if (hasChanged(value, oldValue)) {
          trigger(target, "set", key, value, oldValue);
        }
      }
      return result;
    }
    deleteProperty(target, key) {
      const hadKey = hasOwn(target, key);
      const oldValue = target[key];
      const result = Reflect.deleteProperty(target, key);
      if (result && hadKey) {
        trigger(target, "delete", key, void 0, oldValue);
      }
      return result;
    }
    has(target, key) {
      const result = Reflect.has(target, key);
      if (!isSymbol(key) || !builtInSymbols.has(key)) {
        track(target, "has", key);
      }
      return result;
    }
    ownKeys(target) {
      track(
        target,
        "iterate",
        isArray(target) ? "length" : ITERATE_KEY
      );
      return Reflect.ownKeys(target);
    }
  }
  class ReadonlyReactiveHandler extends BaseReactiveHandler {
    constructor(isShallow2 = false) {
      super(true, isShallow2);
    }
    set(target, key) {
      {
        warn$2(
          `Set operation on key "${String(key)}" failed: target is readonly.`,
          target
        );
      }
      return true;
    }
    deleteProperty(target, key) {
      {
        warn$2(
          `Delete operation on key "${String(key)}" failed: target is readonly.`,
          target
        );
      }
      return true;
    }
  }
  const mutableHandlers = /* @__PURE__ */ new MutableReactiveHandler();
  const readonlyHandlers = /* @__PURE__ */ new ReadonlyReactiveHandler();
  const shallowReactiveHandlers = /* @__PURE__ */ new MutableReactiveHandler(true);
  const shallowReadonlyHandlers = /* @__PURE__ */ new ReadonlyReactiveHandler(true);
  const toShallow = (value) => value;
  const getProto = (v) => Reflect.getPrototypeOf(v);
  function createIterableMethod(method, isReadonly2, isShallow2) {
    return function(...args) {
      const target = this["__v_raw"];
      const rawTarget = toRaw(target);
      const targetIsMap = isMap(rawTarget);
      const isPair = method === "entries" || method === Symbol.iterator && targetIsMap;
      const isKeyOnly = method === "keys" && targetIsMap;
      const innerIterator = target[method](...args);
      const wrap = isShallow2 ? toShallow : isReadonly2 ? toReadonly : toReactive;
      !isReadonly2 && track(
        rawTarget,
        "iterate",
        isKeyOnly ? MAP_KEY_ITERATE_KEY : ITERATE_KEY
      );
      return {
        // iterator protocol
        next() {
          const { value, done } = innerIterator.next();
          return done ? { value, done } : {
            value: isPair ? [wrap(value[0]), wrap(value[1])] : wrap(value),
            done
          };
        },
        // iterable protocol
        [Symbol.iterator]() {
          return this;
        }
      };
    };
  }
  function createReadonlyMethod(type) {
    return function(...args) {
      {
        const key = args[0] ? `on key "${args[0]}" ` : ``;
        warn$2(
          `${capitalize(type)} operation ${key}failed: target is readonly.`,
          toRaw(this)
        );
      }
      return type === "delete" ? false : type === "clear" ? void 0 : this;
    };
  }
  function createInstrumentations(readonly2, shallow) {
    const instrumentations = {
      get(key) {
        const target = this["__v_raw"];
        const rawTarget = toRaw(target);
        const rawKey = toRaw(key);
        if (!readonly2) {
          if (hasChanged(key, rawKey)) {
            track(rawTarget, "get", key);
          }
          track(rawTarget, "get", rawKey);
        }
        const { has } = getProto(rawTarget);
        const wrap = shallow ? toShallow : readonly2 ? toReadonly : toReactive;
        if (has.call(rawTarget, key)) {
          return wrap(target.get(key));
        } else if (has.call(rawTarget, rawKey)) {
          return wrap(target.get(rawKey));
        } else if (target !== rawTarget) {
          target.get(key);
        }
      },
      get size() {
        const target = this["__v_raw"];
        !readonly2 && track(toRaw(target), "iterate", ITERATE_KEY);
        return Reflect.get(target, "size", target);
      },
      has(key) {
        const target = this["__v_raw"];
        const rawTarget = toRaw(target);
        const rawKey = toRaw(key);
        if (!readonly2) {
          if (hasChanged(key, rawKey)) {
            track(rawTarget, "has", key);
          }
          track(rawTarget, "has", rawKey);
        }
        return key === rawKey ? target.has(key) : target.has(key) || target.has(rawKey);
      },
      forEach(callback, thisArg) {
        const observed = this;
        const target = observed["__v_raw"];
        const rawTarget = toRaw(target);
        const wrap = shallow ? toShallow : readonly2 ? toReadonly : toReactive;
        !readonly2 && track(rawTarget, "iterate", ITERATE_KEY);
        return target.forEach((value, key) => {
          return callback.call(thisArg, wrap(value), wrap(key), observed);
        });
      }
    };
    extend(
      instrumentations,
      readonly2 ? {
        add: createReadonlyMethod("add"),
        set: createReadonlyMethod("set"),
        delete: createReadonlyMethod("delete"),
        clear: createReadonlyMethod("clear")
      } : {
        add(value) {
          if (!shallow && !isShallow(value) && !isReadonly(value)) {
            value = toRaw(value);
          }
          const target = toRaw(this);
          const proto = getProto(target);
          const hadKey = proto.has.call(target, value);
          if (!hadKey) {
            target.add(value);
            trigger(target, "add", value, value);
          }
          return this;
        },
        set(key, value) {
          if (!shallow && !isShallow(value) && !isReadonly(value)) {
            value = toRaw(value);
          }
          const target = toRaw(this);
          const { has, get } = getProto(target);
          let hadKey = has.call(target, key);
          if (!hadKey) {
            key = toRaw(key);
            hadKey = has.call(target, key);
          } else {
            checkIdentityKeys(target, has, key);
          }
          const oldValue = get.call(target, key);
          target.set(key, value);
          if (!hadKey) {
            trigger(target, "add", key, value);
          } else if (hasChanged(value, oldValue)) {
            trigger(target, "set", key, value, oldValue);
          }
          return this;
        },
        delete(key) {
          const target = toRaw(this);
          const { has, get } = getProto(target);
          let hadKey = has.call(target, key);
          if (!hadKey) {
            key = toRaw(key);
            hadKey = has.call(target, key);
          } else {
            checkIdentityKeys(target, has, key);
          }
          const oldValue = get ? get.call(target, key) : void 0;
          const result = target.delete(key);
          if (hadKey) {
            trigger(target, "delete", key, void 0, oldValue);
          }
          return result;
        },
        clear() {
          const target = toRaw(this);
          const hadItems = target.size !== 0;
          const oldTarget = isMap(target) ? new Map(target) : new Set(target);
          const result = target.clear();
          if (hadItems) {
            trigger(
              target,
              "clear",
              void 0,
              void 0,
              oldTarget
            );
          }
          return result;
        }
      }
    );
    const iteratorMethods = [
      "keys",
      "values",
      "entries",
      Symbol.iterator
    ];
    iteratorMethods.forEach((method) => {
      instrumentations[method] = createIterableMethod(method, readonly2, shallow);
    });
    return instrumentations;
  }
  function createInstrumentationGetter(isReadonly2, shallow) {
    const instrumentations = createInstrumentations(isReadonly2, shallow);
    return (target, key, receiver) => {
      if (key === "__v_isReactive") {
        return !isReadonly2;
      } else if (key === "__v_isReadonly") {
        return isReadonly2;
      } else if (key === "__v_raw") {
        return target;
      }
      return Reflect.get(
        hasOwn(instrumentations, key) && key in target ? instrumentations : target,
        key,
        receiver
      );
    };
  }
  const mutableCollectionHandlers = {
    get: /* @__PURE__ */ createInstrumentationGetter(false, false)
  };
  const shallowCollectionHandlers = {
    get: /* @__PURE__ */ createInstrumentationGetter(false, true)
  };
  const readonlyCollectionHandlers = {
    get: /* @__PURE__ */ createInstrumentationGetter(true, false)
  };
  const shallowReadonlyCollectionHandlers = {
    get: /* @__PURE__ */ createInstrumentationGetter(true, true)
  };
  function checkIdentityKeys(target, has, key) {
    const rawKey = toRaw(key);
    if (rawKey !== key && has.call(target, rawKey)) {
      const type = toRawType(target);
      warn$2(
        `Reactive ${type} contains both the raw and reactive versions of the same object${type === `Map` ? ` as keys` : ``}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`
      );
    }
  }
  const reactiveMap = /* @__PURE__ */ new WeakMap();
  const shallowReactiveMap = /* @__PURE__ */ new WeakMap();
  const readonlyMap = /* @__PURE__ */ new WeakMap();
  const shallowReadonlyMap = /* @__PURE__ */ new WeakMap();
  function targetTypeMap(rawType) {
    switch (rawType) {
      case "Object":
      case "Array":
        return 1;
      case "Map":
      case "Set":
      case "WeakMap":
      case "WeakSet":
        return 2;
      default:
        return 0;
    }
  }
  function getTargetType(value) {
    return value["__v_skip"] || !Object.isExtensible(value) ? 0 : targetTypeMap(toRawType(value));
  }
  function reactive(target) {
    if (isReadonly(target)) {
      return target;
    }
    return createReactiveObject(
      target,
      false,
      mutableHandlers,
      mutableCollectionHandlers,
      reactiveMap
    );
  }
  function shallowReactive(target) {
    return createReactiveObject(
      target,
      false,
      shallowReactiveHandlers,
      shallowCollectionHandlers,
      shallowReactiveMap
    );
  }
  function readonly(target) {
    return createReactiveObject(
      target,
      true,
      readonlyHandlers,
      readonlyCollectionHandlers,
      readonlyMap
    );
  }
  function shallowReadonly(target) {
    return createReactiveObject(
      target,
      true,
      shallowReadonlyHandlers,
      shallowReadonlyCollectionHandlers,
      shallowReadonlyMap
    );
  }
  function createReactiveObject(target, isReadonly2, baseHandlers, collectionHandlers, proxyMap) {
    if (!isObject(target)) {
      {
        warn$2(
          `value cannot be made ${isReadonly2 ? "readonly" : "reactive"}: ${String(
            target
          )}`
        );
      }
      return target;
    }
    if (target["__v_raw"] && !(isReadonly2 && target["__v_isReactive"])) {
      return target;
    }
    const existingProxy = proxyMap.get(target);
    if (existingProxy) {
      return existingProxy;
    }
    const targetType = getTargetType(target);
    if (targetType === 0) {
      return target;
    }
    const proxy = new Proxy(
      target,
      targetType === 2 ? collectionHandlers : baseHandlers
    );
    proxyMap.set(target, proxy);
    return proxy;
  }
  function isReactive(value) {
    if (isReadonly(value)) {
      return isReactive(value["__v_raw"]);
    }
    return !!(value && value["__v_isReactive"]);
  }
  function isReadonly(value) {
    return !!(value && value["__v_isReadonly"]);
  }
  function isShallow(value) {
    return !!(value && value["__v_isShallow"]);
  }
  function isProxy(value) {
    return value ? !!value["__v_raw"] : false;
  }
  function toRaw(observed) {
    const raw = observed && observed["__v_raw"];
    return raw ? toRaw(raw) : observed;
  }
  function markRaw(value) {
    if (!hasOwn(value, "__v_skip") && Object.isExtensible(value)) {
      def(value, "__v_skip", true);
    }
    return value;
  }
  const toReactive = (value) => isObject(value) ? reactive(value) : value;
  const toReadonly = (value) => isObject(value) ? readonly(value) : value;
  function isRef(r) {
    return r ? r["__v_isRef"] === true : false;
  }
  function ref(value) {
    return createRef(value, false);
  }
  function createRef(rawValue, shallow) {
    if (isRef(rawValue)) {
      return rawValue;
    }
    return new RefImpl(rawValue, shallow);
  }
  class RefImpl {
    constructor(value, isShallow2) {
      this.dep = new Dep();
      this["__v_isRef"] = true;
      this["__v_isShallow"] = false;
      this._rawValue = isShallow2 ? value : toRaw(value);
      this._value = isShallow2 ? value : toReactive(value);
      this["__v_isShallow"] = isShallow2;
    }
    get value() {
      {
        this.dep.track({
          target: this,
          type: "get",
          key: "value"
        });
      }
      return this._value;
    }
    set value(newValue) {
      const oldValue = this._rawValue;
      const useDirectValue = this["__v_isShallow"] || isShallow(newValue) || isReadonly(newValue);
      newValue = useDirectValue ? newValue : toRaw(newValue);
      if (hasChanged(newValue, oldValue)) {
        this._rawValue = newValue;
        this._value = useDirectValue ? newValue : toReactive(newValue);
        {
          this.dep.trigger({
            target: this,
            type: "set",
            key: "value",
            newValue,
            oldValue
          });
        }
      }
    }
  }
  function unref(ref2) {
    return isRef(ref2) ? ref2.value : ref2;
  }
  const shallowUnwrapHandlers = {
    get: (target, key, receiver) => key === "__v_raw" ? target : unref(Reflect.get(target, key, receiver)),
    set: (target, key, value, receiver) => {
      const oldValue = target[key];
      if (isRef(oldValue) && !isRef(value)) {
        oldValue.value = value;
        return true;
      } else {
        return Reflect.set(target, key, value, receiver);
      }
    }
  };
  function proxyRefs(objectWithRefs) {
    return isReactive(objectWithRefs) ? objectWithRefs : new Proxy(objectWithRefs, shallowUnwrapHandlers);
  }
  class ComputedRefImpl {
    constructor(fn, setter, isSSR) {
      this.fn = fn;
      this.setter = setter;
      this._value = void 0;
      this.dep = new Dep(this);
      this.__v_isRef = true;
      this.deps = void 0;
      this.depsTail = void 0;
      this.flags = 16;
      this.globalVersion = globalVersion - 1;
      this.next = void 0;
      this.effect = this;
      this["__v_isReadonly"] = !setter;
      this.isSSR = isSSR;
    }
    /**
     * @internal
     */
    notify() {
      this.flags |= 16;
      if (!(this.flags & 8) && // avoid infinite self recursion
      activeSub !== this) {
        batch(this, true);
        return true;
      }
    }
    get value() {
      const link = this.dep.track({
        target: this,
        type: "get",
        key: "value"
      });
      refreshComputed(this);
      if (link) {
        link.version = this.dep.version;
      }
      return this._value;
    }
    set value(newValue) {
      if (this.setter) {
        this.setter(newValue);
      } else {
        warn$2("Write operation failed: computed value is readonly");
      }
    }
  }
  function computed$1(getterOrOptions, debugOptions, isSSR = false) {
    let getter;
    let setter;
    if (isFunction(getterOrOptions)) {
      getter = getterOrOptions;
    } else {
      getter = getterOrOptions.get;
      setter = getterOrOptions.set;
    }
    const cRef = new ComputedRefImpl(getter, setter, isSSR);
    if (debugOptions && !isSSR) {
      cRef.onTrack = debugOptions.onTrack;
      cRef.onTrigger = debugOptions.onTrigger;
    }
    return cRef;
  }
  const INITIAL_WATCHER_VALUE = {};
  const cleanupMap = /* @__PURE__ */ new WeakMap();
  let activeWatcher = void 0;
  function onWatcherCleanup(cleanupFn, failSilently = false, owner = activeWatcher) {
    if (owner) {
      let cleanups = cleanupMap.get(owner);
      if (!cleanups) cleanupMap.set(owner, cleanups = []);
      cleanups.push(cleanupFn);
    } else if (!failSilently) {
      warn$2(
        `onWatcherCleanup() was called when there was no active watcher to associate with.`
      );
    }
  }
  function watch$1(source, cb, options = EMPTY_OBJ) {
    const { immediate, deep, once, scheduler, augmentJob, call } = options;
    const warnInvalidSource = (s) => {
      (options.onWarn || warn$2)(
        `Invalid watch source: `,
        s,
        `A watch source can only be a getter/effect function, a ref, a reactive object, or an array of these types.`
      );
    };
    const reactiveGetter = (source2) => {
      if (deep) return source2;
      if (isShallow(source2) || deep === false || deep === 0)
        return traverse(source2, 1);
      return traverse(source2);
    };
    let effect2;
    let getter;
    let cleanup;
    let boundCleanup;
    let forceTrigger = false;
    let isMultiSource = false;
    if (isRef(source)) {
      getter = () => source.value;
      forceTrigger = isShallow(source);
    } else if (isReactive(source)) {
      getter = () => reactiveGetter(source);
      forceTrigger = true;
    } else if (isArray(source)) {
      isMultiSource = true;
      forceTrigger = source.some((s) => isReactive(s) || isShallow(s));
      getter = () => source.map((s) => {
        if (isRef(s)) {
          return s.value;
        } else if (isReactive(s)) {
          return reactiveGetter(s);
        } else if (isFunction(s)) {
          return call ? call(s, 2) : s();
        } else {
          warnInvalidSource(s);
        }
      });
    } else if (isFunction(source)) {
      if (cb) {
        getter = call ? () => call(source, 2) : source;
      } else {
        getter = () => {
          if (cleanup) {
            pauseTracking();
            try {
              cleanup();
            } finally {
              resetTracking();
            }
          }
          const currentEffect = activeWatcher;
          activeWatcher = effect2;
          try {
            return call ? call(source, 3, [boundCleanup]) : source(boundCleanup);
          } finally {
            activeWatcher = currentEffect;
          }
        };
      }
    } else {
      getter = NOOP;
      warnInvalidSource(source);
    }
    if (cb && deep) {
      const baseGetter = getter;
      const depth = deep === true ? Infinity : deep;
      getter = () => traverse(baseGetter(), depth);
    }
    const scope = getCurrentScope();
    const watchHandle = () => {
      effect2.stop();
      if (scope && scope.active) {
        remove(scope.effects, effect2);
      }
    };
    if (once && cb) {
      const _cb = cb;
      cb = (...args) => {
        _cb(...args);
        watchHandle();
      };
    }
    let oldValue = isMultiSource ? new Array(source.length).fill(INITIAL_WATCHER_VALUE) : INITIAL_WATCHER_VALUE;
    const job = (immediateFirstRun) => {
      if (!(effect2.flags & 1) || !effect2.dirty && !immediateFirstRun) {
        return;
      }
      if (cb) {
        const newValue = effect2.run();
        if (deep || forceTrigger || (isMultiSource ? newValue.some((v, i) => hasChanged(v, oldValue[i])) : hasChanged(newValue, oldValue))) {
          if (cleanup) {
            cleanup();
          }
          const currentWatcher = activeWatcher;
          activeWatcher = effect2;
          try {
            const args = [
              newValue,
              // pass undefined as the old value when it's changed for the first time
              oldValue === INITIAL_WATCHER_VALUE ? void 0 : isMultiSource && oldValue[0] === INITIAL_WATCHER_VALUE ? [] : oldValue,
              boundCleanup
            ];
            call ? call(cb, 3, args) : (
              // @ts-expect-error
              cb(...args)
            );
            oldValue = newValue;
          } finally {
            activeWatcher = currentWatcher;
          }
        }
      } else {
        effect2.run();
      }
    };
    if (augmentJob) {
      augmentJob(job);
    }
    effect2 = new ReactiveEffect(getter);
    effect2.scheduler = scheduler ? () => scheduler(job, false) : job;
    boundCleanup = (fn) => onWatcherCleanup(fn, false, effect2);
    cleanup = effect2.onStop = () => {
      const cleanups = cleanupMap.get(effect2);
      if (cleanups) {
        if (call) {
          call(cleanups, 4);
        } else {
          for (const cleanup2 of cleanups) cleanup2();
        }
        cleanupMap.delete(effect2);
      }
    };
    {
      effect2.onTrack = options.onTrack;
      effect2.onTrigger = options.onTrigger;
    }
    if (cb) {
      if (immediate) {
        job(true);
      } else {
        oldValue = effect2.run();
      }
    } else if (scheduler) {
      scheduler(job.bind(null, true), true);
    } else {
      effect2.run();
    }
    watchHandle.pause = effect2.pause.bind(effect2);
    watchHandle.resume = effect2.resume.bind(effect2);
    watchHandle.stop = watchHandle;
    return watchHandle;
  }
  function traverse(value, depth = Infinity, seen) {
    if (depth <= 0 || !isObject(value) || value["__v_skip"]) {
      return value;
    }
    seen = seen || /* @__PURE__ */ new Set();
    if (seen.has(value)) {
      return value;
    }
    seen.add(value);
    depth--;
    if (isRef(value)) {
      traverse(value.value, depth, seen);
    } else if (isArray(value)) {
      for (let i = 0; i < value.length; i++) {
        traverse(value[i], depth, seen);
      }
    } else if (isSet(value) || isMap(value)) {
      value.forEach((v) => {
        traverse(v, depth, seen);
      });
    } else if (isPlainObject(value)) {
      for (const key in value) {
        traverse(value[key], depth, seen);
      }
      for (const key of Object.getOwnPropertySymbols(value)) {
        if (Object.prototype.propertyIsEnumerable.call(value, key)) {
          traverse(value[key], depth, seen);
        }
      }
    }
    return value;
  }
  /**
  * @vue/runtime-core v3.5.13
  * (c) 2018-present Yuxi (Evan) You and Vue contributors
  * @license MIT
  **/
  const stack = [];
  function pushWarningContext(vnode) {
    stack.push(vnode);
  }
  function popWarningContext() {
    stack.pop();
  }
  let isWarning = false;
  function warn$1(msg, ...args) {
    if (isWarning) return;
    isWarning = true;
    pauseTracking();
    const instance = stack.length ? stack[stack.length - 1].component : null;
    const appWarnHandler = instance && instance.appContext.config.warnHandler;
    const trace = getComponentTrace();
    if (appWarnHandler) {
      callWithErrorHandling(
        appWarnHandler,
        instance,
        11,
        [
          // eslint-disable-next-line no-restricted-syntax
          msg + args.map((a) => {
            var _a, _b;
            return (_b = (_a = a.toString) == null ? void 0 : _a.call(a)) != null ? _b : JSON.stringify(a);
          }).join(""),
          instance && instance.proxy,
          trace.map(
            ({ vnode }) => `at <${formatComponentName(instance, vnode.type)}>`
          ).join("\n"),
          trace
        ]
      );
    } else {
      const warnArgs = [`[Vue warn]: ${msg}`, ...args];
      if (trace.length && // avoid spamming console during tests
      true) {
        warnArgs.push(`
`, ...formatTrace(trace));
      }
      console.warn(...warnArgs);
    }
    resetTracking();
    isWarning = false;
  }
  function getComponentTrace() {
    let currentVNode = stack[stack.length - 1];
    if (!currentVNode) {
      return [];
    }
    const normalizedStack = [];
    while (currentVNode) {
      const last = normalizedStack[0];
      if (last && last.vnode === currentVNode) {
        last.recurseCount++;
      } else {
        normalizedStack.push({
          vnode: currentVNode,
          recurseCount: 0
        });
      }
      const parentInstance = currentVNode.component && currentVNode.component.parent;
      currentVNode = parentInstance && parentInstance.vnode;
    }
    return normalizedStack;
  }
  function formatTrace(trace) {
    const logs = [];
    trace.forEach((entry, i) => {
      logs.push(...i === 0 ? [] : [`
`], ...formatTraceEntry(entry));
    });
    return logs;
  }
  function formatTraceEntry({ vnode, recurseCount }) {
    const postfix = recurseCount > 0 ? `... (${recurseCount} recursive calls)` : ``;
    const isRoot = vnode.component ? vnode.component.parent == null : false;
    const open = ` at <${formatComponentName(
      vnode.component,
      vnode.type,
      isRoot
    )}`;
    const close = `>` + postfix;
    return vnode.props ? [open, ...formatProps(vnode.props), close] : [open + close];
  }
  function formatProps(props) {
    const res = [];
    const keys = Object.keys(props);
    keys.slice(0, 3).forEach((key) => {
      res.push(...formatProp(key, props[key]));
    });
    if (keys.length > 3) {
      res.push(` ...`);
    }
    return res;
  }
  function formatProp(key, value, raw) {
    if (isString(value)) {
      value = JSON.stringify(value);
      return raw ? value : [`${key}=${value}`];
    } else if (typeof value === "number" || typeof value === "boolean" || value == null) {
      return raw ? value : [`${key}=${value}`];
    } else if (isRef(value)) {
      value = formatProp(key, toRaw(value.value), true);
      return raw ? value : [`${key}=Ref<`, value, `>`];
    } else if (isFunction(value)) {
      return [`${key}=fn${value.name ? `<${value.name}>` : ``}`];
    } else {
      value = toRaw(value);
      return raw ? value : [`${key}=`, value];
    }
  }
  const ErrorTypeStrings$1 = {
    ["sp"]: "serverPrefetch hook",
    ["bc"]: "beforeCreate hook",
    ["c"]: "created hook",
    ["bm"]: "beforeMount hook",
    ["m"]: "mounted hook",
    ["bu"]: "beforeUpdate hook",
    ["u"]: "updated",
    ["bum"]: "beforeUnmount hook",
    ["um"]: "unmounted hook",
    ["a"]: "activated hook",
    ["da"]: "deactivated hook",
    ["ec"]: "errorCaptured hook",
    ["rtc"]: "renderTracked hook",
    ["rtg"]: "renderTriggered hook",
    [0]: "setup function",
    [1]: "render function",
    [2]: "watcher getter",
    [3]: "watcher callback",
    [4]: "watcher cleanup function",
    [5]: "native event handler",
    [6]: "component event handler",
    [7]: "vnode hook",
    [8]: "directive hook",
    [9]: "transition hook",
    [10]: "app errorHandler",
    [11]: "app warnHandler",
    [12]: "ref function",
    [13]: "async component loader",
    [14]: "scheduler flush",
    [15]: "component update",
    [16]: "app unmount cleanup function"
  };
  function callWithErrorHandling(fn, instance, type, args) {
    try {
      return args ? fn(...args) : fn();
    } catch (err) {
      handleError(err, instance, type);
    }
  }
  function callWithAsyncErrorHandling(fn, instance, type, args) {
    if (isFunction(fn)) {
      const res = callWithErrorHandling(fn, instance, type, args);
      if (res && isPromise(res)) {
        res.catch((err) => {
          handleError(err, instance, type);
        });
      }
      return res;
    }
    if (isArray(fn)) {
      const values = [];
      for (let i = 0; i < fn.length; i++) {
        values.push(callWithAsyncErrorHandling(fn[i], instance, type, args));
      }
      return values;
    } else {
      warn$1(
        `Invalid value type passed to callWithAsyncErrorHandling(): ${typeof fn}`
      );
    }
  }
  function handleError(err, instance, type, throwInDev = true) {
    const contextVNode = instance ? instance.vnode : null;
    const { errorHandler, throwUnhandledErrorInProduction } = instance && instance.appContext.config || EMPTY_OBJ;
    if (instance) {
      let cur = instance.parent;
      const exposedInstance = instance.proxy;
      const errorInfo = ErrorTypeStrings$1[type];
      while (cur) {
        const errorCapturedHooks = cur.ec;
        if (errorCapturedHooks) {
          for (let i = 0; i < errorCapturedHooks.length; i++) {
            if (errorCapturedHooks[i](err, exposedInstance, errorInfo) === false) {
              return;
            }
          }
        }
        cur = cur.parent;
      }
      if (errorHandler) {
        pauseTracking();
        callWithErrorHandling(errorHandler, null, 10, [
          err,
          exposedInstance,
          errorInfo
        ]);
        resetTracking();
        return;
      }
    }
    logError(err, type, contextVNode, throwInDev, throwUnhandledErrorInProduction);
  }
  function logError(err, type, contextVNode, throwInDev = true, throwInProd = false) {
    {
      const info = ErrorTypeStrings$1[type];
      if (contextVNode) {
        pushWarningContext(contextVNode);
      }
      warn$1(`Unhandled error${info ? ` during execution of ${info}` : ``}`);
      if (contextVNode) {
        popWarningContext();
      }
      if (throwInDev) {
        throw err;
      } else {
        console.error(err);
      }
    }
  }
  const queue = [];
  let flushIndex = -1;
  const pendingPostFlushCbs = [];
  let activePostFlushCbs = null;
  let postFlushIndex = 0;
  const resolvedPromise = /* @__PURE__ */ Promise.resolve();
  let currentFlushPromise = null;
  const RECURSION_LIMIT = 100;
  function nextTick(fn) {
    const p2 = currentFlushPromise || resolvedPromise;
    return fn ? p2.then(this ? fn.bind(this) : fn) : p2;
  }
  function findInsertionIndex(id) {
    let start = flushIndex + 1;
    let end = queue.length;
    while (start < end) {
      const middle = start + end >>> 1;
      const middleJob = queue[middle];
      const middleJobId = getId(middleJob);
      if (middleJobId < id || middleJobId === id && middleJob.flags & 2) {
        start = middle + 1;
      } else {
        end = middle;
      }
    }
    return start;
  }
  function queueJob(job) {
    if (!(job.flags & 1)) {
      const jobId = getId(job);
      const lastJob = queue[queue.length - 1];
      if (!lastJob || // fast path when the job id is larger than the tail
      !(job.flags & 2) && jobId >= getId(lastJob)) {
        queue.push(job);
      } else {
        queue.splice(findInsertionIndex(jobId), 0, job);
      }
      job.flags |= 1;
      queueFlush();
    }
  }
  function queueFlush() {
    if (!currentFlushPromise) {
      currentFlushPromise = resolvedPromise.then(flushJobs);
    }
  }
  function queuePostFlushCb(cb) {
    if (!isArray(cb)) {
      if (activePostFlushCbs && cb.id === -1) {
        activePostFlushCbs.splice(postFlushIndex + 1, 0, cb);
      } else if (!(cb.flags & 1)) {
        pendingPostFlushCbs.push(cb);
        cb.flags |= 1;
      }
    } else {
      pendingPostFlushCbs.push(...cb);
    }
    queueFlush();
  }
  function flushPreFlushCbs(instance, seen, i = flushIndex + 1) {
    {
      seen = seen || /* @__PURE__ */ new Map();
    }
    for (; i < queue.length; i++) {
      const cb = queue[i];
      if (cb && cb.flags & 2) {
        if (instance && cb.id !== instance.uid) {
          continue;
        }
        if (checkRecursiveUpdates(seen, cb)) {
          continue;
        }
        queue.splice(i, 1);
        i--;
        if (cb.flags & 4) {
          cb.flags &= -2;
        }
        cb();
        if (!(cb.flags & 4)) {
          cb.flags &= -2;
        }
      }
    }
  }
  function flushPostFlushCbs(seen) {
    if (pendingPostFlushCbs.length) {
      const deduped = [...new Set(pendingPostFlushCbs)].sort(
        (a, b) => getId(a) - getId(b)
      );
      pendingPostFlushCbs.length = 0;
      if (activePostFlushCbs) {
        activePostFlushCbs.push(...deduped);
        return;
      }
      activePostFlushCbs = deduped;
      {
        seen = seen || /* @__PURE__ */ new Map();
      }
      for (postFlushIndex = 0; postFlushIndex < activePostFlushCbs.length; postFlushIndex++) {
        const cb = activePostFlushCbs[postFlushIndex];
        if (checkRecursiveUpdates(seen, cb)) {
          continue;
        }
        if (cb.flags & 4) {
          cb.flags &= -2;
        }
        if (!(cb.flags & 8)) cb();
        cb.flags &= -2;
      }
      activePostFlushCbs = null;
      postFlushIndex = 0;
    }
  }
  const getId = (job) => job.id == null ? job.flags & 2 ? -1 : Infinity : job.id;
  function flushJobs(seen) {
    {
      seen = seen || /* @__PURE__ */ new Map();
    }
    const check = (job) => checkRecursiveUpdates(seen, job);
    try {
      for (flushIndex = 0; flushIndex < queue.length; flushIndex++) {
        const job = queue[flushIndex];
        if (job && !(job.flags & 8)) {
          if (check(job)) {
            continue;
          }
          if (job.flags & 4) {
            job.flags &= ~1;
          }
          callWithErrorHandling(
            job,
            job.i,
            job.i ? 15 : 14
          );
          if (!(job.flags & 4)) {
            job.flags &= ~1;
          }
        }
      }
    } finally {
      for (; flushIndex < queue.length; flushIndex++) {
        const job = queue[flushIndex];
        if (job) {
          job.flags &= -2;
        }
      }
      flushIndex = -1;
      queue.length = 0;
      flushPostFlushCbs(seen);
      currentFlushPromise = null;
      if (queue.length || pendingPostFlushCbs.length) {
        flushJobs(seen);
      }
    }
  }
  function checkRecursiveUpdates(seen, fn) {
    const count = seen.get(fn) || 0;
    if (count > RECURSION_LIMIT) {
      const instance = fn.i;
      const componentName2 = instance && getComponentName(instance.type);
      handleError(
        `Maximum recursive updates exceeded${componentName2 ? ` in component <${componentName2}>` : ``}. This means you have a reactive effect that is mutating its own dependencies and thus recursively triggering itself. Possible sources include component template, render function, updated hook or watcher source function.`,
        null,
        10
      );
      return true;
    }
    seen.set(fn, count + 1);
    return false;
  }
  let isHmrUpdating = false;
  const hmrDirtyComponents = /* @__PURE__ */ new Map();
  {
    getGlobalThis().__VUE_HMR_RUNTIME__ = {
      createRecord: tryWrap(createRecord),
      rerender: tryWrap(rerender),
      reload: tryWrap(reload)
    };
  }
  const map = /* @__PURE__ */ new Map();
  function registerHMR(instance) {
    const id = instance.type.__hmrId;
    let record = map.get(id);
    if (!record) {
      createRecord(id, instance.type);
      record = map.get(id);
    }
    record.instances.add(instance);
  }
  function unregisterHMR(instance) {
    map.get(instance.type.__hmrId).instances.delete(instance);
  }
  function createRecord(id, initialDef) {
    if (map.has(id)) {
      return false;
    }
    map.set(id, {
      initialDef: normalizeClassComponent(initialDef),
      instances: /* @__PURE__ */ new Set()
    });
    return true;
  }
  function normalizeClassComponent(component) {
    return isClassComponent(component) ? component.__vccOpts : component;
  }
  function rerender(id, newRender) {
    const record = map.get(id);
    if (!record) {
      return;
    }
    record.initialDef.render = newRender;
    [...record.instances].forEach((instance) => {
      if (newRender) {
        instance.render = newRender;
        normalizeClassComponent(instance.type).render = newRender;
      }
      instance.renderCache = [];
      isHmrUpdating = true;
      instance.update();
      isHmrUpdating = false;
    });
  }
  function reload(id, newComp) {
    const record = map.get(id);
    if (!record) return;
    newComp = normalizeClassComponent(newComp);
    updateComponentDef(record.initialDef, newComp);
    const instances = [...record.instances];
    for (let i = 0; i < instances.length; i++) {
      const instance = instances[i];
      const oldComp = normalizeClassComponent(instance.type);
      let dirtyInstances = hmrDirtyComponents.get(oldComp);
      if (!dirtyInstances) {
        if (oldComp !== record.initialDef) {
          updateComponentDef(oldComp, newComp);
        }
        hmrDirtyComponents.set(oldComp, dirtyInstances = /* @__PURE__ */ new Set());
      }
      dirtyInstances.add(instance);
      instance.appContext.propsCache.delete(instance.type);
      instance.appContext.emitsCache.delete(instance.type);
      instance.appContext.optionsCache.delete(instance.type);
      if (instance.ceReload) {
        dirtyInstances.add(instance);
        instance.ceReload(newComp.styles);
        dirtyInstances.delete(instance);
      } else if (instance.parent) {
        queueJob(() => {
          isHmrUpdating = true;
          instance.parent.update();
          isHmrUpdating = false;
          dirtyInstances.delete(instance);
        });
      } else if (instance.appContext.reload) {
        instance.appContext.reload();
      } else if (typeof window !== "undefined") {
        window.location.reload();
      } else {
        console.warn(
          "[HMR] Root or manually mounted instance modified. Full reload required."
        );
      }
      if (instance.root.ce && instance !== instance.root) {
        instance.root.ce._removeChildStyle(oldComp);
      }
    }
    queuePostFlushCb(() => {
      hmrDirtyComponents.clear();
    });
  }
  function updateComponentDef(oldComp, newComp) {
    extend(oldComp, newComp);
    for (const key in oldComp) {
      if (key !== "__file" && !(key in newComp)) {
        delete oldComp[key];
      }
    }
  }
  function tryWrap(fn) {
    return (id, arg) => {
      try {
        return fn(id, arg);
      } catch (e) {
        console.error(e);
        console.warn(
          `[HMR] Something went wrong during Vue component hot-reload. Full reload required.`
        );
      }
    };
  }
  let devtools$1;
  let buffer = [];
  let devtoolsNotInstalled = false;
  function emit$1(event, ...args) {
    if (devtools$1) {
      devtools$1.emit(event, ...args);
    } else if (!devtoolsNotInstalled) {
      buffer.push({ event, args });
    }
  }
  function setDevtoolsHook$1(hook, target) {
    var _a, _b;
    devtools$1 = hook;
    if (devtools$1) {
      devtools$1.enabled = true;
      buffer.forEach(({ event, args }) => devtools$1.emit(event, ...args));
      buffer = [];
    } else if (
      // handle late devtools injection - only do this if we are in an actual
      // browser environment to avoid the timer handle stalling test runner exit
      // (#4815)
      typeof window !== "undefined" && // some envs mock window but not fully
      window.HTMLElement && // also exclude jsdom
      // eslint-disable-next-line no-restricted-syntax
      !((_b = (_a = window.navigator) == null ? void 0 : _a.userAgent) == null ? void 0 : _b.includes("jsdom"))
    ) {
      const replay = target.__VUE_DEVTOOLS_HOOK_REPLAY__ = target.__VUE_DEVTOOLS_HOOK_REPLAY__ || [];
      replay.push((newHook) => {
        setDevtoolsHook$1(newHook, target);
      });
      setTimeout(() => {
        if (!devtools$1) {
          target.__VUE_DEVTOOLS_HOOK_REPLAY__ = null;
          devtoolsNotInstalled = true;
          buffer = [];
        }
      }, 3e3);
    } else {
      devtoolsNotInstalled = true;
      buffer = [];
    }
  }
  function devtoolsInitApp(app2, version2) {
    emit$1("app:init", app2, version2, {
      Fragment,
      Text,
      Comment,
      Static
    });
  }
  function devtoolsUnmountApp(app2) {
    emit$1("app:unmount", app2);
  }
  const devtoolsComponentAdded = /* @__PURE__ */ createDevtoolsComponentHook(
    "component:added"
    /* COMPONENT_ADDED */
  );
  const devtoolsComponentUpdated = /* @__PURE__ */ createDevtoolsComponentHook(
    "component:updated"
    /* COMPONENT_UPDATED */
  );
  const _devtoolsComponentRemoved = /* @__PURE__ */ createDevtoolsComponentHook(
    "component:removed"
    /* COMPONENT_REMOVED */
  );
  const devtoolsComponentRemoved = (component) => {
    if (devtools$1 && typeof devtools$1.cleanupBuffer === "function" && // remove the component if it wasn't buffered
    !devtools$1.cleanupBuffer(component)) {
      _devtoolsComponentRemoved(component);
    }
  };
  /*! #__NO_SIDE_EFFECTS__ */
  // @__NO_SIDE_EFFECTS__
  function createDevtoolsComponentHook(hook) {
    return (component) => {
      emit$1(
        hook,
        component.appContext.app,
        component.uid,
        component.parent ? component.parent.uid : void 0,
        component
      );
    };
  }
  const devtoolsPerfStart = /* @__PURE__ */ createDevtoolsPerformanceHook(
    "perf:start"
    /* PERFORMANCE_START */
  );
  const devtoolsPerfEnd = /* @__PURE__ */ createDevtoolsPerformanceHook(
    "perf:end"
    /* PERFORMANCE_END */
  );
  function createDevtoolsPerformanceHook(hook) {
    return (component, type, time) => {
      emit$1(hook, component.appContext.app, component.uid, component, type, time);
    };
  }
  function devtoolsComponentEmit(component, event, params) {
    emit$1(
      "component:emit",
      component.appContext.app,
      component,
      event,
      params
    );
  }
  let currentRenderingInstance = null;
  let currentScopeId = null;
  function setCurrentRenderingInstance(instance) {
    const prev = currentRenderingInstance;
    currentRenderingInstance = instance;
    currentScopeId = instance && instance.type.__scopeId || null;
    return prev;
  }
  function withCtx(fn, ctx = currentRenderingInstance, isNonScopedSlot) {
    if (!ctx) return fn;
    if (fn._n) {
      return fn;
    }
    const renderFnWithContext = (...args) => {
      if (renderFnWithContext._d) {
        setBlockTracking(-1);
      }
      const prevInstance = setCurrentRenderingInstance(ctx);
      let res;
      try {
        res = fn(...args);
      } finally {
        setCurrentRenderingInstance(prevInstance);
        if (renderFnWithContext._d) {
          setBlockTracking(1);
        }
      }
      {
        devtoolsComponentUpdated(ctx);
      }
      return res;
    };
    renderFnWithContext._n = true;
    renderFnWithContext._c = true;
    renderFnWithContext._d = true;
    return renderFnWithContext;
  }
  function validateDirectiveName(name) {
    if (isBuiltInDirective(name)) {
      warn$1("Do not use built-in directive ids as custom directive id: " + name);
    }
  }
  function withDirectives(vnode, directives) {
    if (currentRenderingInstance === null) {
      warn$1(`withDirectives can only be used inside render functions.`);
      return vnode;
    }
    const instance = getComponentPublicInstance(currentRenderingInstance);
    const bindings = vnode.dirs || (vnode.dirs = []);
    for (let i = 0; i < directives.length; i++) {
      let [dir, value, arg, modifiers = EMPTY_OBJ] = directives[i];
      if (dir) {
        if (isFunction(dir)) {
          dir = {
            mounted: dir,
            updated: dir
          };
        }
        if (dir.deep) {
          traverse(value);
        }
        bindings.push({
          dir,
          instance,
          value,
          oldValue: void 0,
          arg,
          modifiers
        });
      }
    }
    return vnode;
  }
  function invokeDirectiveHook(vnode, prevVNode, instance, name) {
    const bindings = vnode.dirs;
    const oldBindings = prevVNode && prevVNode.dirs;
    for (let i = 0; i < bindings.length; i++) {
      const binding = bindings[i];
      if (oldBindings) {
        binding.oldValue = oldBindings[i].value;
      }
      let hook = binding.dir[name];
      if (hook) {
        pauseTracking();
        callWithAsyncErrorHandling(hook, instance, 8, [
          vnode.el,
          binding,
          vnode,
          prevVNode
        ]);
        resetTracking();
      }
    }
  }
  const TeleportEndKey = Symbol("_vte");
  const isTeleport = (type) => type.__isTeleport;
  function setTransitionHooks(vnode, hooks) {
    if (vnode.shapeFlag & 6 && vnode.component) {
      vnode.transition = hooks;
      setTransitionHooks(vnode.component.subTree, hooks);
    } else if (vnode.shapeFlag & 128) {
      vnode.ssContent.transition = hooks.clone(vnode.ssContent);
      vnode.ssFallback.transition = hooks.clone(vnode.ssFallback);
    } else {
      vnode.transition = hooks;
    }
  }
  /*! #__NO_SIDE_EFFECTS__ */
  // @__NO_SIDE_EFFECTS__
  function defineComponent(options, extraOptions) {
    return isFunction(options) ? (
      // #8236: extend call and options.name access are considered side-effects
      // by Rollup, so we have to wrap it in a pure-annotated IIFE.
      /* @__PURE__ */ (() => extend({ name: options.name }, extraOptions, { setup: options }))()
    ) : options;
  }
  function markAsyncBoundary(instance) {
    instance.ids = [instance.ids[0] + instance.ids[2]++ + "-", 0, 0];
  }
  const knownTemplateRefs = /* @__PURE__ */ new WeakSet();
  function setRef(rawRef, oldRawRef, parentSuspense, vnode, isUnmount = false) {
    if (isArray(rawRef)) {
      rawRef.forEach(
        (r, i) => setRef(
          r,
          oldRawRef && (isArray(oldRawRef) ? oldRawRef[i] : oldRawRef),
          parentSuspense,
          vnode,
          isUnmount
        )
      );
      return;
    }
    if (isAsyncWrapper(vnode) && !isUnmount) {
      if (vnode.shapeFlag & 512 && vnode.type.__asyncResolved && vnode.component.subTree.component) {
        setRef(rawRef, oldRawRef, parentSuspense, vnode.component.subTree);
      }
      return;
    }
    const refValue = vnode.shapeFlag & 4 ? getComponentPublicInstance(vnode.component) : vnode.el;
    const value = isUnmount ? null : refValue;
    const { i: owner, r: ref3 } = rawRef;
    if (!owner) {
      warn$1(
        `Missing ref owner context. ref cannot be used on hoisted vnodes. A vnode with ref must be created inside the render function.`
      );
      return;
    }
    const oldRef = oldRawRef && oldRawRef.r;
    const refs = owner.refs === EMPTY_OBJ ? owner.refs = {} : owner.refs;
    const setupState = owner.setupState;
    const rawSetupState = toRaw(setupState);
    const canSetSetupRef = setupState === EMPTY_OBJ ? () => false : (key) => {
      {
        if (hasOwn(rawSetupState, key) && !isRef(rawSetupState[key])) {
          warn$1(
            `Template ref "${key}" used on a non-ref value. It will not work in the production build.`
          );
        }
        if (knownTemplateRefs.has(rawSetupState[key])) {
          return false;
        }
      }
      return hasOwn(rawSetupState, key);
    };
    if (oldRef != null && oldRef !== ref3) {
      if (isString(oldRef)) {
        refs[oldRef] = null;
        if (canSetSetupRef(oldRef)) {
          setupState[oldRef] = null;
        }
      } else if (isRef(oldRef)) {
        oldRef.value = null;
      }
    }
    if (isFunction(ref3)) {
      callWithErrorHandling(ref3, owner, 12, [value, refs]);
    } else {
      const _isString = isString(ref3);
      const _isRef = isRef(ref3);
      if (_isString || _isRef) {
        const doSet = () => {
          if (rawRef.f) {
            const existing = _isString ? canSetSetupRef(ref3) ? setupState[ref3] : refs[ref3] : ref3.value;
            if (isUnmount) {
              isArray(existing) && remove(existing, refValue);
            } else {
              if (!isArray(existing)) {
                if (_isString) {
                  refs[ref3] = [refValue];
                  if (canSetSetupRef(ref3)) {
                    setupState[ref3] = refs[ref3];
                  }
                } else {
                  ref3.value = [refValue];
                  if (rawRef.k) refs[rawRef.k] = ref3.value;
                }
              } else if (!existing.includes(refValue)) {
                existing.push(refValue);
              }
            }
          } else if (_isString) {
            refs[ref3] = value;
            if (canSetSetupRef(ref3)) {
              setupState[ref3] = value;
            }
          } else if (_isRef) {
            ref3.value = value;
            if (rawRef.k) refs[rawRef.k] = value;
          } else {
            warn$1("Invalid template ref type:", ref3, `(${typeof ref3})`);
          }
        };
        if (value) {
          doSet.id = -1;
          queuePostRenderEffect(doSet, parentSuspense);
        } else {
          doSet();
        }
      } else {
        warn$1("Invalid template ref type:", ref3, `(${typeof ref3})`);
      }
    }
  }
  getGlobalThis().requestIdleCallback || ((cb) => setTimeout(cb, 1));
  getGlobalThis().cancelIdleCallback || ((id) => clearTimeout(id));
  const isAsyncWrapper = (i) => !!i.type.__asyncLoader;
  const isKeepAlive = (vnode) => vnode.type.__isKeepAlive;
  function onActivated(hook, target) {
    registerKeepAliveHook(hook, "a", target);
  }
  function onDeactivated(hook, target) {
    registerKeepAliveHook(hook, "da", target);
  }
  function registerKeepAliveHook(hook, type, target = currentInstance) {
    const wrappedHook = hook.__wdc || (hook.__wdc = () => {
      let current = target;
      while (current) {
        if (current.isDeactivated) {
          return;
        }
        current = current.parent;
      }
      return hook();
    });
    injectHook(type, wrappedHook, target);
    if (target) {
      let current = target.parent;
      while (current && current.parent) {
        if (isKeepAlive(current.parent.vnode)) {
          injectToKeepAliveRoot(wrappedHook, type, target, current);
        }
        current = current.parent;
      }
    }
  }
  function injectToKeepAliveRoot(hook, type, target, keepAliveRoot) {
    const injected = injectHook(
      type,
      hook,
      keepAliveRoot,
      true
      /* prepend */
    );
    onUnmounted(() => {
      remove(keepAliveRoot[type], injected);
    }, target);
  }
  function injectHook(type, hook, target = currentInstance, prepend = false) {
    if (target) {
      const hooks = target[type] || (target[type] = []);
      const wrappedHook = hook.__weh || (hook.__weh = (...args) => {
        pauseTracking();
        const reset = setCurrentInstance(target);
        const res = callWithAsyncErrorHandling(hook, target, type, args);
        reset();
        resetTracking();
        return res;
      });
      if (prepend) {
        hooks.unshift(wrappedHook);
      } else {
        hooks.push(wrappedHook);
      }
      return wrappedHook;
    } else {
      const apiName = toHandlerKey(ErrorTypeStrings$1[type].replace(/ hook$/, ""));
      warn$1(
        `${apiName} is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.`
      );
    }
  }
  const createHook = (lifecycle) => (hook, target = currentInstance) => {
    if (!isInSSRComponentSetup || lifecycle === "sp") {
      injectHook(lifecycle, (...args) => hook(...args), target);
    }
  };
  const onBeforeMount = createHook("bm");
  const onMounted = createHook("m");
  const onBeforeUpdate = createHook(
    "bu"
  );
  const onUpdated = createHook("u");
  const onBeforeUnmount = createHook(
    "bum"
  );
  const onUnmounted = createHook("um");
  const onServerPrefetch = createHook(
    "sp"
  );
  const onRenderTriggered = createHook("rtg");
  const onRenderTracked = createHook("rtc");
  function onErrorCaptured(hook, target = currentInstance) {
    injectHook("ec", hook, target);
  }
  const NULL_DYNAMIC_COMPONENT = Symbol.for("v-ndc");
  function renderList(source, renderItem, cache, index) {
    let ret;
    const cached = cache;
    const sourceIsArray = isArray(source);
    if (sourceIsArray || isString(source)) {
      const sourceIsReactiveArray = sourceIsArray && isReactive(source);
      let needsWrap = false;
      if (sourceIsReactiveArray) {
        needsWrap = !isShallow(source);
        source = shallowReadArray(source);
      }
      ret = new Array(source.length);
      for (let i = 0, l = source.length; i < l; i++) {
        ret[i] = renderItem(
          needsWrap ? toReactive(source[i]) : source[i],
          i,
          void 0,
          cached
        );
      }
    } else if (typeof source === "number") {
      if (!Number.isInteger(source)) {
        warn$1(`The v-for range expect an integer value but got ${source}.`);
      }
      ret = new Array(source);
      for (let i = 0; i < source; i++) {
        ret[i] = renderItem(i + 1, i, void 0, cached);
      }
    } else if (isObject(source)) {
      if (source[Symbol.iterator]) {
        ret = Array.from(
          source,
          (item, i) => renderItem(item, i, void 0, cached)
        );
      } else {
        const keys = Object.keys(source);
        ret = new Array(keys.length);
        for (let i = 0, l = keys.length; i < l; i++) {
          const key = keys[i];
          ret[i] = renderItem(source[key], key, i, cached);
        }
      }
    } else {
      ret = [];
    }
    return ret;
  }
  function renderSlot(slots, name, props = {}, fallback, noSlotted) {
    if (currentRenderingInstance.ce || currentRenderingInstance.parent && isAsyncWrapper(currentRenderingInstance.parent) && currentRenderingInstance.parent.ce) {
      if (name !== "default") props.name = name;
      return openBlock(), createBlock(
        Fragment,
        null,
        [createVNode("slot", props, fallback && fallback())],
        64
      );
    }
    let slot = slots[name];
    if (slot && slot.length > 1) {
      warn$1(
        `SSR-optimized slot function detected in a non-SSR-optimized render function. You need to mark this component with $dynamic-slots in the parent template.`
      );
      slot = () => [];
    }
    if (slot && slot._c) {
      slot._d = false;
    }
    openBlock();
    const validSlotContent = slot && ensureValidVNode(slot(props));
    const slotKey = props.key || // slot content array of a dynamic conditional slot may have a branch
    // key attached in the `createSlots` helper, respect that
    validSlotContent && validSlotContent.key;
    const rendered = createBlock(
      Fragment,
      {
        key: (slotKey && !isSymbol(slotKey) ? slotKey : `_${name}`) + // #7256 force differentiate fallback content from actual content
        (!validSlotContent && fallback ? "_fb" : "")
      },
      validSlotContent || (fallback ? fallback() : []),
      validSlotContent && slots._ === 1 ? 64 : -2
    );
    if (!noSlotted && rendered.scopeId) {
      rendered.slotScopeIds = [rendered.scopeId + "-s"];
    }
    if (slot && slot._c) {
      slot._d = true;
    }
    return rendered;
  }
  function ensureValidVNode(vnodes) {
    return vnodes.some((child) => {
      if (!isVNode(child)) return true;
      if (child.type === Comment) return false;
      if (child.type === Fragment && !ensureValidVNode(child.children))
        return false;
      return true;
    }) ? vnodes : null;
  }
  const getPublicInstance = (i) => {
    if (!i) return null;
    if (isStatefulComponent(i)) return getComponentPublicInstance(i);
    return getPublicInstance(i.parent);
  };
  const publicPropertiesMap = (
    // Move PURE marker to new line to workaround compiler discarding it
    // due to type annotation
    /* @__PURE__ */ extend(/* @__PURE__ */ Object.create(null), {
      $: (i) => i,
      $el: (i) => i.vnode.el,
      $data: (i) => i.data,
      $props: (i) => shallowReadonly(i.props),
      $attrs: (i) => shallowReadonly(i.attrs),
      $slots: (i) => shallowReadonly(i.slots),
      $refs: (i) => shallowReadonly(i.refs),
      $parent: (i) => getPublicInstance(i.parent),
      $root: (i) => getPublicInstance(i.root),
      $host: (i) => i.ce,
      $emit: (i) => i.emit,
      $options: (i) => resolveMergedOptions(i),
      $forceUpdate: (i) => i.f || (i.f = () => {
        queueJob(i.update);
      }),
      $nextTick: (i) => i.n || (i.n = nextTick.bind(i.proxy)),
      $watch: (i) => instanceWatch.bind(i)
    })
  );
  const isReservedPrefix = (key) => key === "_" || key === "$";
  const hasSetupBinding = (state, key) => state !== EMPTY_OBJ && !state.__isScriptSetup && hasOwn(state, key);
  const PublicInstanceProxyHandlers = {
    get({ _: instance }, key) {
      if (key === "__v_skip") {
        return true;
      }
      const { ctx, setupState, data, props, accessCache, type, appContext } = instance;
      if (key === "__isVue") {
        return true;
      }
      let normalizedProps;
      if (key[0] !== "$") {
        const n = accessCache[key];
        if (n !== void 0) {
          switch (n) {
            case 1:
              return setupState[key];
            case 2:
              return data[key];
            case 4:
              return ctx[key];
            case 3:
              return props[key];
          }
        } else if (hasSetupBinding(setupState, key)) {
          accessCache[key] = 1;
          return setupState[key];
        } else if (data !== EMPTY_OBJ && hasOwn(data, key)) {
          accessCache[key] = 2;
          return data[key];
        } else if (
          // only cache other properties when instance has declared (thus stable)
          // props
          (normalizedProps = instance.propsOptions[0]) && hasOwn(normalizedProps, key)
        ) {
          accessCache[key] = 3;
          return props[key];
        } else if (ctx !== EMPTY_OBJ && hasOwn(ctx, key)) {
          accessCache[key] = 4;
          return ctx[key];
        } else if (shouldCacheAccess) {
          accessCache[key] = 0;
        }
      }
      const publicGetter = publicPropertiesMap[key];
      let cssModule, globalProperties;
      if (publicGetter) {
        if (key === "$attrs") {
          track(instance.attrs, "get", "");
          markAttrsAccessed();
        } else if (key === "$slots") {
          track(instance, "get", key);
        }
        return publicGetter(instance);
      } else if (
        // css module (injected by vue-loader)
        (cssModule = type.__cssModules) && (cssModule = cssModule[key])
      ) {
        return cssModule;
      } else if (ctx !== EMPTY_OBJ && hasOwn(ctx, key)) {
        accessCache[key] = 4;
        return ctx[key];
      } else if (
        // global properties
        globalProperties = appContext.config.globalProperties, hasOwn(globalProperties, key)
      ) {
        {
          return globalProperties[key];
        }
      } else if (currentRenderingInstance && (!isString(key) || // #1091 avoid internal isRef/isVNode checks on component instance leading
      // to infinite warning loop
      key.indexOf("__v") !== 0)) {
        if (data !== EMPTY_OBJ && isReservedPrefix(key[0]) && hasOwn(data, key)) {
          warn$1(
            `Property ${JSON.stringify(
              key
            )} must be accessed via $data because it starts with a reserved character ("$" or "_") and is not proxied on the render context.`
          );
        } else if (instance === currentRenderingInstance) {
          warn$1(
            `Property ${JSON.stringify(key)} was accessed during render but is not defined on instance.`
          );
        }
      }
    },
    set({ _: instance }, key, value) {
      const { data, setupState, ctx } = instance;
      if (hasSetupBinding(setupState, key)) {
        setupState[key] = value;
        return true;
      } else if (setupState.__isScriptSetup && hasOwn(setupState, key)) {
        warn$1(`Cannot mutate <script setup> binding "${key}" from Options API.`);
        return false;
      } else if (data !== EMPTY_OBJ && hasOwn(data, key)) {
        data[key] = value;
        return true;
      } else if (hasOwn(instance.props, key)) {
        warn$1(`Attempting to mutate prop "${key}". Props are readonly.`);
        return false;
      }
      if (key[0] === "$" && key.slice(1) in instance) {
        warn$1(
          `Attempting to mutate public property "${key}". Properties starting with $ are reserved and readonly.`
        );
        return false;
      } else {
        if (key in instance.appContext.config.globalProperties) {
          Object.defineProperty(ctx, key, {
            enumerable: true,
            configurable: true,
            value
          });
        } else {
          ctx[key] = value;
        }
      }
      return true;
    },
    has({
      _: { data, setupState, accessCache, ctx, appContext, propsOptions }
    }, key) {
      let normalizedProps;
      return !!accessCache[key] || data !== EMPTY_OBJ && hasOwn(data, key) || hasSetupBinding(setupState, key) || (normalizedProps = propsOptions[0]) && hasOwn(normalizedProps, key) || hasOwn(ctx, key) || hasOwn(publicPropertiesMap, key) || hasOwn(appContext.config.globalProperties, key);
    },
    defineProperty(target, key, descriptor) {
      if (descriptor.get != null) {
        target._.accessCache[key] = 0;
      } else if (hasOwn(descriptor, "value")) {
        this.set(target, key, descriptor.value, null);
      }
      return Reflect.defineProperty(target, key, descriptor);
    }
  };
  {
    PublicInstanceProxyHandlers.ownKeys = (target) => {
      warn$1(
        `Avoid app logic that relies on enumerating keys on a component instance. The keys will be empty in production mode to avoid performance overhead.`
      );
      return Reflect.ownKeys(target);
    };
  }
  function createDevRenderContext(instance) {
    const target = {};
    Object.defineProperty(target, `_`, {
      configurable: true,
      enumerable: false,
      get: () => instance
    });
    Object.keys(publicPropertiesMap).forEach((key) => {
      Object.defineProperty(target, key, {
        configurable: true,
        enumerable: false,
        get: () => publicPropertiesMap[key](instance),
        // intercepted by the proxy so no need for implementation,
        // but needed to prevent set errors
        set: NOOP
      });
    });
    return target;
  }
  function exposePropsOnRenderContext(instance) {
    const {
      ctx,
      propsOptions: [propsOptions]
    } = instance;
    if (propsOptions) {
      Object.keys(propsOptions).forEach((key) => {
        Object.defineProperty(ctx, key, {
          enumerable: true,
          configurable: true,
          get: () => instance.props[key],
          set: NOOP
        });
      });
    }
  }
  function exposeSetupStateOnRenderContext(instance) {
    const { ctx, setupState } = instance;
    Object.keys(toRaw(setupState)).forEach((key) => {
      if (!setupState.__isScriptSetup) {
        if (isReservedPrefix(key[0])) {
          warn$1(
            `setup() return property ${JSON.stringify(
              key
            )} should not start with "$" or "_" which are reserved prefixes for Vue internals.`
          );
          return;
        }
        Object.defineProperty(ctx, key, {
          enumerable: true,
          configurable: true,
          get: () => setupState[key],
          set: NOOP
        });
      }
    });
  }
  function normalizePropsOrEmits(props) {
    return isArray(props) ? props.reduce(
      (normalized, p2) => (normalized[p2] = null, normalized),
      {}
    ) : props;
  }
  function createDuplicateChecker() {
    const cache = /* @__PURE__ */ Object.create(null);
    return (type, key) => {
      if (cache[key]) {
        warn$1(`${type} property "${key}" is already defined in ${cache[key]}.`);
      } else {
        cache[key] = type;
      }
    };
  }
  let shouldCacheAccess = true;
  function applyOptions(instance) {
    const options = resolveMergedOptions(instance);
    const publicThis = instance.proxy;
    const ctx = instance.ctx;
    shouldCacheAccess = false;
    if (options.beforeCreate) {
      callHook(options.beforeCreate, instance, "bc");
    }
    const {
      // state
      data: dataOptions,
      computed: computedOptions,
      methods,
      watch: watchOptions,
      provide: provideOptions,
      inject: injectOptions,
      // lifecycle
      created,
      beforeMount,
      mounted,
      beforeUpdate,
      updated,
      activated,
      deactivated,
      beforeDestroy,
      beforeUnmount,
      destroyed,
      unmounted,
      render: render2,
      renderTracked,
      renderTriggered,
      errorCaptured,
      serverPrefetch,
      // public API
      expose,
      inheritAttrs,
      // assets
      components,
      directives,
      filters
    } = options;
    const checkDuplicateProperties = createDuplicateChecker();
    {
      const [propsOptions] = instance.propsOptions;
      if (propsOptions) {
        for (const key in propsOptions) {
          checkDuplicateProperties("Props", key);
        }
      }
    }
    if (injectOptions) {
      resolveInjections(injectOptions, ctx, checkDuplicateProperties);
    }
    if (methods) {
      for (const key in methods) {
        const methodHandler = methods[key];
        if (isFunction(methodHandler)) {
          {
            Object.defineProperty(ctx, key, {
              value: methodHandler.bind(publicThis),
              configurable: true,
              enumerable: true,
              writable: true
            });
          }
          {
            checkDuplicateProperties("Methods", key);
          }
        } else {
          warn$1(
            `Method "${key}" has type "${typeof methodHandler}" in the component definition. Did you reference the function correctly?`
          );
        }
      }
    }
    if (dataOptions) {
      if (!isFunction(dataOptions)) {
        warn$1(
          `The data option must be a function. Plain object usage is no longer supported.`
        );
      }
      const data = dataOptions.call(publicThis, publicThis);
      if (isPromise(data)) {
        warn$1(
          `data() returned a Promise - note data() cannot be async; If you intend to perform data fetching before component renders, use async setup() + <Suspense>.`
        );
      }
      if (!isObject(data)) {
        warn$1(`data() should return an object.`);
      } else {
        instance.data = reactive(data);
        {
          for (const key in data) {
            checkDuplicateProperties("Data", key);
            if (!isReservedPrefix(key[0])) {
              Object.defineProperty(ctx, key, {
                configurable: true,
                enumerable: true,
                get: () => data[key],
                set: NOOP
              });
            }
          }
        }
      }
    }
    shouldCacheAccess = true;
    if (computedOptions) {
      for (const key in computedOptions) {
        const opt = computedOptions[key];
        const get = isFunction(opt) ? opt.bind(publicThis, publicThis) : isFunction(opt.get) ? opt.get.bind(publicThis, publicThis) : NOOP;
        if (get === NOOP) {
          warn$1(`Computed property "${key}" has no getter.`);
        }
        const set = !isFunction(opt) && isFunction(opt.set) ? opt.set.bind(publicThis) : () => {
          warn$1(
            `Write operation failed: computed property "${key}" is readonly.`
          );
        };
        const c = computed({
          get,
          set
        });
        Object.defineProperty(ctx, key, {
          enumerable: true,
          configurable: true,
          get: () => c.value,
          set: (v) => c.value = v
        });
        {
          checkDuplicateProperties("Computed", key);
        }
      }
    }
    if (watchOptions) {
      for (const key in watchOptions) {
        createWatcher(watchOptions[key], ctx, publicThis, key);
      }
    }
    if (provideOptions) {
      const provides = isFunction(provideOptions) ? provideOptions.call(publicThis) : provideOptions;
      Reflect.ownKeys(provides).forEach((key) => {
        provide(key, provides[key]);
      });
    }
    if (created) {
      callHook(created, instance, "c");
    }
    function registerLifecycleHook(register, hook) {
      if (isArray(hook)) {
        hook.forEach((_hook) => register(_hook.bind(publicThis)));
      } else if (hook) {
        register(hook.bind(publicThis));
      }
    }
    registerLifecycleHook(onBeforeMount, beforeMount);
    registerLifecycleHook(onMounted, mounted);
    registerLifecycleHook(onBeforeUpdate, beforeUpdate);
    registerLifecycleHook(onUpdated, updated);
    registerLifecycleHook(onActivated, activated);
    registerLifecycleHook(onDeactivated, deactivated);
    registerLifecycleHook(onErrorCaptured, errorCaptured);
    registerLifecycleHook(onRenderTracked, renderTracked);
    registerLifecycleHook(onRenderTriggered, renderTriggered);
    registerLifecycleHook(onBeforeUnmount, beforeUnmount);
    registerLifecycleHook(onUnmounted, unmounted);
    registerLifecycleHook(onServerPrefetch, serverPrefetch);
    if (isArray(expose)) {
      if (expose.length) {
        const exposed = instance.exposed || (instance.exposed = {});
        expose.forEach((key) => {
          Object.defineProperty(exposed, key, {
            get: () => publicThis[key],
            set: (val) => publicThis[key] = val
          });
        });
      } else if (!instance.exposed) {
        instance.exposed = {};
      }
    }
    if (render2 && instance.render === NOOP) {
      instance.render = render2;
    }
    if (inheritAttrs != null) {
      instance.inheritAttrs = inheritAttrs;
    }
    if (components) instance.components = components;
    if (directives) instance.directives = directives;
    if (serverPrefetch) {
      markAsyncBoundary(instance);
    }
  }
  function resolveInjections(injectOptions, ctx, checkDuplicateProperties = NOOP) {
    if (isArray(injectOptions)) {
      injectOptions = normalizeInject(injectOptions);
    }
    for (const key in injectOptions) {
      const opt = injectOptions[key];
      let injected;
      if (isObject(opt)) {
        if ("default" in opt) {
          injected = inject(
            opt.from || key,
            opt.default,
            true
          );
        } else {
          injected = inject(opt.from || key);
        }
      } else {
        injected = inject(opt);
      }
      if (isRef(injected)) {
        Object.defineProperty(ctx, key, {
          enumerable: true,
          configurable: true,
          get: () => injected.value,
          set: (v) => injected.value = v
        });
      } else {
        ctx[key] = injected;
      }
      {
        checkDuplicateProperties("Inject", key);
      }
    }
  }
  function callHook(hook, instance, type) {
    callWithAsyncErrorHandling(
      isArray(hook) ? hook.map((h2) => h2.bind(instance.proxy)) : hook.bind(instance.proxy),
      instance,
      type
    );
  }
  function createWatcher(raw, ctx, publicThis, key) {
    let getter = key.includes(".") ? createPathGetter(publicThis, key) : () => publicThis[key];
    if (isString(raw)) {
      const handler = ctx[raw];
      if (isFunction(handler)) {
        {
          watch(getter, handler);
        }
      } else {
        warn$1(`Invalid watch handler specified by key "${raw}"`, handler);
      }
    } else if (isFunction(raw)) {
      {
        watch(getter, raw.bind(publicThis));
      }
    } else if (isObject(raw)) {
      if (isArray(raw)) {
        raw.forEach((r) => createWatcher(r, ctx, publicThis, key));
      } else {
        const handler = isFunction(raw.handler) ? raw.handler.bind(publicThis) : ctx[raw.handler];
        if (isFunction(handler)) {
          watch(getter, handler, raw);
        } else {
          warn$1(`Invalid watch handler specified by key "${raw.handler}"`, handler);
        }
      }
    } else {
      warn$1(`Invalid watch option: "${key}"`, raw);
    }
  }
  function resolveMergedOptions(instance) {
    const base = instance.type;
    const { mixins, extends: extendsOptions } = base;
    const {
      mixins: globalMixins,
      optionsCache: cache,
      config: { optionMergeStrategies }
    } = instance.appContext;
    const cached = cache.get(base);
    let resolved;
    if (cached) {
      resolved = cached;
    } else if (!globalMixins.length && !mixins && !extendsOptions) {
      {
        resolved = base;
      }
    } else {
      resolved = {};
      if (globalMixins.length) {
        globalMixins.forEach(
          (m) => mergeOptions(resolved, m, optionMergeStrategies, true)
        );
      }
      mergeOptions(resolved, base, optionMergeStrategies);
    }
    if (isObject(base)) {
      cache.set(base, resolved);
    }
    return resolved;
  }
  function mergeOptions(to, from, strats, asMixin = false) {
    const { mixins, extends: extendsOptions } = from;
    if (extendsOptions) {
      mergeOptions(to, extendsOptions, strats, true);
    }
    if (mixins) {
      mixins.forEach(
        (m) => mergeOptions(to, m, strats, true)
      );
    }
    for (const key in from) {
      if (asMixin && key === "expose") {
        warn$1(
          `"expose" option is ignored when declared in mixins or extends. It should only be declared in the base component itself.`
        );
      } else {
        const strat = internalOptionMergeStrats[key] || strats && strats[key];
        to[key] = strat ? strat(to[key], from[key]) : from[key];
      }
    }
    return to;
  }
  const internalOptionMergeStrats = {
    data: mergeDataFn,
    props: mergeEmitsOrPropsOptions,
    emits: mergeEmitsOrPropsOptions,
    // objects
    methods: mergeObjectOptions,
    computed: mergeObjectOptions,
    // lifecycle
    beforeCreate: mergeAsArray,
    created: mergeAsArray,
    beforeMount: mergeAsArray,
    mounted: mergeAsArray,
    beforeUpdate: mergeAsArray,
    updated: mergeAsArray,
    beforeDestroy: mergeAsArray,
    beforeUnmount: mergeAsArray,
    destroyed: mergeAsArray,
    unmounted: mergeAsArray,
    activated: mergeAsArray,
    deactivated: mergeAsArray,
    errorCaptured: mergeAsArray,
    serverPrefetch: mergeAsArray,
    // assets
    components: mergeObjectOptions,
    directives: mergeObjectOptions,
    // watch
    watch: mergeWatchOptions,
    // provide / inject
    provide: mergeDataFn,
    inject: mergeInject
  };
  function mergeDataFn(to, from) {
    if (!from) {
      return to;
    }
    if (!to) {
      return from;
    }
    return function mergedDataFn() {
      return extend(
        isFunction(to) ? to.call(this, this) : to,
        isFunction(from) ? from.call(this, this) : from
      );
    };
  }
  function mergeInject(to, from) {
    return mergeObjectOptions(normalizeInject(to), normalizeInject(from));
  }
  function normalizeInject(raw) {
    if (isArray(raw)) {
      const res = {};
      for (let i = 0; i < raw.length; i++) {
        res[raw[i]] = raw[i];
      }
      return res;
    }
    return raw;
  }
  function mergeAsArray(to, from) {
    return to ? [...new Set([].concat(to, from))] : from;
  }
  function mergeObjectOptions(to, from) {
    return to ? extend(/* @__PURE__ */ Object.create(null), to, from) : from;
  }
  function mergeEmitsOrPropsOptions(to, from) {
    if (to) {
      if (isArray(to) && isArray(from)) {
        return [.../* @__PURE__ */ new Set([...to, ...from])];
      }
      return extend(
        /* @__PURE__ */ Object.create(null),
        normalizePropsOrEmits(to),
        normalizePropsOrEmits(from != null ? from : {})
      );
    } else {
      return from;
    }
  }
  function mergeWatchOptions(to, from) {
    if (!to) return from;
    if (!from) return to;
    const merged = extend(/* @__PURE__ */ Object.create(null), to);
    for (const key in from) {
      merged[key] = mergeAsArray(to[key], from[key]);
    }
    return merged;
  }
  function createAppContext() {
    return {
      app: null,
      config: {
        isNativeTag: NO,
        performance: false,
        globalProperties: {},
        optionMergeStrategies: {},
        errorHandler: void 0,
        warnHandler: void 0,
        compilerOptions: {}
      },
      mixins: [],
      components: {},
      directives: {},
      provides: /* @__PURE__ */ Object.create(null),
      optionsCache: /* @__PURE__ */ new WeakMap(),
      propsCache: /* @__PURE__ */ new WeakMap(),
      emitsCache: /* @__PURE__ */ new WeakMap()
    };
  }
  let uid$1 = 0;
  function createAppAPI(render2, hydrate) {
    return function createApp2(rootComponent, rootProps = null) {
      if (!isFunction(rootComponent)) {
        rootComponent = extend({}, rootComponent);
      }
      if (rootProps != null && !isObject(rootProps)) {
        warn$1(`root props passed to app.mount() must be an object.`);
        rootProps = null;
      }
      const context = createAppContext();
      const installedPlugins = /* @__PURE__ */ new WeakSet();
      const pluginCleanupFns = [];
      let isMounted = false;
      const app2 = context.app = {
        _uid: uid$1++,
        _component: rootComponent,
        _props: rootProps,
        _container: null,
        _context: context,
        _instance: null,
        version,
        get config() {
          return context.config;
        },
        set config(v) {
          {
            warn$1(
              `app.config cannot be replaced. Modify individual options instead.`
            );
          }
        },
        use(plugin, ...options) {
          if (installedPlugins.has(plugin)) {
            warn$1(`Plugin has already been applied to target app.`);
          } else if (plugin && isFunction(plugin.install)) {
            installedPlugins.add(plugin);
            plugin.install(app2, ...options);
          } else if (isFunction(plugin)) {
            installedPlugins.add(plugin);
            plugin(app2, ...options);
          } else {
            warn$1(
              `A plugin must either be a function or an object with an "install" function.`
            );
          }
          return app2;
        },
        mixin(mixin) {
          {
            if (!context.mixins.includes(mixin)) {
              context.mixins.push(mixin);
            } else {
              warn$1(
                "Mixin has already been applied to target app" + (mixin.name ? `: ${mixin.name}` : "")
              );
            }
          }
          return app2;
        },
        component(name, component) {
          {
            validateComponentName(name, context.config);
          }
          if (!component) {
            return context.components[name];
          }
          if (context.components[name]) {
            warn$1(`Component "${name}" has already been registered in target app.`);
          }
          context.components[name] = component;
          return app2;
        },
        directive(name, directive) {
          {
            validateDirectiveName(name);
          }
          if (!directive) {
            return context.directives[name];
          }
          if (context.directives[name]) {
            warn$1(`Directive "${name}" has already been registered in target app.`);
          }
          context.directives[name] = directive;
          return app2;
        },
        mount(rootContainer, isHydrate, namespace) {
          if (!isMounted) {
            if (rootContainer.__vue_app__) {
              warn$1(
                `There is already an app instance mounted on the host container.
 If you want to mount another app on the same host container, you need to unmount the previous app by calling \`app.unmount()\` first.`
              );
            }
            const vnode = app2._ceVNode || createVNode(rootComponent, rootProps);
            vnode.appContext = context;
            if (namespace === true) {
              namespace = "svg";
            } else if (namespace === false) {
              namespace = void 0;
            }
            {
              context.reload = () => {
                render2(
                  cloneVNode(vnode),
                  rootContainer,
                  namespace
                );
              };
            }
            {
              render2(vnode, rootContainer, namespace);
            }
            isMounted = true;
            app2._container = rootContainer;
            rootContainer.__vue_app__ = app2;
            {
              app2._instance = vnode.component;
              devtoolsInitApp(app2, version);
            }
            return getComponentPublicInstance(vnode.component);
          } else {
            warn$1(
              `App has already been mounted.
If you want to remount the same app, move your app creation logic into a factory function and create fresh app instances for each mount - e.g. \`const createMyApp = () => createApp(App)\``
            );
          }
        },
        onUnmount(cleanupFn) {
          if (typeof cleanupFn !== "function") {
            warn$1(
              `Expected function as first argument to app.onUnmount(), but got ${typeof cleanupFn}`
            );
          }
          pluginCleanupFns.push(cleanupFn);
        },
        unmount() {
          if (isMounted) {
            callWithAsyncErrorHandling(
              pluginCleanupFns,
              app2._instance,
              16
            );
            render2(null, app2._container);
            {
              app2._instance = null;
              devtoolsUnmountApp(app2);
            }
            delete app2._container.__vue_app__;
          } else {
            warn$1(`Cannot unmount an app that is not mounted.`);
          }
        },
        provide(key, value) {
          if (key in context.provides) {
            warn$1(
              `App already provides property with key "${String(key)}". It will be overwritten with the new value.`
            );
          }
          context.provides[key] = value;
          return app2;
        },
        runWithContext(fn) {
          const lastApp = currentApp;
          currentApp = app2;
          try {
            return fn();
          } finally {
            currentApp = lastApp;
          }
        }
      };
      return app2;
    };
  }
  let currentApp = null;
  function provide(key, value) {
    if (!currentInstance) {
      {
        warn$1(`provide() can only be used inside setup().`);
      }
    } else {
      let provides = currentInstance.provides;
      const parentProvides = currentInstance.parent && currentInstance.parent.provides;
      if (parentProvides === provides) {
        provides = currentInstance.provides = Object.create(parentProvides);
      }
      provides[key] = value;
    }
  }
  function inject(key, defaultValue, treatDefaultAsFactory = false) {
    const instance = currentInstance || currentRenderingInstance;
    if (instance || currentApp) {
      const provides = currentApp ? currentApp._context.provides : instance ? instance.parent == null ? instance.vnode.appContext && instance.vnode.appContext.provides : instance.parent.provides : void 0;
      if (provides && key in provides) {
        return provides[key];
      } else if (arguments.length > 1) {
        return treatDefaultAsFactory && isFunction(defaultValue) ? defaultValue.call(instance && instance.proxy) : defaultValue;
      } else {
        warn$1(`injection "${String(key)}" not found.`);
      }
    } else {
      warn$1(`inject() can only be used inside setup() or functional components.`);
    }
  }
  const internalObjectProto = {};
  const createInternalObject = () => Object.create(internalObjectProto);
  const isInternalObject = (obj) => Object.getPrototypeOf(obj) === internalObjectProto;
  function initProps(instance, rawProps, isStateful, isSSR = false) {
    const props = {};
    const attrs = createInternalObject();
    instance.propsDefaults = /* @__PURE__ */ Object.create(null);
    setFullProps(instance, rawProps, props, attrs);
    for (const key in instance.propsOptions[0]) {
      if (!(key in props)) {
        props[key] = void 0;
      }
    }
    {
      validateProps(rawProps || {}, props, instance);
    }
    if (isStateful) {
      instance.props = isSSR ? props : shallowReactive(props);
    } else {
      if (!instance.type.props) {
        instance.props = attrs;
      } else {
        instance.props = props;
      }
    }
    instance.attrs = attrs;
  }
  function isInHmrContext(instance) {
    while (instance) {
      if (instance.type.__hmrId) return true;
      instance = instance.parent;
    }
  }
  function updateProps(instance, rawProps, rawPrevProps, optimized) {
    const {
      props,
      attrs,
      vnode: { patchFlag }
    } = instance;
    const rawCurrentProps = toRaw(props);
    const [options] = instance.propsOptions;
    let hasAttrsChanged = false;
    if (
      // always force full diff in dev
      // - #1942 if hmr is enabled with sfc component
      // - vite#872 non-sfc component used by sfc component
      !isInHmrContext(instance) && (optimized || patchFlag > 0) && !(patchFlag & 16)
    ) {
      if (patchFlag & 8) {
        const propsToUpdate = instance.vnode.dynamicProps;
        for (let i = 0; i < propsToUpdate.length; i++) {
          let key = propsToUpdate[i];
          if (isEmitListener(instance.emitsOptions, key)) {
            continue;
          }
          const value = rawProps[key];
          if (options) {
            if (hasOwn(attrs, key)) {
              if (value !== attrs[key]) {
                attrs[key] = value;
                hasAttrsChanged = true;
              }
            } else {
              const camelizedKey = camelize(key);
              props[camelizedKey] = resolvePropValue(
                options,
                rawCurrentProps,
                camelizedKey,
                value,
                instance,
                false
              );
            }
          } else {
            if (value !== attrs[key]) {
              attrs[key] = value;
              hasAttrsChanged = true;
            }
          }
        }
      }
    } else {
      if (setFullProps(instance, rawProps, props, attrs)) {
        hasAttrsChanged = true;
      }
      let kebabKey;
      for (const key in rawCurrentProps) {
        if (!rawProps || // for camelCase
        !hasOwn(rawProps, key) && // it's possible the original props was passed in as kebab-case
        // and converted to camelCase (#955)
        ((kebabKey = hyphenate(key)) === key || !hasOwn(rawProps, kebabKey))) {
          if (options) {
            if (rawPrevProps && // for camelCase
            (rawPrevProps[key] !== void 0 || // for kebab-case
            rawPrevProps[kebabKey] !== void 0)) {
              props[key] = resolvePropValue(
                options,
                rawCurrentProps,
                key,
                void 0,
                instance,
                true
              );
            }
          } else {
            delete props[key];
          }
        }
      }
      if (attrs !== rawCurrentProps) {
        for (const key in attrs) {
          if (!rawProps || !hasOwn(rawProps, key) && true) {
            delete attrs[key];
            hasAttrsChanged = true;
          }
        }
      }
    }
    if (hasAttrsChanged) {
      trigger(instance.attrs, "set", "");
    }
    {
      validateProps(rawProps || {}, props, instance);
    }
  }
  function setFullProps(instance, rawProps, props, attrs) {
    const [options, needCastKeys] = instance.propsOptions;
    let hasAttrsChanged = false;
    let rawCastValues;
    if (rawProps) {
      for (let key in rawProps) {
        if (isReservedProp(key)) {
          continue;
        }
        const value = rawProps[key];
        let camelKey;
        if (options && hasOwn(options, camelKey = camelize(key))) {
          if (!needCastKeys || !needCastKeys.includes(camelKey)) {
            props[camelKey] = value;
          } else {
            (rawCastValues || (rawCastValues = {}))[camelKey] = value;
          }
        } else if (!isEmitListener(instance.emitsOptions, key)) {
          if (!(key in attrs) || value !== attrs[key]) {
            attrs[key] = value;
            hasAttrsChanged = true;
          }
        }
      }
    }
    if (needCastKeys) {
      const rawCurrentProps = toRaw(props);
      const castValues = rawCastValues || EMPTY_OBJ;
      for (let i = 0; i < needCastKeys.length; i++) {
        const key = needCastKeys[i];
        props[key] = resolvePropValue(
          options,
          rawCurrentProps,
          key,
          castValues[key],
          instance,
          !hasOwn(castValues, key)
        );
      }
    }
    return hasAttrsChanged;
  }
  function resolvePropValue(options, props, key, value, instance, isAbsent) {
    const opt = options[key];
    if (opt != null) {
      const hasDefault = hasOwn(opt, "default");
      if (hasDefault && value === void 0) {
        const defaultValue = opt.default;
        if (opt.type !== Function && !opt.skipFactory && isFunction(defaultValue)) {
          const { propsDefaults } = instance;
          if (key in propsDefaults) {
            value = propsDefaults[key];
          } else {
            const reset = setCurrentInstance(instance);
            value = propsDefaults[key] = defaultValue.call(
              null,
              props
            );
            reset();
          }
        } else {
          value = defaultValue;
        }
        if (instance.ce) {
          instance.ce._setProp(key, value);
        }
      }
      if (opt[
        0
        /* shouldCast */
      ]) {
        if (isAbsent && !hasDefault) {
          value = false;
        } else if (opt[
          1
          /* shouldCastTrue */
        ] && (value === "" || value === hyphenate(key))) {
          value = true;
        }
      }
    }
    return value;
  }
  const mixinPropsCache = /* @__PURE__ */ new WeakMap();
  function normalizePropsOptions(comp, appContext, asMixin = false) {
    const cache = asMixin ? mixinPropsCache : appContext.propsCache;
    const cached = cache.get(comp);
    if (cached) {
      return cached;
    }
    const raw = comp.props;
    const normalized = {};
    const needCastKeys = [];
    let hasExtends = false;
    if (!isFunction(comp)) {
      const extendProps = (raw2) => {
        hasExtends = true;
        const [props, keys] = normalizePropsOptions(raw2, appContext, true);
        extend(normalized, props);
        if (keys) needCastKeys.push(...keys);
      };
      if (!asMixin && appContext.mixins.length) {
        appContext.mixins.forEach(extendProps);
      }
      if (comp.extends) {
        extendProps(comp.extends);
      }
      if (comp.mixins) {
        comp.mixins.forEach(extendProps);
      }
    }
    if (!raw && !hasExtends) {
      if (isObject(comp)) {
        cache.set(comp, EMPTY_ARR);
      }
      return EMPTY_ARR;
    }
    if (isArray(raw)) {
      for (let i = 0; i < raw.length; i++) {
        if (!isString(raw[i])) {
          warn$1(`props must be strings when using array syntax.`, raw[i]);
        }
        const normalizedKey = camelize(raw[i]);
        if (validatePropName(normalizedKey)) {
          normalized[normalizedKey] = EMPTY_OBJ;
        }
      }
    } else if (raw) {
      if (!isObject(raw)) {
        warn$1(`invalid props options`, raw);
      }
      for (const key in raw) {
        const normalizedKey = camelize(key);
        if (validatePropName(normalizedKey)) {
          const opt = raw[key];
          const prop = normalized[normalizedKey] = isArray(opt) || isFunction(opt) ? { type: opt } : extend({}, opt);
          const propType = prop.type;
          let shouldCast = false;
          let shouldCastTrue = true;
          if (isArray(propType)) {
            for (let index = 0; index < propType.length; ++index) {
              const type = propType[index];
              const typeName = isFunction(type) && type.name;
              if (typeName === "Boolean") {
                shouldCast = true;
                break;
              } else if (typeName === "String") {
                shouldCastTrue = false;
              }
            }
          } else {
            shouldCast = isFunction(propType) && propType.name === "Boolean";
          }
          prop[
            0
            /* shouldCast */
          ] = shouldCast;
          prop[
            1
            /* shouldCastTrue */
          ] = shouldCastTrue;
          if (shouldCast || hasOwn(prop, "default")) {
            needCastKeys.push(normalizedKey);
          }
        }
      }
    }
    const res = [normalized, needCastKeys];
    if (isObject(comp)) {
      cache.set(comp, res);
    }
    return res;
  }
  function validatePropName(key) {
    if (key[0] !== "$" && !isReservedProp(key)) {
      return true;
    } else {
      warn$1(`Invalid prop name: "${key}" is a reserved property.`);
    }
    return false;
  }
  function getType(ctor) {
    if (ctor === null) {
      return "null";
    }
    if (typeof ctor === "function") {
      return ctor.name || "";
    } else if (typeof ctor === "object") {
      const name = ctor.constructor && ctor.constructor.name;
      return name || "";
    }
    return "";
  }
  function validateProps(rawProps, props, instance) {
    const resolvedValues = toRaw(props);
    const options = instance.propsOptions[0];
    const camelizePropsKey = Object.keys(rawProps).map((key) => camelize(key));
    for (const key in options) {
      let opt = options[key];
      if (opt == null) continue;
      validateProp(
        key,
        resolvedValues[key],
        opt,
        shallowReadonly(resolvedValues),
        !camelizePropsKey.includes(key)
      );
    }
  }
  function validateProp(name, value, prop, props, isAbsent) {
    const { type, required, validator, skipCheck } = prop;
    if (required && isAbsent) {
      warn$1('Missing required prop: "' + name + '"');
      return;
    }
    if (value == null && !required) {
      return;
    }
    if (type != null && type !== true && !skipCheck) {
      let isValid = false;
      const types = isArray(type) ? type : [type];
      const expectedTypes = [];
      for (let i = 0; i < types.length && !isValid; i++) {
        const { valid, expectedType } = assertType(value, types[i]);
        expectedTypes.push(expectedType || "");
        isValid = valid;
      }
      if (!isValid) {
        warn$1(getInvalidTypeMessage(name, value, expectedTypes));
        return;
      }
    }
    if (validator && !validator(value, props)) {
      warn$1('Invalid prop: custom validator check failed for prop "' + name + '".');
    }
  }
  const isSimpleType = /* @__PURE__ */ makeMap(
    "String,Number,Boolean,Function,Symbol,BigInt"
  );
  function assertType(value, type) {
    let valid;
    const expectedType = getType(type);
    if (expectedType === "null") {
      valid = value === null;
    } else if (isSimpleType(expectedType)) {
      const t = typeof value;
      valid = t === expectedType.toLowerCase();
      if (!valid && t === "object") {
        valid = value instanceof type;
      }
    } else if (expectedType === "Object") {
      valid = isObject(value);
    } else if (expectedType === "Array") {
      valid = isArray(value);
    } else {
      valid = value instanceof type;
    }
    return {
      valid,
      expectedType
    };
  }
  function getInvalidTypeMessage(name, value, expectedTypes) {
    if (expectedTypes.length === 0) {
      return `Prop type [] for prop "${name}" won't match anything. Did you mean to use type Array instead?`;
    }
    let message = `Invalid prop: type check failed for prop "${name}". Expected ${expectedTypes.map(capitalize).join(" | ")}`;
    const expectedType = expectedTypes[0];
    const receivedType = toRawType(value);
    const expectedValue = styleValue(value, expectedType);
    const receivedValue = styleValue(value, receivedType);
    if (expectedTypes.length === 1 && isExplicable(expectedType) && !isBoolean(expectedType, receivedType)) {
      message += ` with value ${expectedValue}`;
    }
    message += `, got ${receivedType} `;
    if (isExplicable(receivedType)) {
      message += `with value ${receivedValue}.`;
    }
    return message;
  }
  function styleValue(value, type) {
    if (type === "String") {
      return `"${value}"`;
    } else if (type === "Number") {
      return `${Number(value)}`;
    } else {
      return `${value}`;
    }
  }
  function isExplicable(type) {
    const explicitTypes = ["string", "number", "boolean"];
    return explicitTypes.some((elem) => type.toLowerCase() === elem);
  }
  function isBoolean(...args) {
    return args.some((elem) => elem.toLowerCase() === "boolean");
  }
  const isInternalKey = (key) => key[0] === "_" || key === "$stable";
  const normalizeSlotValue = (value) => isArray(value) ? value.map(normalizeVNode) : [normalizeVNode(value)];
  const normalizeSlot = (key, rawSlot, ctx) => {
    if (rawSlot._n) {
      return rawSlot;
    }
    const normalized = withCtx((...args) => {
      if (currentInstance && (!ctx || ctx.root === currentInstance.root)) {
        warn$1(
          `Slot "${key}" invoked outside of the render function: this will not track dependencies used in the slot. Invoke the slot function inside the render function instead.`
        );
      }
      return normalizeSlotValue(rawSlot(...args));
    }, ctx);
    normalized._c = false;
    return normalized;
  };
  const normalizeObjectSlots = (rawSlots, slots, instance) => {
    const ctx = rawSlots._ctx;
    for (const key in rawSlots) {
      if (isInternalKey(key)) continue;
      const value = rawSlots[key];
      if (isFunction(value)) {
        slots[key] = normalizeSlot(key, value, ctx);
      } else if (value != null) {
        {
          warn$1(
            `Non-function value encountered for slot "${key}". Prefer function slots for better performance.`
          );
        }
        const normalized = normalizeSlotValue(value);
        slots[key] = () => normalized;
      }
    }
  };
  const normalizeVNodeSlots = (instance, children) => {
    if (!isKeepAlive(instance.vnode) && true) {
      warn$1(
        `Non-function value encountered for default slot. Prefer function slots for better performance.`
      );
    }
    const normalized = normalizeSlotValue(children);
    instance.slots.default = () => normalized;
  };
  const assignSlots = (slots, children, optimized) => {
    for (const key in children) {
      if (optimized || key !== "_") {
        slots[key] = children[key];
      }
    }
  };
  const initSlots = (instance, children, optimized) => {
    const slots = instance.slots = createInternalObject();
    if (instance.vnode.shapeFlag & 32) {
      const type = children._;
      if (type) {
        assignSlots(slots, children, optimized);
        if (optimized) {
          def(slots, "_", type, true);
        }
      } else {
        normalizeObjectSlots(children, slots);
      }
    } else if (children) {
      normalizeVNodeSlots(instance, children);
    }
  };
  const updateSlots = (instance, children, optimized) => {
    const { vnode, slots } = instance;
    let needDeletionCheck = true;
    let deletionComparisonTarget = EMPTY_OBJ;
    if (vnode.shapeFlag & 32) {
      const type = children._;
      if (type) {
        if (isHmrUpdating) {
          assignSlots(slots, children, optimized);
          trigger(instance, "set", "$slots");
        } else if (optimized && type === 1) {
          needDeletionCheck = false;
        } else {
          assignSlots(slots, children, optimized);
        }
      } else {
        needDeletionCheck = !children.$stable;
        normalizeObjectSlots(children, slots);
      }
      deletionComparisonTarget = children;
    } else if (children) {
      normalizeVNodeSlots(instance, children);
      deletionComparisonTarget = { default: 1 };
    }
    if (needDeletionCheck) {
      for (const key in slots) {
        if (!isInternalKey(key) && deletionComparisonTarget[key] == null) {
          delete slots[key];
        }
      }
    }
  };
  let supported;
  let perf;
  function startMeasure(instance, type) {
    if (instance.appContext.config.performance && isSupported()) {
      perf.mark(`vue-${type}-${instance.uid}`);
    }
    {
      devtoolsPerfStart(instance, type, isSupported() ? perf.now() : Date.now());
    }
  }
  function endMeasure(instance, type) {
    if (instance.appContext.config.performance && isSupported()) {
      const startTag = `vue-${type}-${instance.uid}`;
      const endTag = startTag + `:end`;
      perf.mark(endTag);
      perf.measure(
        `<${formatComponentName(instance, instance.type)}> ${type}`,
        startTag,
        endTag
      );
      perf.clearMarks(startTag);
      perf.clearMarks(endTag);
    }
    {
      devtoolsPerfEnd(instance, type, isSupported() ? perf.now() : Date.now());
    }
  }
  function isSupported() {
    if (supported !== void 0) {
      return supported;
    }
    if (typeof window !== "undefined" && window.performance) {
      supported = true;
      perf = window.performance;
    } else {
      supported = false;
    }
    return supported;
  }
  function initFeatureFlags() {
    const needWarn = [];
    if (needWarn.length) {
      const multi = needWarn.length > 1;
      console.warn(
        `Feature flag${multi ? `s` : ``} ${needWarn.join(", ")} ${multi ? `are` : `is`} not explicitly defined. You are running the esm-bundler build of Vue, which expects these compile-time feature flags to be globally injected via the bundler config in order to get better tree-shaking in the production bundle.

For more details, see https://link.vuejs.org/feature-flags.`
      );
    }
  }
  const queuePostRenderEffect = queueEffectWithSuspense;
  function createRenderer(options) {
    return baseCreateRenderer(options);
  }
  function baseCreateRenderer(options, createHydrationFns) {
    {
      initFeatureFlags();
    }
    const target = getGlobalThis();
    target.__VUE__ = true;
    {
      setDevtoolsHook$1(target.__VUE_DEVTOOLS_GLOBAL_HOOK__, target);
    }
    const {
      insert: hostInsert,
      remove: hostRemove,
      patchProp: hostPatchProp,
      createElement: hostCreateElement,
      createText: hostCreateText,
      createComment: hostCreateComment,
      setText: hostSetText,
      setElementText: hostSetElementText,
      parentNode: hostParentNode,
      nextSibling: hostNextSibling,
      setScopeId: hostSetScopeId = NOOP,
      insertStaticContent: hostInsertStaticContent
    } = options;
    const patch = (n1, n2, container, anchor = null, parentComponent = null, parentSuspense = null, namespace = void 0, slotScopeIds = null, optimized = isHmrUpdating ? false : !!n2.dynamicChildren) => {
      if (n1 === n2) {
        return;
      }
      if (n1 && !isSameVNodeType(n1, n2)) {
        anchor = getNextHostNode(n1);
        unmount(n1, parentComponent, parentSuspense, true);
        n1 = null;
      }
      if (n2.patchFlag === -2) {
        optimized = false;
        n2.dynamicChildren = null;
      }
      const { type, ref: ref3, shapeFlag } = n2;
      switch (type) {
        case Text:
          processText(n1, n2, container, anchor);
          break;
        case Comment:
          processCommentNode(n1, n2, container, anchor);
          break;
        case Static:
          if (n1 == null) {
            mountStaticNode(n2, container, anchor, namespace);
          } else {
            patchStaticNode(n1, n2, container, namespace);
          }
          break;
        case Fragment:
          processFragment(
            n1,
            n2,
            container,
            anchor,
            parentComponent,
            parentSuspense,
            namespace,
            slotScopeIds,
            optimized
          );
          break;
        default:
          if (shapeFlag & 1) {
            processElement(
              n1,
              n2,
              container,
              anchor,
              parentComponent,
              parentSuspense,
              namespace,
              slotScopeIds,
              optimized
            );
          } else if (shapeFlag & 6) {
            processComponent(
              n1,
              n2,
              container,
              anchor,
              parentComponent,
              parentSuspense,
              namespace,
              slotScopeIds,
              optimized
            );
          } else if (shapeFlag & 64) {
            type.process(
              n1,
              n2,
              container,
              anchor,
              parentComponent,
              parentSuspense,
              namespace,
              slotScopeIds,
              optimized,
              internals
            );
          } else if (shapeFlag & 128) {
            type.process(
              n1,
              n2,
              container,
              anchor,
              parentComponent,
              parentSuspense,
              namespace,
              slotScopeIds,
              optimized,
              internals
            );
          } else {
            warn$1("Invalid VNode type:", type, `(${typeof type})`);
          }
      }
      if (ref3 != null && parentComponent) {
        setRef(ref3, n1 && n1.ref, parentSuspense, n2 || n1, !n2);
      }
    };
    const processText = (n1, n2, container, anchor) => {
      if (n1 == null) {
        hostInsert(
          n2.el = hostCreateText(n2.children),
          container,
          anchor
        );
      } else {
        const el = n2.el = n1.el;
        if (n2.children !== n1.children) {
          hostSetText(el, n2.children);
        }
      }
    };
    const processCommentNode = (n1, n2, container, anchor) => {
      if (n1 == null) {
        hostInsert(
          n2.el = hostCreateComment(n2.children || ""),
          container,
          anchor
        );
      } else {
        n2.el = n1.el;
      }
    };
    const mountStaticNode = (n2, container, anchor, namespace) => {
      [n2.el, n2.anchor] = hostInsertStaticContent(
        n2.children,
        container,
        anchor,
        namespace,
        n2.el,
        n2.anchor
      );
    };
    const patchStaticNode = (n1, n2, container, namespace) => {
      if (n2.children !== n1.children) {
        const anchor = hostNextSibling(n1.anchor);
        removeStaticNode(n1);
        [n2.el, n2.anchor] = hostInsertStaticContent(
          n2.children,
          container,
          anchor,
          namespace
        );
      } else {
        n2.el = n1.el;
        n2.anchor = n1.anchor;
      }
    };
    const moveStaticNode = ({ el, anchor }, container, nextSibling) => {
      let next;
      while (el && el !== anchor) {
        next = hostNextSibling(el);
        hostInsert(el, container, nextSibling);
        el = next;
      }
      hostInsert(anchor, container, nextSibling);
    };
    const removeStaticNode = ({ el, anchor }) => {
      let next;
      while (el && el !== anchor) {
        next = hostNextSibling(el);
        hostRemove(el);
        el = next;
      }
      hostRemove(anchor);
    };
    const processElement = (n1, n2, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized) => {
      if (n2.type === "svg") {
        namespace = "svg";
      } else if (n2.type === "math") {
        namespace = "mathml";
      }
      if (n1 == null) {
        mountElement(
          n2,
          container,
          anchor,
          parentComponent,
          parentSuspense,
          namespace,
          slotScopeIds,
          optimized
        );
      } else {
        patchElement(
          n1,
          n2,
          parentComponent,
          parentSuspense,
          namespace,
          slotScopeIds,
          optimized
        );
      }
    };
    const mountElement = (vnode, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized) => {
      let el;
      let vnodeHook;
      const { props, shapeFlag, transition, dirs } = vnode;
      el = vnode.el = hostCreateElement(
        vnode.type,
        namespace,
        props && props.is,
        props
      );
      if (shapeFlag & 8) {
        hostSetElementText(el, vnode.children);
      } else if (shapeFlag & 16) {
        mountChildren(
          vnode.children,
          el,
          null,
          parentComponent,
          parentSuspense,
          resolveChildrenNamespace(vnode, namespace),
          slotScopeIds,
          optimized
        );
      }
      if (dirs) {
        invokeDirectiveHook(vnode, null, parentComponent, "created");
      }
      setScopeId(el, vnode, vnode.scopeId, slotScopeIds, parentComponent);
      if (props) {
        for (const key in props) {
          if (key !== "value" && !isReservedProp(key)) {
            hostPatchProp(el, key, null, props[key], namespace, parentComponent);
          }
        }
        if ("value" in props) {
          hostPatchProp(el, "value", null, props.value, namespace);
        }
        if (vnodeHook = props.onVnodeBeforeMount) {
          invokeVNodeHook(vnodeHook, parentComponent, vnode);
        }
      }
      {
        def(el, "__vnode", vnode, true);
        def(el, "__vueParentComponent", parentComponent, true);
      }
      if (dirs) {
        invokeDirectiveHook(vnode, null, parentComponent, "beforeMount");
      }
      const needCallTransitionHooks = needTransition(parentSuspense, transition);
      if (needCallTransitionHooks) {
        transition.beforeEnter(el);
      }
      hostInsert(el, container, anchor);
      if ((vnodeHook = props && props.onVnodeMounted) || needCallTransitionHooks || dirs) {
        queuePostRenderEffect(() => {
          vnodeHook && invokeVNodeHook(vnodeHook, parentComponent, vnode);
          needCallTransitionHooks && transition.enter(el);
          dirs && invokeDirectiveHook(vnode, null, parentComponent, "mounted");
        }, parentSuspense);
      }
    };
    const setScopeId = (el, vnode, scopeId, slotScopeIds, parentComponent) => {
      if (scopeId) {
        hostSetScopeId(el, scopeId);
      }
      if (slotScopeIds) {
        for (let i = 0; i < slotScopeIds.length; i++) {
          hostSetScopeId(el, slotScopeIds[i]);
        }
      }
      if (parentComponent) {
        let subTree = parentComponent.subTree;
        if (subTree.patchFlag > 0 && subTree.patchFlag & 2048) {
          subTree = filterSingleRoot(subTree.children) || subTree;
        }
        if (vnode === subTree || isSuspense(subTree.type) && (subTree.ssContent === vnode || subTree.ssFallback === vnode)) {
          const parentVNode = parentComponent.vnode;
          setScopeId(
            el,
            parentVNode,
            parentVNode.scopeId,
            parentVNode.slotScopeIds,
            parentComponent.parent
          );
        }
      }
    };
    const mountChildren = (children, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized, start = 0) => {
      for (let i = start; i < children.length; i++) {
        const child = children[i] = optimized ? cloneIfMounted(children[i]) : normalizeVNode(children[i]);
        patch(
          null,
          child,
          container,
          anchor,
          parentComponent,
          parentSuspense,
          namespace,
          slotScopeIds,
          optimized
        );
      }
    };
    const patchElement = (n1, n2, parentComponent, parentSuspense, namespace, slotScopeIds, optimized) => {
      const el = n2.el = n1.el;
      {
        el.__vnode = n2;
      }
      let { patchFlag, dynamicChildren, dirs } = n2;
      patchFlag |= n1.patchFlag & 16;
      const oldProps = n1.props || EMPTY_OBJ;
      const newProps = n2.props || EMPTY_OBJ;
      let vnodeHook;
      parentComponent && toggleRecurse(parentComponent, false);
      if (vnodeHook = newProps.onVnodeBeforeUpdate) {
        invokeVNodeHook(vnodeHook, parentComponent, n2, n1);
      }
      if (dirs) {
        invokeDirectiveHook(n2, n1, parentComponent, "beforeUpdate");
      }
      parentComponent && toggleRecurse(parentComponent, true);
      if (isHmrUpdating) {
        patchFlag = 0;
        optimized = false;
        dynamicChildren = null;
      }
      if (oldProps.innerHTML && newProps.innerHTML == null || oldProps.textContent && newProps.textContent == null) {
        hostSetElementText(el, "");
      }
      if (dynamicChildren) {
        patchBlockChildren(
          n1.dynamicChildren,
          dynamicChildren,
          el,
          parentComponent,
          parentSuspense,
          resolveChildrenNamespace(n2, namespace),
          slotScopeIds
        );
        {
          traverseStaticChildren(n1, n2);
        }
      } else if (!optimized) {
        patchChildren(
          n1,
          n2,
          el,
          null,
          parentComponent,
          parentSuspense,
          resolveChildrenNamespace(n2, namespace),
          slotScopeIds,
          false
        );
      }
      if (patchFlag > 0) {
        if (patchFlag & 16) {
          patchProps(el, oldProps, newProps, parentComponent, namespace);
        } else {
          if (patchFlag & 2) {
            if (oldProps.class !== newProps.class) {
              hostPatchProp(el, "class", null, newProps.class, namespace);
            }
          }
          if (patchFlag & 4) {
            hostPatchProp(el, "style", oldProps.style, newProps.style, namespace);
          }
          if (patchFlag & 8) {
            const propsToUpdate = n2.dynamicProps;
            for (let i = 0; i < propsToUpdate.length; i++) {
              const key = propsToUpdate[i];
              const prev = oldProps[key];
              const next = newProps[key];
              if (next !== prev || key === "value") {
                hostPatchProp(el, key, prev, next, namespace, parentComponent);
              }
            }
          }
        }
        if (patchFlag & 1) {
          if (n1.children !== n2.children) {
            hostSetElementText(el, n2.children);
          }
        }
      } else if (!optimized && dynamicChildren == null) {
        patchProps(el, oldProps, newProps, parentComponent, namespace);
      }
      if ((vnodeHook = newProps.onVnodeUpdated) || dirs) {
        queuePostRenderEffect(() => {
          vnodeHook && invokeVNodeHook(vnodeHook, parentComponent, n2, n1);
          dirs && invokeDirectiveHook(n2, n1, parentComponent, "updated");
        }, parentSuspense);
      }
    };
    const patchBlockChildren = (oldChildren, newChildren, fallbackContainer, parentComponent, parentSuspense, namespace, slotScopeIds) => {
      for (let i = 0; i < newChildren.length; i++) {
        const oldVNode = oldChildren[i];
        const newVNode = newChildren[i];
        const container = (
          // oldVNode may be an errored async setup() component inside Suspense
          // which will not have a mounted element
          oldVNode.el && // - In the case of a Fragment, we need to provide the actual parent
          // of the Fragment itself so it can move its children.
          (oldVNode.type === Fragment || // - In the case of different nodes, there is going to be a replacement
          // which also requires the correct parent container
          !isSameVNodeType(oldVNode, newVNode) || // - In the case of a component, it could contain anything.
          oldVNode.shapeFlag & (6 | 64)) ? hostParentNode(oldVNode.el) : (
            // In other cases, the parent container is not actually used so we
            // just pass the block element here to avoid a DOM parentNode call.
            fallbackContainer
          )
        );
        patch(
          oldVNode,
          newVNode,
          container,
          null,
          parentComponent,
          parentSuspense,
          namespace,
          slotScopeIds,
          true
        );
      }
    };
    const patchProps = (el, oldProps, newProps, parentComponent, namespace) => {
      if (oldProps !== newProps) {
        if (oldProps !== EMPTY_OBJ) {
          for (const key in oldProps) {
            if (!isReservedProp(key) && !(key in newProps)) {
              hostPatchProp(
                el,
                key,
                oldProps[key],
                null,
                namespace,
                parentComponent
              );
            }
          }
        }
        for (const key in newProps) {
          if (isReservedProp(key)) continue;
          const next = newProps[key];
          const prev = oldProps[key];
          if (next !== prev && key !== "value") {
            hostPatchProp(el, key, prev, next, namespace, parentComponent);
          }
        }
        if ("value" in newProps) {
          hostPatchProp(el, "value", oldProps.value, newProps.value, namespace);
        }
      }
    };
    const processFragment = (n1, n2, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized) => {
      const fragmentStartAnchor = n2.el = n1 ? n1.el : hostCreateText("");
      const fragmentEndAnchor = n2.anchor = n1 ? n1.anchor : hostCreateText("");
      let { patchFlag, dynamicChildren, slotScopeIds: fragmentSlotScopeIds } = n2;
      if (
        // #5523 dev root fragment may inherit directives
        isHmrUpdating || patchFlag & 2048
      ) {
        patchFlag = 0;
        optimized = false;
        dynamicChildren = null;
      }
      if (fragmentSlotScopeIds) {
        slotScopeIds = slotScopeIds ? slotScopeIds.concat(fragmentSlotScopeIds) : fragmentSlotScopeIds;
      }
      if (n1 == null) {
        hostInsert(fragmentStartAnchor, container, anchor);
        hostInsert(fragmentEndAnchor, container, anchor);
        mountChildren(
          // #10007
          // such fragment like `<></>` will be compiled into
          // a fragment which doesn't have a children.
          // In this case fallback to an empty array
          n2.children || [],
          container,
          fragmentEndAnchor,
          parentComponent,
          parentSuspense,
          namespace,
          slotScopeIds,
          optimized
        );
      } else {
        if (patchFlag > 0 && patchFlag & 64 && dynamicChildren && // #2715 the previous fragment could've been a BAILed one as a result
        // of renderSlot() with no valid children
        n1.dynamicChildren) {
          patchBlockChildren(
            n1.dynamicChildren,
            dynamicChildren,
            container,
            parentComponent,
            parentSuspense,
            namespace,
            slotScopeIds
          );
          {
            traverseStaticChildren(n1, n2);
          }
        } else {
          patchChildren(
            n1,
            n2,
            container,
            fragmentEndAnchor,
            parentComponent,
            parentSuspense,
            namespace,
            slotScopeIds,
            optimized
          );
        }
      }
    };
    const processComponent = (n1, n2, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized) => {
      n2.slotScopeIds = slotScopeIds;
      if (n1 == null) {
        if (n2.shapeFlag & 512) {
          parentComponent.ctx.activate(
            n2,
            container,
            anchor,
            namespace,
            optimized
          );
        } else {
          mountComponent(
            n2,
            container,
            anchor,
            parentComponent,
            parentSuspense,
            namespace,
            optimized
          );
        }
      } else {
        updateComponent(n1, n2, optimized);
      }
    };
    const mountComponent = (initialVNode, container, anchor, parentComponent, parentSuspense, namespace, optimized) => {
      const instance = initialVNode.component = createComponentInstance(
        initialVNode,
        parentComponent,
        parentSuspense
      );
      if (instance.type.__hmrId) {
        registerHMR(instance);
      }
      {
        pushWarningContext(initialVNode);
        startMeasure(instance, `mount`);
      }
      if (isKeepAlive(initialVNode)) {
        instance.ctx.renderer = internals;
      }
      {
        {
          startMeasure(instance, `init`);
        }
        setupComponent(instance, false, optimized);
        {
          endMeasure(instance, `init`);
        }
      }
      if (instance.asyncDep) {
        if (isHmrUpdating) initialVNode.el = null;
        parentSuspense && parentSuspense.registerDep(instance, setupRenderEffect, optimized);
        if (!initialVNode.el) {
          const placeholder = instance.subTree = createVNode(Comment);
          processCommentNode(null, placeholder, container, anchor);
        }
      } else {
        setupRenderEffect(
          instance,
          initialVNode,
          container,
          anchor,
          parentSuspense,
          namespace,
          optimized
        );
      }
      {
        popWarningContext();
        endMeasure(instance, `mount`);
      }
    };
    const updateComponent = (n1, n2, optimized) => {
      const instance = n2.component = n1.component;
      if (shouldUpdateComponent(n1, n2, optimized)) {
        if (instance.asyncDep && !instance.asyncResolved) {
          {
            pushWarningContext(n2);
          }
          updateComponentPreRender(instance, n2, optimized);
          {
            popWarningContext();
          }
          return;
        } else {
          instance.next = n2;
          instance.update();
        }
      } else {
        n2.el = n1.el;
        instance.vnode = n2;
      }
    };
    const setupRenderEffect = (instance, initialVNode, container, anchor, parentSuspense, namespace, optimized) => {
      const componentUpdateFn = () => {
        if (!instance.isMounted) {
          let vnodeHook;
          const { el, props } = initialVNode;
          const { bm, m, parent, root, type } = instance;
          const isAsyncWrapperVNode = isAsyncWrapper(initialVNode);
          toggleRecurse(instance, false);
          if (bm) {
            invokeArrayFns(bm);
          }
          if (!isAsyncWrapperVNode && (vnodeHook = props && props.onVnodeBeforeMount)) {
            invokeVNodeHook(vnodeHook, parent, initialVNode);
          }
          toggleRecurse(instance, true);
          {
            if (root.ce) {
              root.ce._injectChildStyle(type);
            }
            {
              startMeasure(instance, `render`);
            }
            const subTree = instance.subTree = renderComponentRoot(instance);
            {
              endMeasure(instance, `render`);
            }
            {
              startMeasure(instance, `patch`);
            }
            patch(
              null,
              subTree,
              container,
              anchor,
              instance,
              parentSuspense,
              namespace
            );
            {
              endMeasure(instance, `patch`);
            }
            initialVNode.el = subTree.el;
          }
          if (m) {
            queuePostRenderEffect(m, parentSuspense);
          }
          if (!isAsyncWrapperVNode && (vnodeHook = props && props.onVnodeMounted)) {
            const scopedInitialVNode = initialVNode;
            queuePostRenderEffect(
              () => invokeVNodeHook(vnodeHook, parent, scopedInitialVNode),
              parentSuspense
            );
          }
          if (initialVNode.shapeFlag & 256 || parent && isAsyncWrapper(parent.vnode) && parent.vnode.shapeFlag & 256) {
            instance.a && queuePostRenderEffect(instance.a, parentSuspense);
          }
          instance.isMounted = true;
          {
            devtoolsComponentAdded(instance);
          }
          initialVNode = container = anchor = null;
        } else {
          let { next, bu, u, parent, vnode } = instance;
          {
            const nonHydratedAsyncRoot = locateNonHydratedAsyncRoot(instance);
            if (nonHydratedAsyncRoot) {
              if (next) {
                next.el = vnode.el;
                updateComponentPreRender(instance, next, optimized);
              }
              nonHydratedAsyncRoot.asyncDep.then(() => {
                if (!instance.isUnmounted) {
                  componentUpdateFn();
                }
              });
              return;
            }
          }
          let originNext = next;
          let vnodeHook;
          {
            pushWarningContext(next || instance.vnode);
          }
          toggleRecurse(instance, false);
          if (next) {
            next.el = vnode.el;
            updateComponentPreRender(instance, next, optimized);
          } else {
            next = vnode;
          }
          if (bu) {
            invokeArrayFns(bu);
          }
          if (vnodeHook = next.props && next.props.onVnodeBeforeUpdate) {
            invokeVNodeHook(vnodeHook, parent, next, vnode);
          }
          toggleRecurse(instance, true);
          {
            startMeasure(instance, `render`);
          }
          const nextTree = renderComponentRoot(instance);
          {
            endMeasure(instance, `render`);
          }
          const prevTree = instance.subTree;
          instance.subTree = nextTree;
          {
            startMeasure(instance, `patch`);
          }
          patch(
            prevTree,
            nextTree,
            // parent may have changed if it's in a teleport
            hostParentNode(prevTree.el),
            // anchor may have changed if it's in a fragment
            getNextHostNode(prevTree),
            instance,
            parentSuspense,
            namespace
          );
          {
            endMeasure(instance, `patch`);
          }
          next.el = nextTree.el;
          if (originNext === null) {
            updateHOCHostEl(instance, nextTree.el);
          }
          if (u) {
            queuePostRenderEffect(u, parentSuspense);
          }
          if (vnodeHook = next.props && next.props.onVnodeUpdated) {
            queuePostRenderEffect(
              () => invokeVNodeHook(vnodeHook, parent, next, vnode),
              parentSuspense
            );
          }
          {
            devtoolsComponentUpdated(instance);
          }
          {
            popWarningContext();
          }
        }
      };
      instance.scope.on();
      const effect2 = instance.effect = new ReactiveEffect(componentUpdateFn);
      instance.scope.off();
      const update = instance.update = effect2.run.bind(effect2);
      const job = instance.job = effect2.runIfDirty.bind(effect2);
      job.i = instance;
      job.id = instance.uid;
      effect2.scheduler = () => queueJob(job);
      toggleRecurse(instance, true);
      {
        effect2.onTrack = instance.rtc ? (e) => invokeArrayFns(instance.rtc, e) : void 0;
        effect2.onTrigger = instance.rtg ? (e) => invokeArrayFns(instance.rtg, e) : void 0;
      }
      update();
    };
    const updateComponentPreRender = (instance, nextVNode, optimized) => {
      nextVNode.component = instance;
      const prevProps = instance.vnode.props;
      instance.vnode = nextVNode;
      instance.next = null;
      updateProps(instance, nextVNode.props, prevProps, optimized);
      updateSlots(instance, nextVNode.children, optimized);
      pauseTracking();
      flushPreFlushCbs(instance);
      resetTracking();
    };
    const patchChildren = (n1, n2, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized = false) => {
      const c1 = n1 && n1.children;
      const prevShapeFlag = n1 ? n1.shapeFlag : 0;
      const c2 = n2.children;
      const { patchFlag, shapeFlag } = n2;
      if (patchFlag > 0) {
        if (patchFlag & 128) {
          patchKeyedChildren(
            c1,
            c2,
            container,
            anchor,
            parentComponent,
            parentSuspense,
            namespace,
            slotScopeIds,
            optimized
          );
          return;
        } else if (patchFlag & 256) {
          patchUnkeyedChildren(
            c1,
            c2,
            container,
            anchor,
            parentComponent,
            parentSuspense,
            namespace,
            slotScopeIds,
            optimized
          );
          return;
        }
      }
      if (shapeFlag & 8) {
        if (prevShapeFlag & 16) {
          unmountChildren(c1, parentComponent, parentSuspense);
        }
        if (c2 !== c1) {
          hostSetElementText(container, c2);
        }
      } else {
        if (prevShapeFlag & 16) {
          if (shapeFlag & 16) {
            patchKeyedChildren(
              c1,
              c2,
              container,
              anchor,
              parentComponent,
              parentSuspense,
              namespace,
              slotScopeIds,
              optimized
            );
          } else {
            unmountChildren(c1, parentComponent, parentSuspense, true);
          }
        } else {
          if (prevShapeFlag & 8) {
            hostSetElementText(container, "");
          }
          if (shapeFlag & 16) {
            mountChildren(
              c2,
              container,
              anchor,
              parentComponent,
              parentSuspense,
              namespace,
              slotScopeIds,
              optimized
            );
          }
        }
      }
    };
    const patchUnkeyedChildren = (c1, c2, container, anchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized) => {
      c1 = c1 || EMPTY_ARR;
      c2 = c2 || EMPTY_ARR;
      const oldLength = c1.length;
      const newLength = c2.length;
      const commonLength = Math.min(oldLength, newLength);
      let i;
      for (i = 0; i < commonLength; i++) {
        const nextChild = c2[i] = optimized ? cloneIfMounted(c2[i]) : normalizeVNode(c2[i]);
        patch(
          c1[i],
          nextChild,
          container,
          null,
          parentComponent,
          parentSuspense,
          namespace,
          slotScopeIds,
          optimized
        );
      }
      if (oldLength > newLength) {
        unmountChildren(
          c1,
          parentComponent,
          parentSuspense,
          true,
          false,
          commonLength
        );
      } else {
        mountChildren(
          c2,
          container,
          anchor,
          parentComponent,
          parentSuspense,
          namespace,
          slotScopeIds,
          optimized,
          commonLength
        );
      }
    };
    const patchKeyedChildren = (c1, c2, container, parentAnchor, parentComponent, parentSuspense, namespace, slotScopeIds, optimized) => {
      let i = 0;
      const l2 = c2.length;
      let e1 = c1.length - 1;
      let e2 = l2 - 1;
      while (i <= e1 && i <= e2) {
        const n1 = c1[i];
        const n2 = c2[i] = optimized ? cloneIfMounted(c2[i]) : normalizeVNode(c2[i]);
        if (isSameVNodeType(n1, n2)) {
          patch(
            n1,
            n2,
            container,
            null,
            parentComponent,
            parentSuspense,
            namespace,
            slotScopeIds,
            optimized
          );
        } else {
          break;
        }
        i++;
      }
      while (i <= e1 && i <= e2) {
        const n1 = c1[e1];
        const n2 = c2[e2] = optimized ? cloneIfMounted(c2[e2]) : normalizeVNode(c2[e2]);
        if (isSameVNodeType(n1, n2)) {
          patch(
            n1,
            n2,
            container,
            null,
            parentComponent,
            parentSuspense,
            namespace,
            slotScopeIds,
            optimized
          );
        } else {
          break;
        }
        e1--;
        e2--;
      }
      if (i > e1) {
        if (i <= e2) {
          const nextPos = e2 + 1;
          const anchor = nextPos < l2 ? c2[nextPos].el : parentAnchor;
          while (i <= e2) {
            patch(
              null,
              c2[i] = optimized ? cloneIfMounted(c2[i]) : normalizeVNode(c2[i]),
              container,
              anchor,
              parentComponent,
              parentSuspense,
              namespace,
              slotScopeIds,
              optimized
            );
            i++;
          }
        }
      } else if (i > e2) {
        while (i <= e1) {
          unmount(c1[i], parentComponent, parentSuspense, true);
          i++;
        }
      } else {
        const s1 = i;
        const s2 = i;
        const keyToNewIndexMap = /* @__PURE__ */ new Map();
        for (i = s2; i <= e2; i++) {
          const nextChild = c2[i] = optimized ? cloneIfMounted(c2[i]) : normalizeVNode(c2[i]);
          if (nextChild.key != null) {
            if (keyToNewIndexMap.has(nextChild.key)) {
              warn$1(
                `Duplicate keys found during update:`,
                JSON.stringify(nextChild.key),
                `Make sure keys are unique.`
              );
            }
            keyToNewIndexMap.set(nextChild.key, i);
          }
        }
        let j;
        let patched = 0;
        const toBePatched = e2 - s2 + 1;
        let moved = false;
        let maxNewIndexSoFar = 0;
        const newIndexToOldIndexMap = new Array(toBePatched);
        for (i = 0; i < toBePatched; i++) newIndexToOldIndexMap[i] = 0;
        for (i = s1; i <= e1; i++) {
          const prevChild = c1[i];
          if (patched >= toBePatched) {
            unmount(prevChild, parentComponent, parentSuspense, true);
            continue;
          }
          let newIndex;
          if (prevChild.key != null) {
            newIndex = keyToNewIndexMap.get(prevChild.key);
          } else {
            for (j = s2; j <= e2; j++) {
              if (newIndexToOldIndexMap[j - s2] === 0 && isSameVNodeType(prevChild, c2[j])) {
                newIndex = j;
                break;
              }
            }
          }
          if (newIndex === void 0) {
            unmount(prevChild, parentComponent, parentSuspense, true);
          } else {
            newIndexToOldIndexMap[newIndex - s2] = i + 1;
            if (newIndex >= maxNewIndexSoFar) {
              maxNewIndexSoFar = newIndex;
            } else {
              moved = true;
            }
            patch(
              prevChild,
              c2[newIndex],
              container,
              null,
              parentComponent,
              parentSuspense,
              namespace,
              slotScopeIds,
              optimized
            );
            patched++;
          }
        }
        const increasingNewIndexSequence = moved ? getSequence(newIndexToOldIndexMap) : EMPTY_ARR;
        j = increasingNewIndexSequence.length - 1;
        for (i = toBePatched - 1; i >= 0; i--) {
          const nextIndex = s2 + i;
          const nextChild = c2[nextIndex];
          const anchor = nextIndex + 1 < l2 ? c2[nextIndex + 1].el : parentAnchor;
          if (newIndexToOldIndexMap[i] === 0) {
            patch(
              null,
              nextChild,
              container,
              anchor,
              parentComponent,
              parentSuspense,
              namespace,
              slotScopeIds,
              optimized
            );
          } else if (moved) {
            if (j < 0 || i !== increasingNewIndexSequence[j]) {
              move(nextChild, container, anchor, 2);
            } else {
              j--;
            }
          }
        }
      }
    };
    const move = (vnode, container, anchor, moveType, parentSuspense = null) => {
      const { el, type, transition, children, shapeFlag } = vnode;
      if (shapeFlag & 6) {
        move(vnode.component.subTree, container, anchor, moveType);
        return;
      }
      if (shapeFlag & 128) {
        vnode.suspense.move(container, anchor, moveType);
        return;
      }
      if (shapeFlag & 64) {
        type.move(vnode, container, anchor, internals);
        return;
      }
      if (type === Fragment) {
        hostInsert(el, container, anchor);
        for (let i = 0; i < children.length; i++) {
          move(children[i], container, anchor, moveType);
        }
        hostInsert(vnode.anchor, container, anchor);
        return;
      }
      if (type === Static) {
        moveStaticNode(vnode, container, anchor);
        return;
      }
      const needTransition2 = moveType !== 2 && shapeFlag & 1 && transition;
      if (needTransition2) {
        if (moveType === 0) {
          transition.beforeEnter(el);
          hostInsert(el, container, anchor);
          queuePostRenderEffect(() => transition.enter(el), parentSuspense);
        } else {
          const { leave, delayLeave, afterLeave } = transition;
          const remove22 = () => hostInsert(el, container, anchor);
          const performLeave = () => {
            leave(el, () => {
              remove22();
              afterLeave && afterLeave();
            });
          };
          if (delayLeave) {
            delayLeave(el, remove22, performLeave);
          } else {
            performLeave();
          }
        }
      } else {
        hostInsert(el, container, anchor);
      }
    };
    const unmount = (vnode, parentComponent, parentSuspense, doRemove = false, optimized = false) => {
      const {
        type,
        props,
        ref: ref3,
        children,
        dynamicChildren,
        shapeFlag,
        patchFlag,
        dirs,
        cacheIndex
      } = vnode;
      if (patchFlag === -2) {
        optimized = false;
      }
      if (ref3 != null) {
        setRef(ref3, null, parentSuspense, vnode, true);
      }
      if (cacheIndex != null) {
        parentComponent.renderCache[cacheIndex] = void 0;
      }
      if (shapeFlag & 256) {
        parentComponent.ctx.deactivate(vnode);
        return;
      }
      const shouldInvokeDirs = shapeFlag & 1 && dirs;
      const shouldInvokeVnodeHook = !isAsyncWrapper(vnode);
      let vnodeHook;
      if (shouldInvokeVnodeHook && (vnodeHook = props && props.onVnodeBeforeUnmount)) {
        invokeVNodeHook(vnodeHook, parentComponent, vnode);
      }
      if (shapeFlag & 6) {
        unmountComponent(vnode.component, parentSuspense, doRemove);
      } else {
        if (shapeFlag & 128) {
          vnode.suspense.unmount(parentSuspense, doRemove);
          return;
        }
        if (shouldInvokeDirs) {
          invokeDirectiveHook(vnode, null, parentComponent, "beforeUnmount");
        }
        if (shapeFlag & 64) {
          vnode.type.remove(
            vnode,
            parentComponent,
            parentSuspense,
            internals,
            doRemove
          );
        } else if (dynamicChildren && // #5154
        // when v-once is used inside a block, setBlockTracking(-1) marks the
        // parent block with hasOnce: true
        // so that it doesn't take the fast path during unmount - otherwise
        // components nested in v-once are never unmounted.
        !dynamicChildren.hasOnce && // #1153: fast path should not be taken for non-stable (v-for) fragments
        (type !== Fragment || patchFlag > 0 && patchFlag & 64)) {
          unmountChildren(
            dynamicChildren,
            parentComponent,
            parentSuspense,
            false,
            true
          );
        } else if (type === Fragment && patchFlag & (128 | 256) || !optimized && shapeFlag & 16) {
          unmountChildren(children, parentComponent, parentSuspense);
        }
        if (doRemove) {
          remove2(vnode);
        }
      }
      if (shouldInvokeVnodeHook && (vnodeHook = props && props.onVnodeUnmounted) || shouldInvokeDirs) {
        queuePostRenderEffect(() => {
          vnodeHook && invokeVNodeHook(vnodeHook, parentComponent, vnode);
          shouldInvokeDirs && invokeDirectiveHook(vnode, null, parentComponent, "unmounted");
        }, parentSuspense);
      }
    };
    const remove2 = (vnode) => {
      const { type, el, anchor, transition } = vnode;
      if (type === Fragment) {
        if (vnode.patchFlag > 0 && vnode.patchFlag & 2048 && transition && !transition.persisted) {
          vnode.children.forEach((child) => {
            if (child.type === Comment) {
              hostRemove(child.el);
            } else {
              remove2(child);
            }
          });
        } else {
          removeFragment(el, anchor);
        }
        return;
      }
      if (type === Static) {
        removeStaticNode(vnode);
        return;
      }
      const performRemove = () => {
        hostRemove(el);
        if (transition && !transition.persisted && transition.afterLeave) {
          transition.afterLeave();
        }
      };
      if (vnode.shapeFlag & 1 && transition && !transition.persisted) {
        const { leave, delayLeave } = transition;
        const performLeave = () => leave(el, performRemove);
        if (delayLeave) {
          delayLeave(vnode.el, performRemove, performLeave);
        } else {
          performLeave();
        }
      } else {
        performRemove();
      }
    };
    const removeFragment = (cur, end) => {
      let next;
      while (cur !== end) {
        next = hostNextSibling(cur);
        hostRemove(cur);
        cur = next;
      }
      hostRemove(end);
    };
    const unmountComponent = (instance, parentSuspense, doRemove) => {
      if (instance.type.__hmrId) {
        unregisterHMR(instance);
      }
      const { bum, scope, job, subTree, um, m, a } = instance;
      invalidateMount(m);
      invalidateMount(a);
      if (bum) {
        invokeArrayFns(bum);
      }
      scope.stop();
      if (job) {
        job.flags |= 8;
        unmount(subTree, instance, parentSuspense, doRemove);
      }
      if (um) {
        queuePostRenderEffect(um, parentSuspense);
      }
      queuePostRenderEffect(() => {
        instance.isUnmounted = true;
      }, parentSuspense);
      if (parentSuspense && parentSuspense.pendingBranch && !parentSuspense.isUnmounted && instance.asyncDep && !instance.asyncResolved && instance.suspenseId === parentSuspense.pendingId) {
        parentSuspense.deps--;
        if (parentSuspense.deps === 0) {
          parentSuspense.resolve();
        }
      }
      {
        devtoolsComponentRemoved(instance);
      }
    };
    const unmountChildren = (children, parentComponent, parentSuspense, doRemove = false, optimized = false, start = 0) => {
      for (let i = start; i < children.length; i++) {
        unmount(children[i], parentComponent, parentSuspense, doRemove, optimized);
      }
    };
    const getNextHostNode = (vnode) => {
      if (vnode.shapeFlag & 6) {
        return getNextHostNode(vnode.component.subTree);
      }
      if (vnode.shapeFlag & 128) {
        return vnode.suspense.next();
      }
      const el = hostNextSibling(vnode.anchor || vnode.el);
      const teleportEnd = el && el[TeleportEndKey];
      return teleportEnd ? hostNextSibling(teleportEnd) : el;
    };
    let isFlushing = false;
    const render2 = (vnode, container, namespace) => {
      if (vnode == null) {
        if (container._vnode) {
          unmount(container._vnode, null, null, true);
        }
      } else {
        patch(
          container._vnode || null,
          vnode,
          container,
          null,
          null,
          null,
          namespace
        );
      }
      container._vnode = vnode;
      if (!isFlushing) {
        isFlushing = true;
        flushPreFlushCbs();
        flushPostFlushCbs();
        isFlushing = false;
      }
    };
    const internals = {
      p: patch,
      um: unmount,
      m: move,
      r: remove2,
      mt: mountComponent,
      mc: mountChildren,
      pc: patchChildren,
      pbc: patchBlockChildren,
      n: getNextHostNode,
      o: options
    };
    let hydrate;
    return {
      render: render2,
      hydrate,
      createApp: createAppAPI(render2)
    };
  }
  function resolveChildrenNamespace({ type, props }, currentNamespace) {
    return currentNamespace === "svg" && type === "foreignObject" || currentNamespace === "mathml" && type === "annotation-xml" && props && props.encoding && props.encoding.includes("html") ? void 0 : currentNamespace;
  }
  function toggleRecurse({ effect: effect2, job }, allowed) {
    if (allowed) {
      effect2.flags |= 32;
      job.flags |= 4;
    } else {
      effect2.flags &= -33;
      job.flags &= -5;
    }
  }
  function needTransition(parentSuspense, transition) {
    return (!parentSuspense || parentSuspense && !parentSuspense.pendingBranch) && transition && !transition.persisted;
  }
  function traverseStaticChildren(n1, n2, shallow = false) {
    const ch1 = n1.children;
    const ch2 = n2.children;
    if (isArray(ch1) && isArray(ch2)) {
      for (let i = 0; i < ch1.length; i++) {
        const c1 = ch1[i];
        let c2 = ch2[i];
        if (c2.shapeFlag & 1 && !c2.dynamicChildren) {
          if (c2.patchFlag <= 0 || c2.patchFlag === 32) {
            c2 = ch2[i] = cloneIfMounted(ch2[i]);
            c2.el = c1.el;
          }
          if (!shallow && c2.patchFlag !== -2)
            traverseStaticChildren(c1, c2);
        }
        if (c2.type === Text) {
          c2.el = c1.el;
        }
        if (c2.type === Comment && !c2.el) {
          c2.el = c1.el;
        }
      }
    }
  }
  function getSequence(arr) {
    const p2 = arr.slice();
    const result = [0];
    let i, j, u, v, c;
    const len = arr.length;
    for (i = 0; i < len; i++) {
      const arrI = arr[i];
      if (arrI !== 0) {
        j = result[result.length - 1];
        if (arr[j] < arrI) {
          p2[i] = j;
          result.push(i);
          continue;
        }
        u = 0;
        v = result.length - 1;
        while (u < v) {
          c = u + v >> 1;
          if (arr[result[c]] < arrI) {
            u = c + 1;
          } else {
            v = c;
          }
        }
        if (arrI < arr[result[u]]) {
          if (u > 0) {
            p2[i] = result[u - 1];
          }
          result[u] = i;
        }
      }
    }
    u = result.length;
    v = result[u - 1];
    while (u-- > 0) {
      result[u] = v;
      v = p2[v];
    }
    return result;
  }
  function locateNonHydratedAsyncRoot(instance) {
    const subComponent = instance.subTree.component;
    if (subComponent) {
      if (subComponent.asyncDep && !subComponent.asyncResolved) {
        return subComponent;
      } else {
        return locateNonHydratedAsyncRoot(subComponent);
      }
    }
  }
  function invalidateMount(hooks) {
    if (hooks) {
      for (let i = 0; i < hooks.length; i++)
        hooks[i].flags |= 8;
    }
  }
  const ssrContextKey = Symbol.for("v-scx");
  const useSSRContext = () => {
    {
      const ctx = inject(ssrContextKey);
      if (!ctx) {
        warn$1(
          `Server rendering context not provided. Make sure to only call useSSRContext() conditionally in the server build.`
        );
      }
      return ctx;
    }
  };
  function watch(source, cb, options) {
    if (!isFunction(cb)) {
      warn$1(
        `\`watch(fn, options?)\` signature has been moved to a separate API. Use \`watchEffect(fn, options?)\` instead. \`watch\` now only supports \`watch(source, cb, options?) signature.`
      );
    }
    return doWatch(source, cb, options);
  }
  function doWatch(source, cb, options = EMPTY_OBJ) {
    const { immediate, deep, flush, once } = options;
    if (!cb) {
      if (immediate !== void 0) {
        warn$1(
          `watch() "immediate" option is only respected when using the watch(source, callback, options?) signature.`
        );
      }
      if (deep !== void 0) {
        warn$1(
          `watch() "deep" option is only respected when using the watch(source, callback, options?) signature.`
        );
      }
      if (once !== void 0) {
        warn$1(
          `watch() "once" option is only respected when using the watch(source, callback, options?) signature.`
        );
      }
    }
    const baseWatchOptions = extend({}, options);
    baseWatchOptions.onWarn = warn$1;
    const runsImmediately = cb && immediate || !cb && flush !== "post";
    let ssrCleanup;
    if (isInSSRComponentSetup) {
      if (flush === "sync") {
        const ctx = useSSRContext();
        ssrCleanup = ctx.__watcherHandles || (ctx.__watcherHandles = []);
      } else if (!runsImmediately) {
        const watchStopHandle = () => {
        };
        watchStopHandle.stop = NOOP;
        watchStopHandle.resume = NOOP;
        watchStopHandle.pause = NOOP;
        return watchStopHandle;
      }
    }
    const instance = currentInstance;
    baseWatchOptions.call = (fn, type, args) => callWithAsyncErrorHandling(fn, instance, type, args);
    let isPre = false;
    if (flush === "post") {
      baseWatchOptions.scheduler = (job) => {
        queuePostRenderEffect(job, instance && instance.suspense);
      };
    } else if (flush !== "sync") {
      isPre = true;
      baseWatchOptions.scheduler = (job, isFirstRun) => {
        if (isFirstRun) {
          job();
        } else {
          queueJob(job);
        }
      };
    }
    baseWatchOptions.augmentJob = (job) => {
      if (cb) {
        job.flags |= 4;
      }
      if (isPre) {
        job.flags |= 2;
        if (instance) {
          job.id = instance.uid;
          job.i = instance;
        }
      }
    };
    const watchHandle = watch$1(source, cb, baseWatchOptions);
    if (isInSSRComponentSetup) {
      if (ssrCleanup) {
        ssrCleanup.push(watchHandle);
      } else if (runsImmediately) {
        watchHandle();
      }
    }
    return watchHandle;
  }
  function instanceWatch(source, value, options) {
    const publicThis = this.proxy;
    const getter = isString(source) ? source.includes(".") ? createPathGetter(publicThis, source) : () => publicThis[source] : source.bind(publicThis, publicThis);
    let cb;
    if (isFunction(value)) {
      cb = value;
    } else {
      cb = value.handler;
      options = value;
    }
    const reset = setCurrentInstance(this);
    const res = doWatch(getter, cb.bind(publicThis), options);
    reset();
    return res;
  }
  function createPathGetter(ctx, path) {
    const segments = path.split(".");
    return () => {
      let cur = ctx;
      for (let i = 0; i < segments.length && cur; i++) {
        cur = cur[segments[i]];
      }
      return cur;
    };
  }
  const getModelModifiers = (props, modelName) => {
    return modelName === "modelValue" || modelName === "model-value" ? props.modelModifiers : props[`${modelName}Modifiers`] || props[`${camelize(modelName)}Modifiers`] || props[`${hyphenate(modelName)}Modifiers`];
  };
  function emit(instance, event, ...rawArgs) {
    if (instance.isUnmounted) return;
    const props = instance.vnode.props || EMPTY_OBJ;
    {
      const {
        emitsOptions,
        propsOptions: [propsOptions]
      } = instance;
      if (emitsOptions) {
        if (!(event in emitsOptions) && true) {
          if (!propsOptions || !(toHandlerKey(camelize(event)) in propsOptions)) {
            warn$1(
              `Component emitted event "${event}" but it is neither declared in the emits option nor as an "${toHandlerKey(camelize(event))}" prop.`
            );
          }
        } else {
          const validator = emitsOptions[event];
          if (isFunction(validator)) {
            const isValid = validator(...rawArgs);
            if (!isValid) {
              warn$1(
                `Invalid event arguments: event validation failed for event "${event}".`
              );
            }
          }
        }
      }
    }
    let args = rawArgs;
    const isModelListener2 = event.startsWith("update:");
    const modifiers = isModelListener2 && getModelModifiers(props, event.slice(7));
    if (modifiers) {
      if (modifiers.trim) {
        args = rawArgs.map((a) => isString(a) ? a.trim() : a);
      }
      if (modifiers.number) {
        args = rawArgs.map(looseToNumber);
      }
    }
    {
      devtoolsComponentEmit(instance, event, args);
    }
    {
      const lowerCaseEvent = event.toLowerCase();
      if (lowerCaseEvent !== event && props[toHandlerKey(lowerCaseEvent)]) {
        warn$1(
          `Event "${lowerCaseEvent}" is emitted in component ${formatComponentName(
            instance,
            instance.type
          )} but the handler is registered for "${event}". Note that HTML attributes are case-insensitive and you cannot use v-on to listen to camelCase events when using in-DOM templates. You should probably use "${hyphenate(
            event
          )}" instead of "${event}".`
        );
      }
    }
    let handlerName;
    let handler = props[handlerName = toHandlerKey(event)] || // also try camelCase event handler (#2249)
    props[handlerName = toHandlerKey(camelize(event))];
    if (!handler && isModelListener2) {
      handler = props[handlerName = toHandlerKey(hyphenate(event))];
    }
    if (handler) {
      callWithAsyncErrorHandling(
        handler,
        instance,
        6,
        args
      );
    }
    const onceHandler = props[handlerName + `Once`];
    if (onceHandler) {
      if (!instance.emitted) {
        instance.emitted = {};
      } else if (instance.emitted[handlerName]) {
        return;
      }
      instance.emitted[handlerName] = true;
      callWithAsyncErrorHandling(
        onceHandler,
        instance,
        6,
        args
      );
    }
  }
  function normalizeEmitsOptions(comp, appContext, asMixin = false) {
    const cache = appContext.emitsCache;
    const cached = cache.get(comp);
    if (cached !== void 0) {
      return cached;
    }
    const raw = comp.emits;
    let normalized = {};
    let hasExtends = false;
    if (!isFunction(comp)) {
      const extendEmits = (raw2) => {
        const normalizedFromExtend = normalizeEmitsOptions(raw2, appContext, true);
        if (normalizedFromExtend) {
          hasExtends = true;
          extend(normalized, normalizedFromExtend);
        }
      };
      if (!asMixin && appContext.mixins.length) {
        appContext.mixins.forEach(extendEmits);
      }
      if (comp.extends) {
        extendEmits(comp.extends);
      }
      if (comp.mixins) {
        comp.mixins.forEach(extendEmits);
      }
    }
    if (!raw && !hasExtends) {
      if (isObject(comp)) {
        cache.set(comp, null);
      }
      return null;
    }
    if (isArray(raw)) {
      raw.forEach((key) => normalized[key] = null);
    } else {
      extend(normalized, raw);
    }
    if (isObject(comp)) {
      cache.set(comp, normalized);
    }
    return normalized;
  }
  function isEmitListener(options, key) {
    if (!options || !isOn(key)) {
      return false;
    }
    key = key.slice(2).replace(/Once$/, "");
    return hasOwn(options, key[0].toLowerCase() + key.slice(1)) || hasOwn(options, hyphenate(key)) || hasOwn(options, key);
  }
  let accessedAttrs = false;
  function markAttrsAccessed() {
    accessedAttrs = true;
  }
  function renderComponentRoot(instance) {
    const {
      type: Component,
      vnode,
      proxy,
      withProxy,
      propsOptions: [propsOptions],
      slots,
      attrs,
      emit: emit2,
      render: render2,
      renderCache,
      props,
      data,
      setupState,
      ctx,
      inheritAttrs
    } = instance;
    const prev = setCurrentRenderingInstance(instance);
    let result;
    let fallthroughAttrs;
    {
      accessedAttrs = false;
    }
    try {
      if (vnode.shapeFlag & 4) {
        const proxyToUse = withProxy || proxy;
        const thisProxy = setupState.__isScriptSetup ? new Proxy(proxyToUse, {
          get(target, key, receiver) {
            warn$1(
              `Property '${String(
                key
              )}' was accessed via 'this'. Avoid using 'this' in templates.`
            );
            return Reflect.get(target, key, receiver);
          }
        }) : proxyToUse;
        result = normalizeVNode(
          render2.call(
            thisProxy,
            proxyToUse,
            renderCache,
            true ? shallowReadonly(props) : props,
            setupState,
            data,
            ctx
          )
        );
        fallthroughAttrs = attrs;
      } else {
        const render22 = Component;
        if (attrs === props) {
          markAttrsAccessed();
        }
        result = normalizeVNode(
          render22.length > 1 ? render22(
            true ? shallowReadonly(props) : props,
            true ? {
              get attrs() {
                markAttrsAccessed();
                return shallowReadonly(attrs);
              },
              slots,
              emit: emit2
            } : { attrs, slots, emit: emit2 }
          ) : render22(
            true ? shallowReadonly(props) : props,
            null
          )
        );
        fallthroughAttrs = Component.props ? attrs : getFunctionalFallthrough(attrs);
      }
    } catch (err) {
      blockStack.length = 0;
      handleError(err, instance, 1);
      result = createVNode(Comment);
    }
    let root = result;
    let setRoot = void 0;
    if (result.patchFlag > 0 && result.patchFlag & 2048) {
      [root, setRoot] = getChildRoot(result);
    }
    if (fallthroughAttrs && inheritAttrs !== false) {
      const keys = Object.keys(fallthroughAttrs);
      const { shapeFlag } = root;
      if (keys.length) {
        if (shapeFlag & (1 | 6)) {
          if (propsOptions && keys.some(isModelListener)) {
            fallthroughAttrs = filterModelListeners(
              fallthroughAttrs,
              propsOptions
            );
          }
          root = cloneVNode(root, fallthroughAttrs, false, true);
        } else if (!accessedAttrs && root.type !== Comment) {
          const allAttrs = Object.keys(attrs);
          const eventAttrs = [];
          const extraAttrs = [];
          for (let i = 0, l = allAttrs.length; i < l; i++) {
            const key = allAttrs[i];
            if (isOn(key)) {
              if (!isModelListener(key)) {
                eventAttrs.push(key[2].toLowerCase() + key.slice(3));
              }
            } else {
              extraAttrs.push(key);
            }
          }
          if (extraAttrs.length) {
            warn$1(
              `Extraneous non-props attributes (${extraAttrs.join(", ")}) were passed to component but could not be automatically inherited because component renders fragment or text or teleport root nodes.`
            );
          }
          if (eventAttrs.length) {
            warn$1(
              `Extraneous non-emits event listeners (${eventAttrs.join(", ")}) were passed to component but could not be automatically inherited because component renders fragment or text root nodes. If the listener is intended to be a component custom event listener only, declare it using the "emits" option.`
            );
          }
        }
      }
    }
    if (vnode.dirs) {
      if (!isElementRoot(root)) {
        warn$1(
          `Runtime directive used on component with non-element root node. The directives will not function as intended.`
        );
      }
      root = cloneVNode(root, null, false, true);
      root.dirs = root.dirs ? root.dirs.concat(vnode.dirs) : vnode.dirs;
    }
    if (vnode.transition) {
      if (!isElementRoot(root)) {
        warn$1(
          `Component inside <Transition> renders non-element root node that cannot be animated.`
        );
      }
      setTransitionHooks(root, vnode.transition);
    }
    if (setRoot) {
      setRoot(root);
    } else {
      result = root;
    }
    setCurrentRenderingInstance(prev);
    return result;
  }
  const getChildRoot = (vnode) => {
    const rawChildren = vnode.children;
    const dynamicChildren = vnode.dynamicChildren;
    const childRoot = filterSingleRoot(rawChildren, false);
    if (!childRoot) {
      return [vnode, void 0];
    } else if (childRoot.patchFlag > 0 && childRoot.patchFlag & 2048) {
      return getChildRoot(childRoot);
    }
    const index = rawChildren.indexOf(childRoot);
    const dynamicIndex = dynamicChildren ? dynamicChildren.indexOf(childRoot) : -1;
    const setRoot = (updatedRoot) => {
      rawChildren[index] = updatedRoot;
      if (dynamicChildren) {
        if (dynamicIndex > -1) {
          dynamicChildren[dynamicIndex] = updatedRoot;
        } else if (updatedRoot.patchFlag > 0) {
          vnode.dynamicChildren = [...dynamicChildren, updatedRoot];
        }
      }
    };
    return [normalizeVNode(childRoot), setRoot];
  };
  function filterSingleRoot(children, recurse = true) {
    let singleRoot;
    for (let i = 0; i < children.length; i++) {
      const child = children[i];
      if (isVNode(child)) {
        if (child.type !== Comment || child.children === "v-if") {
          if (singleRoot) {
            return;
          } else {
            singleRoot = child;
            if (recurse && singleRoot.patchFlag > 0 && singleRoot.patchFlag & 2048) {
              return filterSingleRoot(singleRoot.children);
            }
          }
        }
      } else {
        return;
      }
    }
    return singleRoot;
  }
  const getFunctionalFallthrough = (attrs) => {
    let res;
    for (const key in attrs) {
      if (key === "class" || key === "style" || isOn(key)) {
        (res || (res = {}))[key] = attrs[key];
      }
    }
    return res;
  };
  const filterModelListeners = (attrs, props) => {
    const res = {};
    for (const key in attrs) {
      if (!isModelListener(key) || !(key.slice(9) in props)) {
        res[key] = attrs[key];
      }
    }
    return res;
  };
  const isElementRoot = (vnode) => {
    return vnode.shapeFlag & (6 | 1) || vnode.type === Comment;
  };
  function shouldUpdateComponent(prevVNode, nextVNode, optimized) {
    const { props: prevProps, children: prevChildren, component } = prevVNode;
    const { props: nextProps, children: nextChildren, patchFlag } = nextVNode;
    const emits = component.emitsOptions;
    if ((prevChildren || nextChildren) && isHmrUpdating) {
      return true;
    }
    if (nextVNode.dirs || nextVNode.transition) {
      return true;
    }
    if (optimized && patchFlag >= 0) {
      if (patchFlag & 1024) {
        return true;
      }
      if (patchFlag & 16) {
        if (!prevProps) {
          return !!nextProps;
        }
        return hasPropsChanged(prevProps, nextProps, emits);
      } else if (patchFlag & 8) {
        const dynamicProps = nextVNode.dynamicProps;
        for (let i = 0; i < dynamicProps.length; i++) {
          const key = dynamicProps[i];
          if (nextProps[key] !== prevProps[key] && !isEmitListener(emits, key)) {
            return true;
          }
        }
      }
    } else {
      if (prevChildren || nextChildren) {
        if (!nextChildren || !nextChildren.$stable) {
          return true;
        }
      }
      if (prevProps === nextProps) {
        return false;
      }
      if (!prevProps) {
        return !!nextProps;
      }
      if (!nextProps) {
        return true;
      }
      return hasPropsChanged(prevProps, nextProps, emits);
    }
    return false;
  }
  function hasPropsChanged(prevProps, nextProps, emitsOptions) {
    const nextKeys = Object.keys(nextProps);
    if (nextKeys.length !== Object.keys(prevProps).length) {
      return true;
    }
    for (let i = 0; i < nextKeys.length; i++) {
      const key = nextKeys[i];
      if (nextProps[key] !== prevProps[key] && !isEmitListener(emitsOptions, key)) {
        return true;
      }
    }
    return false;
  }
  function updateHOCHostEl({ vnode, parent }, el) {
    while (parent) {
      const root = parent.subTree;
      if (root.suspense && root.suspense.activeBranch === vnode) {
        root.el = vnode.el;
      }
      if (root === vnode) {
        (vnode = parent.vnode).el = el;
        parent = parent.parent;
      } else {
        break;
      }
    }
  }
  const isSuspense = (type) => type.__isSuspense;
  function queueEffectWithSuspense(fn, suspense) {
    if (suspense && suspense.pendingBranch) {
      if (isArray(fn)) {
        suspense.effects.push(...fn);
      } else {
        suspense.effects.push(fn);
      }
    } else {
      queuePostFlushCb(fn);
    }
  }
  const Fragment = Symbol.for("v-fgt");
  const Text = Symbol.for("v-txt");
  const Comment = Symbol.for("v-cmt");
  const Static = Symbol.for("v-stc");
  const blockStack = [];
  let currentBlock = null;
  function openBlock(disableTracking = false) {
    blockStack.push(currentBlock = disableTracking ? null : []);
  }
  function closeBlock() {
    blockStack.pop();
    currentBlock = blockStack[blockStack.length - 1] || null;
  }
  let isBlockTreeEnabled = 1;
  function setBlockTracking(value, inVOnce = false) {
    isBlockTreeEnabled += value;
    if (value < 0 && currentBlock && inVOnce) {
      currentBlock.hasOnce = true;
    }
  }
  function setupBlock(vnode) {
    vnode.dynamicChildren = isBlockTreeEnabled > 0 ? currentBlock || EMPTY_ARR : null;
    closeBlock();
    if (isBlockTreeEnabled > 0 && currentBlock) {
      currentBlock.push(vnode);
    }
    return vnode;
  }
  function createElementBlock(type, props, children, patchFlag, dynamicProps, shapeFlag) {
    return setupBlock(
      createBaseVNode(
        type,
        props,
        children,
        patchFlag,
        dynamicProps,
        shapeFlag,
        true
      )
    );
  }
  function createBlock(type, props, children, patchFlag, dynamicProps) {
    return setupBlock(
      createVNode(
        type,
        props,
        children,
        patchFlag,
        dynamicProps,
        true
      )
    );
  }
  function isVNode(value) {
    return value ? value.__v_isVNode === true : false;
  }
  function isSameVNodeType(n1, n2) {
    if (n2.shapeFlag & 6 && n1.component) {
      const dirtyInstances = hmrDirtyComponents.get(n2.type);
      if (dirtyInstances && dirtyInstances.has(n1.component)) {
        n1.shapeFlag &= -257;
        n2.shapeFlag &= -513;
        return false;
      }
    }
    return n1.type === n2.type && n1.key === n2.key;
  }
  const createVNodeWithArgsTransform = (...args) => {
    return _createVNode(
      ...args
    );
  };
  const normalizeKey = ({ key }) => key != null ? key : null;
  const normalizeRef = ({
    ref: ref3,
    ref_key,
    ref_for
  }) => {
    if (typeof ref3 === "number") {
      ref3 = "" + ref3;
    }
    return ref3 != null ? isString(ref3) || isRef(ref3) || isFunction(ref3) ? { i: currentRenderingInstance, r: ref3, k: ref_key, f: !!ref_for } : ref3 : null;
  };
  function createBaseVNode(type, props = null, children = null, patchFlag = 0, dynamicProps = null, shapeFlag = type === Fragment ? 0 : 1, isBlockNode = false, needFullChildrenNormalization = false) {
    const vnode = {
      __v_isVNode: true,
      __v_skip: true,
      type,
      props,
      key: props && normalizeKey(props),
      ref: props && normalizeRef(props),
      scopeId: currentScopeId,
      slotScopeIds: null,
      children,
      component: null,
      suspense: null,
      ssContent: null,
      ssFallback: null,
      dirs: null,
      transition: null,
      el: null,
      anchor: null,
      target: null,
      targetStart: null,
      targetAnchor: null,
      staticCount: 0,
      shapeFlag,
      patchFlag,
      dynamicProps,
      dynamicChildren: null,
      appContext: null,
      ctx: currentRenderingInstance
    };
    if (needFullChildrenNormalization) {
      normalizeChildren(vnode, children);
      if (shapeFlag & 128) {
        type.normalize(vnode);
      }
    } else if (children) {
      vnode.shapeFlag |= isString(children) ? 8 : 16;
    }
    if (vnode.key !== vnode.key) {
      warn$1(`VNode created with invalid key (NaN). VNode type:`, vnode.type);
    }
    if (isBlockTreeEnabled > 0 && // avoid a block node from tracking itself
    !isBlockNode && // has current parent block
    currentBlock && // presence of a patch flag indicates this node needs patching on updates.
    // component nodes also should always be patched, because even if the
    // component doesn't need to update, it needs to persist the instance on to
    // the next vnode so that it can be properly unmounted later.
    (vnode.patchFlag > 0 || shapeFlag & 6) && // the EVENTS flag is only for hydration and if it is the only flag, the
    // vnode should not be considered dynamic due to handler caching.
    vnode.patchFlag !== 32) {
      currentBlock.push(vnode);
    }
    return vnode;
  }
  const createVNode = createVNodeWithArgsTransform;
  function _createVNode(type, props = null, children = null, patchFlag = 0, dynamicProps = null, isBlockNode = false) {
    if (!type || type === NULL_DYNAMIC_COMPONENT) {
      if (!type) {
        warn$1(`Invalid vnode type when creating vnode: ${type}.`);
      }
      type = Comment;
    }
    if (isVNode(type)) {
      const cloned = cloneVNode(
        type,
        props,
        true
        /* mergeRef: true */
      );
      if (children) {
        normalizeChildren(cloned, children);
      }
      if (isBlockTreeEnabled > 0 && !isBlockNode && currentBlock) {
        if (cloned.shapeFlag & 6) {
          currentBlock[currentBlock.indexOf(type)] = cloned;
        } else {
          currentBlock.push(cloned);
        }
      }
      cloned.patchFlag = -2;
      return cloned;
    }
    if (isClassComponent(type)) {
      type = type.__vccOpts;
    }
    if (props) {
      props = guardReactiveProps(props);
      let { class: klass, style } = props;
      if (klass && !isString(klass)) {
        props.class = normalizeClass(klass);
      }
      if (isObject(style)) {
        if (isProxy(style) && !isArray(style)) {
          style = extend({}, style);
        }
        props.style = normalizeStyle(style);
      }
    }
    const shapeFlag = isString(type) ? 1 : isSuspense(type) ? 128 : isTeleport(type) ? 64 : isObject(type) ? 4 : isFunction(type) ? 2 : 0;
    if (shapeFlag & 4 && isProxy(type)) {
      type = toRaw(type);
      warn$1(
        `Vue received a Component that was made a reactive object. This can lead to unnecessary performance overhead and should be avoided by marking the component with \`markRaw\` or using \`shallowRef\` instead of \`ref\`.`,
        `
Component that was made reactive: `,
        type
      );
    }
    return createBaseVNode(
      type,
      props,
      children,
      patchFlag,
      dynamicProps,
      shapeFlag,
      isBlockNode,
      true
    );
  }
  function guardReactiveProps(props) {
    if (!props) return null;
    return isProxy(props) || isInternalObject(props) ? extend({}, props) : props;
  }
  function cloneVNode(vnode, extraProps, mergeRef = false, cloneTransition = false) {
    const { props, ref: ref3, patchFlag, children, transition } = vnode;
    const mergedProps = extraProps ? mergeProps(props || {}, extraProps) : props;
    const cloned = {
      __v_isVNode: true,
      __v_skip: true,
      type: vnode.type,
      props: mergedProps,
      key: mergedProps && normalizeKey(mergedProps),
      ref: extraProps && extraProps.ref ? (
        // #2078 in the case of <component :is="vnode" ref="extra"/>
        // if the vnode itself already has a ref, cloneVNode will need to merge
        // the refs so the single vnode can be set on multiple refs
        mergeRef && ref3 ? isArray(ref3) ? ref3.concat(normalizeRef(extraProps)) : [ref3, normalizeRef(extraProps)] : normalizeRef(extraProps)
      ) : ref3,
      scopeId: vnode.scopeId,
      slotScopeIds: vnode.slotScopeIds,
      children: patchFlag === -1 && isArray(children) ? children.map(deepCloneVNode) : children,
      target: vnode.target,
      targetStart: vnode.targetStart,
      targetAnchor: vnode.targetAnchor,
      staticCount: vnode.staticCount,
      shapeFlag: vnode.shapeFlag,
      // if the vnode is cloned with extra props, we can no longer assume its
      // existing patch flag to be reliable and need to add the FULL_PROPS flag.
      // note: preserve flag for fragments since they use the flag for children
      // fast paths only.
      patchFlag: extraProps && vnode.type !== Fragment ? patchFlag === -1 ? 16 : patchFlag | 16 : patchFlag,
      dynamicProps: vnode.dynamicProps,
      dynamicChildren: vnode.dynamicChildren,
      appContext: vnode.appContext,
      dirs: vnode.dirs,
      transition,
      // These should technically only be non-null on mounted VNodes. However,
      // they *should* be copied for kept-alive vnodes. So we just always copy
      // them since them being non-null during a mount doesn't affect the logic as
      // they will simply be overwritten.
      component: vnode.component,
      suspense: vnode.suspense,
      ssContent: vnode.ssContent && cloneVNode(vnode.ssContent),
      ssFallback: vnode.ssFallback && cloneVNode(vnode.ssFallback),
      el: vnode.el,
      anchor: vnode.anchor,
      ctx: vnode.ctx,
      ce: vnode.ce
    };
    if (transition && cloneTransition) {
      setTransitionHooks(
        cloned,
        transition.clone(cloned)
      );
    }
    return cloned;
  }
  function deepCloneVNode(vnode) {
    const cloned = cloneVNode(vnode);
    if (isArray(vnode.children)) {
      cloned.children = vnode.children.map(deepCloneVNode);
    }
    return cloned;
  }
  function createTextVNode(text = " ", flag = 0) {
    return createVNode(Text, null, text, flag);
  }
  function createStaticVNode(content, numberOfNodes) {
    const vnode = createVNode(Static, null, content);
    vnode.staticCount = numberOfNodes;
    return vnode;
  }
  function createCommentVNode(text = "", asBlock = false) {
    return asBlock ? (openBlock(), createBlock(Comment, null, text)) : createVNode(Comment, null, text);
  }
  function normalizeVNode(child) {
    if (child == null || typeof child === "boolean") {
      return createVNode(Comment);
    } else if (isArray(child)) {
      return createVNode(
        Fragment,
        null,
        // #3666, avoid reference pollution when reusing vnode
        child.slice()
      );
    } else if (isVNode(child)) {
      return cloneIfMounted(child);
    } else {
      return createVNode(Text, null, String(child));
    }
  }
  function cloneIfMounted(child) {
    return child.el === null && child.patchFlag !== -1 || child.memo ? child : cloneVNode(child);
  }
  function normalizeChildren(vnode, children) {
    let type = 0;
    const { shapeFlag } = vnode;
    if (children == null) {
      children = null;
    } else if (isArray(children)) {
      type = 16;
    } else if (typeof children === "object") {
      if (shapeFlag & (1 | 64)) {
        const slot = children.default;
        if (slot) {
          slot._c && (slot._d = false);
          normalizeChildren(vnode, slot());
          slot._c && (slot._d = true);
        }
        return;
      } else {
        type = 32;
        const slotFlag = children._;
        if (!slotFlag && !isInternalObject(children)) {
          children._ctx = currentRenderingInstance;
        } else if (slotFlag === 3 && currentRenderingInstance) {
          if (currentRenderingInstance.slots._ === 1) {
            children._ = 1;
          } else {
            children._ = 2;
            vnode.patchFlag |= 1024;
          }
        }
      }
    } else if (isFunction(children)) {
      children = { default: children, _ctx: currentRenderingInstance };
      type = 32;
    } else {
      children = String(children);
      if (shapeFlag & 64) {
        type = 16;
        children = [createTextVNode(children)];
      } else {
        type = 8;
      }
    }
    vnode.children = children;
    vnode.shapeFlag |= type;
  }
  function mergeProps(...args) {
    const ret = {};
    for (let i = 0; i < args.length; i++) {
      const toMerge = args[i];
      for (const key in toMerge) {
        if (key === "class") {
          if (ret.class !== toMerge.class) {
            ret.class = normalizeClass([ret.class, toMerge.class]);
          }
        } else if (key === "style") {
          ret.style = normalizeStyle([ret.style, toMerge.style]);
        } else if (isOn(key)) {
          const existing = ret[key];
          const incoming = toMerge[key];
          if (incoming && existing !== incoming && !(isArray(existing) && existing.includes(incoming))) {
            ret[key] = existing ? [].concat(existing, incoming) : incoming;
          }
        } else if (key !== "") {
          ret[key] = toMerge[key];
        }
      }
    }
    return ret;
  }
  function invokeVNodeHook(hook, instance, vnode, prevVNode = null) {
    callWithAsyncErrorHandling(hook, instance, 7, [
      vnode,
      prevVNode
    ]);
  }
  const emptyAppContext = createAppContext();
  let uid = 0;
  function createComponentInstance(vnode, parent, suspense) {
    const type = vnode.type;
    const appContext = (parent ? parent.appContext : vnode.appContext) || emptyAppContext;
    const instance = {
      uid: uid++,
      vnode,
      type,
      parent,
      appContext,
      root: null,
      // to be immediately set
      next: null,
      subTree: null,
      // will be set synchronously right after creation
      effect: null,
      update: null,
      // will be set synchronously right after creation
      job: null,
      scope: new EffectScope(
        true
        /* detached */
      ),
      render: null,
      proxy: null,
      exposed: null,
      exposeProxy: null,
      withProxy: null,
      provides: parent ? parent.provides : Object.create(appContext.provides),
      ids: parent ? parent.ids : ["", 0, 0],
      accessCache: null,
      renderCache: [],
      // local resolved assets
      components: null,
      directives: null,
      // resolved props and emits options
      propsOptions: normalizePropsOptions(type, appContext),
      emitsOptions: normalizeEmitsOptions(type, appContext),
      // emit
      emit: null,
      // to be set immediately
      emitted: null,
      // props default value
      propsDefaults: EMPTY_OBJ,
      // inheritAttrs
      inheritAttrs: type.inheritAttrs,
      // state
      ctx: EMPTY_OBJ,
      data: EMPTY_OBJ,
      props: EMPTY_OBJ,
      attrs: EMPTY_OBJ,
      slots: EMPTY_OBJ,
      refs: EMPTY_OBJ,
      setupState: EMPTY_OBJ,
      setupContext: null,
      // suspense related
      suspense,
      suspenseId: suspense ? suspense.pendingId : 0,
      asyncDep: null,
      asyncResolved: false,
      // lifecycle hooks
      // not using enums here because it results in computed properties
      isMounted: false,
      isUnmounted: false,
      isDeactivated: false,
      bc: null,
      c: null,
      bm: null,
      m: null,
      bu: null,
      u: null,
      um: null,
      bum: null,
      da: null,
      a: null,
      rtg: null,
      rtc: null,
      ec: null,
      sp: null
    };
    {
      instance.ctx = createDevRenderContext(instance);
    }
    instance.root = parent ? parent.root : instance;
    instance.emit = emit.bind(null, instance);
    if (vnode.ce) {
      vnode.ce(instance);
    }
    return instance;
  }
  let currentInstance = null;
  const getCurrentInstance = () => currentInstance || currentRenderingInstance;
  let internalSetCurrentInstance;
  let setInSSRSetupState;
  {
    const g = getGlobalThis();
    const registerGlobalSetter = (key, setter) => {
      let setters;
      if (!(setters = g[key])) setters = g[key] = [];
      setters.push(setter);
      return (v) => {
        if (setters.length > 1) setters.forEach((set) => set(v));
        else setters[0](v);
      };
    };
    internalSetCurrentInstance = registerGlobalSetter(
      `__VUE_INSTANCE_SETTERS__`,
      (v) => currentInstance = v
    );
    setInSSRSetupState = registerGlobalSetter(
      `__VUE_SSR_SETTERS__`,
      (v) => isInSSRComponentSetup = v
    );
  }
  const setCurrentInstance = (instance) => {
    const prev = currentInstance;
    internalSetCurrentInstance(instance);
    instance.scope.on();
    return () => {
      instance.scope.off();
      internalSetCurrentInstance(prev);
    };
  };
  const unsetCurrentInstance = () => {
    currentInstance && currentInstance.scope.off();
    internalSetCurrentInstance(null);
  };
  const isBuiltInTag = /* @__PURE__ */ makeMap("slot,component");
  function validateComponentName(name, { isNativeTag }) {
    if (isBuiltInTag(name) || isNativeTag(name)) {
      warn$1(
        "Do not use built-in or reserved HTML elements as component id: " + name
      );
    }
  }
  function isStatefulComponent(instance) {
    return instance.vnode.shapeFlag & 4;
  }
  let isInSSRComponentSetup = false;
  function setupComponent(instance, isSSR = false, optimized = false) {
    isSSR && setInSSRSetupState(isSSR);
    const { props, children } = instance.vnode;
    const isStateful = isStatefulComponent(instance);
    initProps(instance, props, isStateful, isSSR);
    initSlots(instance, children, optimized);
    const setupResult = isStateful ? setupStatefulComponent(instance, isSSR) : void 0;
    isSSR && setInSSRSetupState(false);
    return setupResult;
  }
  function setupStatefulComponent(instance, isSSR) {
    var _a;
    const Component = instance.type;
    {
      if (Component.name) {
        validateComponentName(Component.name, instance.appContext.config);
      }
      if (Component.components) {
        const names = Object.keys(Component.components);
        for (let i = 0; i < names.length; i++) {
          validateComponentName(names[i], instance.appContext.config);
        }
      }
      if (Component.directives) {
        const names = Object.keys(Component.directives);
        for (let i = 0; i < names.length; i++) {
          validateDirectiveName(names[i]);
        }
      }
      if (Component.compilerOptions && isRuntimeOnly()) {
        warn$1(
          `"compilerOptions" is only supported when using a build of Vue that includes the runtime compiler. Since you are using a runtime-only build, the options should be passed via your build tool config instead.`
        );
      }
    }
    instance.accessCache = /* @__PURE__ */ Object.create(null);
    instance.proxy = new Proxy(instance.ctx, PublicInstanceProxyHandlers);
    {
      exposePropsOnRenderContext(instance);
    }
    const { setup } = Component;
    if (setup) {
      pauseTracking();
      const setupContext = instance.setupContext = setup.length > 1 ? createSetupContext(instance) : null;
      const reset = setCurrentInstance(instance);
      const setupResult = callWithErrorHandling(
        setup,
        instance,
        0,
        [
          shallowReadonly(instance.props),
          setupContext
        ]
      );
      const isAsyncSetup = isPromise(setupResult);
      resetTracking();
      reset();
      if ((isAsyncSetup || instance.sp) && !isAsyncWrapper(instance)) {
        markAsyncBoundary(instance);
      }
      if (isAsyncSetup) {
        setupResult.then(unsetCurrentInstance, unsetCurrentInstance);
        if (isSSR) {
          return setupResult.then((resolvedResult) => {
            handleSetupResult(instance, resolvedResult, isSSR);
          }).catch((e) => {
            handleError(e, instance, 0);
          });
        } else {
          instance.asyncDep = setupResult;
          if (!instance.suspense) {
            const name = (_a = Component.name) != null ? _a : "Anonymous";
            warn$1(
              `Component <${name}>: setup function returned a promise, but no <Suspense> boundary was found in the parent component tree. A component with async setup() must be nested in a <Suspense> in order to be rendered.`
            );
          }
        }
      } else {
        handleSetupResult(instance, setupResult, isSSR);
      }
    } else {
      finishComponentSetup(instance, isSSR);
    }
  }
  function handleSetupResult(instance, setupResult, isSSR) {
    if (isFunction(setupResult)) {
      if (instance.type.__ssrInlineRender) {
        instance.ssrRender = setupResult;
      } else {
        instance.render = setupResult;
      }
    } else if (isObject(setupResult)) {
      if (isVNode(setupResult)) {
        warn$1(
          `setup() should not return VNodes directly - return a render function instead.`
        );
      }
      {
        instance.devtoolsRawSetupState = setupResult;
      }
      instance.setupState = proxyRefs(setupResult);
      {
        exposeSetupStateOnRenderContext(instance);
      }
    } else if (setupResult !== void 0) {
      warn$1(
        `setup() should return an object. Received: ${setupResult === null ? "null" : typeof setupResult}`
      );
    }
    finishComponentSetup(instance, isSSR);
  }
  const isRuntimeOnly = () => true;
  function finishComponentSetup(instance, isSSR, skipOptions) {
    const Component = instance.type;
    if (!instance.render) {
      instance.render = Component.render || NOOP;
    }
    {
      const reset = setCurrentInstance(instance);
      pauseTracking();
      try {
        applyOptions(instance);
      } finally {
        resetTracking();
        reset();
      }
    }
    if (!Component.render && instance.render === NOOP && !isSSR) {
      if (Component.template) {
        warn$1(
          `Component provided template option but runtime compilation is not supported in this build of Vue. Configure your bundler to alias "vue" to "vue/dist/vue.esm-bundler.js".`
        );
      } else {
        warn$1(`Component is missing template or render function: `, Component);
      }
    }
  }
  const attrsProxyHandlers = {
    get(target, key) {
      markAttrsAccessed();
      track(target, "get", "");
      return target[key];
    },
    set() {
      warn$1(`setupContext.attrs is readonly.`);
      return false;
    },
    deleteProperty() {
      warn$1(`setupContext.attrs is readonly.`);
      return false;
    }
  };
  function getSlotsProxy(instance) {
    return new Proxy(instance.slots, {
      get(target, key) {
        track(instance, "get", "$slots");
        return target[key];
      }
    });
  }
  function createSetupContext(instance) {
    const expose = (exposed) => {
      {
        if (instance.exposed) {
          warn$1(`expose() should be called only once per setup().`);
        }
        if (exposed != null) {
          let exposedType = typeof exposed;
          if (exposedType === "object") {
            if (isArray(exposed)) {
              exposedType = "array";
            } else if (isRef(exposed)) {
              exposedType = "ref";
            }
          }
          if (exposedType !== "object") {
            warn$1(
              `expose() should be passed a plain object, received ${exposedType}.`
            );
          }
        }
      }
      instance.exposed = exposed || {};
    };
    {
      let attrsProxy;
      let slotsProxy;
      return Object.freeze({
        get attrs() {
          return attrsProxy || (attrsProxy = new Proxy(instance.attrs, attrsProxyHandlers));
        },
        get slots() {
          return slotsProxy || (slotsProxy = getSlotsProxy(instance));
        },
        get emit() {
          return (event, ...args) => instance.emit(event, ...args);
        },
        expose
      });
    }
  }
  function getComponentPublicInstance(instance) {
    if (instance.exposed) {
      return instance.exposeProxy || (instance.exposeProxy = new Proxy(proxyRefs(markRaw(instance.exposed)), {
        get(target, key) {
          if (key in target) {
            return target[key];
          } else if (key in publicPropertiesMap) {
            return publicPropertiesMap[key](instance);
          }
        },
        has(target, key) {
          return key in target || key in publicPropertiesMap;
        }
      }));
    } else {
      return instance.proxy;
    }
  }
  const classifyRE = /(?:^|[-_])(\w)/g;
  const classify = (str) => str.replace(classifyRE, (c) => c.toUpperCase()).replace(/[-_]/g, "");
  function getComponentName(Component, includeInferred = true) {
    return isFunction(Component) ? Component.displayName || Component.name : Component.name || includeInferred && Component.__name;
  }
  function formatComponentName(instance, Component, isRoot = false) {
    let name = getComponentName(Component);
    if (!name && Component.__file) {
      const match = Component.__file.match(/([^/\\]+)\.\w+$/);
      if (match) {
        name = match[1];
      }
    }
    if (!name && instance && instance.parent) {
      const inferFromRegistry = (registry) => {
        for (const key in registry) {
          if (registry[key] === Component) {
            return key;
          }
        }
      };
      name = inferFromRegistry(
        instance.components || instance.parent.type.components
      ) || inferFromRegistry(instance.appContext.components);
    }
    return name ? classify(name) : isRoot ? `App` : `Anonymous`;
  }
  function isClassComponent(value) {
    return isFunction(value) && "__vccOpts" in value;
  }
  const computed = (getterOrOptions, debugOptions) => {
    const c = computed$1(getterOrOptions, debugOptions, isInSSRComponentSetup);
    {
      const i = getCurrentInstance();
      if (i && i.appContext.config.warnRecursiveComputed) {
        c._warnRecursive = true;
      }
    }
    return c;
  };
  function initCustomFormatter() {
    if (typeof window === "undefined") {
      return;
    }
    const vueStyle = { style: "color:#3ba776" };
    const numberStyle = { style: "color:#1677ff" };
    const stringStyle = { style: "color:#f5222d" };
    const keywordStyle = { style: "color:#eb2f96" };
    const formatter = {
      __vue_custom_formatter: true,
      header(obj) {
        if (!isObject(obj)) {
          return null;
        }
        if (obj.__isVue) {
          return ["div", vueStyle, `VueInstance`];
        } else if (isRef(obj)) {
          return [
            "div",
            {},
            ["span", vueStyle, genRefFlag(obj)],
            "<",
            // avoid debugger accessing value affecting behavior
            formatValue("_value" in obj ? obj._value : obj),
            `>`
          ];
        } else if (isReactive(obj)) {
          return [
            "div",
            {},
            ["span", vueStyle, isShallow(obj) ? "ShallowReactive" : "Reactive"],
            "<",
            formatValue(obj),
            `>${isReadonly(obj) ? ` (readonly)` : ``}`
          ];
        } else if (isReadonly(obj)) {
          return [
            "div",
            {},
            ["span", vueStyle, isShallow(obj) ? "ShallowReadonly" : "Readonly"],
            "<",
            formatValue(obj),
            ">"
          ];
        }
        return null;
      },
      hasBody(obj) {
        return obj && obj.__isVue;
      },
      body(obj) {
        if (obj && obj.__isVue) {
          return [
            "div",
            {},
            ...formatInstance(obj.$)
          ];
        }
      }
    };
    function formatInstance(instance) {
      const blocks = [];
      if (instance.type.props && instance.props) {
        blocks.push(createInstanceBlock("props", toRaw(instance.props)));
      }
      if (instance.setupState !== EMPTY_OBJ) {
        blocks.push(createInstanceBlock("setup", instance.setupState));
      }
      if (instance.data !== EMPTY_OBJ) {
        blocks.push(createInstanceBlock("data", toRaw(instance.data)));
      }
      const computed2 = extractKeys(instance, "computed");
      if (computed2) {
        blocks.push(createInstanceBlock("computed", computed2));
      }
      const injected = extractKeys(instance, "inject");
      if (injected) {
        blocks.push(createInstanceBlock("injected", injected));
      }
      blocks.push([
        "div",
        {},
        [
          "span",
          {
            style: keywordStyle.style + ";opacity:0.66"
          },
          "$ (internal): "
        ],
        ["object", { object: instance }]
      ]);
      return blocks;
    }
    function createInstanceBlock(type, target) {
      target = extend({}, target);
      if (!Object.keys(target).length) {
        return ["span", {}];
      }
      return [
        "div",
        { style: "line-height:1.25em;margin-bottom:0.6em" },
        [
          "div",
          {
            style: "color:#476582"
          },
          type
        ],
        [
          "div",
          {
            style: "padding-left:1.25em"
          },
          ...Object.keys(target).map((key) => {
            return [
              "div",
              {},
              ["span", keywordStyle, key + ": "],
              formatValue(target[key], false)
            ];
          })
        ]
      ];
    }
    function formatValue(v, asRaw = true) {
      if (typeof v === "number") {
        return ["span", numberStyle, v];
      } else if (typeof v === "string") {
        return ["span", stringStyle, JSON.stringify(v)];
      } else if (typeof v === "boolean") {
        return ["span", keywordStyle, v];
      } else if (isObject(v)) {
        return ["object", { object: asRaw ? toRaw(v) : v }];
      } else {
        return ["span", stringStyle, String(v)];
      }
    }
    function extractKeys(instance, type) {
      const Comp = instance.type;
      if (isFunction(Comp)) {
        return;
      }
      const extracted = {};
      for (const key in instance.ctx) {
        if (isKeyOfType(Comp, key, type)) {
          extracted[key] = instance.ctx[key];
        }
      }
      return extracted;
    }
    function isKeyOfType(Comp, key, type) {
      const opts = Comp[type];
      if (isArray(opts) && opts.includes(key) || isObject(opts) && key in opts) {
        return true;
      }
      if (Comp.extends && isKeyOfType(Comp.extends, key, type)) {
        return true;
      }
      if (Comp.mixins && Comp.mixins.some((m) => isKeyOfType(m, key, type))) {
        return true;
      }
    }
    function genRefFlag(v) {
      if (isShallow(v)) {
        return `ShallowRef`;
      }
      if (v.effect) {
        return `ComputedRef`;
      }
      return `Ref`;
    }
    if (window.devtoolsFormatters) {
      window.devtoolsFormatters.push(formatter);
    } else {
      window.devtoolsFormatters = [formatter];
    }
  }
  const version = "3.5.13";
  const warn = warn$1;
  /**
  * @vue/runtime-dom v3.5.13
  * (c) 2018-present Yuxi (Evan) You and Vue contributors
  * @license MIT
  **/
  let policy = void 0;
  const tt = typeof window !== "undefined" && window.trustedTypes;
  if (tt) {
    try {
      policy = /* @__PURE__ */ tt.createPolicy("vue", {
        createHTML: (val) => val
      });
    } catch (e) {
      warn(`Error creating trusted types policy: ${e}`);
    }
  }
  const unsafeToTrustedHTML = policy ? (val) => policy.createHTML(val) : (val) => val;
  const svgNS = "http://www.w3.org/2000/svg";
  const mathmlNS = "http://www.w3.org/1998/Math/MathML";
  const doc = typeof document !== "undefined" ? document : null;
  const templateContainer = doc && /* @__PURE__ */ doc.createElement("template");
  const nodeOps = {
    insert: (child, parent, anchor) => {
      parent.insertBefore(child, anchor || null);
    },
    remove: (child) => {
      const parent = child.parentNode;
      if (parent) {
        parent.removeChild(child);
      }
    },
    createElement: (tag, namespace, is, props) => {
      const el = namespace === "svg" ? doc.createElementNS(svgNS, tag) : namespace === "mathml" ? doc.createElementNS(mathmlNS, tag) : is ? doc.createElement(tag, { is }) : doc.createElement(tag);
      if (tag === "select" && props && props.multiple != null) {
        el.setAttribute("multiple", props.multiple);
      }
      return el;
    },
    createText: (text) => doc.createTextNode(text),
    createComment: (text) => doc.createComment(text),
    setText: (node, text) => {
      node.nodeValue = text;
    },
    setElementText: (el, text) => {
      el.textContent = text;
    },
    parentNode: (node) => node.parentNode,
    nextSibling: (node) => node.nextSibling,
    querySelector: (selector) => doc.querySelector(selector),
    setScopeId(el, id) {
      el.setAttribute(id, "");
    },
    // __UNSAFE__
    // Reason: innerHTML.
    // Static content here can only come from compiled templates.
    // As long as the user only uses trusted templates, this is safe.
    insertStaticContent(content, parent, anchor, namespace, start, end) {
      const before = anchor ? anchor.previousSibling : parent.lastChild;
      if (start && (start === end || start.nextSibling)) {
        while (true) {
          parent.insertBefore(start.cloneNode(true), anchor);
          if (start === end || !(start = start.nextSibling)) break;
        }
      } else {
        templateContainer.innerHTML = unsafeToTrustedHTML(
          namespace === "svg" ? `<svg>${content}</svg>` : namespace === "mathml" ? `<math>${content}</math>` : content
        );
        const template = templateContainer.content;
        if (namespace === "svg" || namespace === "mathml") {
          const wrapper = template.firstChild;
          while (wrapper.firstChild) {
            template.appendChild(wrapper.firstChild);
          }
          template.removeChild(wrapper);
        }
        parent.insertBefore(template, anchor);
      }
      return [
        // first
        before ? before.nextSibling : parent.firstChild,
        // last
        anchor ? anchor.previousSibling : parent.lastChild
      ];
    }
  };
  const vtcKey = Symbol("_vtc");
  function patchClass(el, value, isSVG) {
    const transitionClasses = el[vtcKey];
    if (transitionClasses) {
      value = (value ? [value, ...transitionClasses] : [...transitionClasses]).join(" ");
    }
    if (value == null) {
      el.removeAttribute("class");
    } else if (isSVG) {
      el.setAttribute("class", value);
    } else {
      el.className = value;
    }
  }
  const vShowOriginalDisplay = Symbol("_vod");
  const vShowHidden = Symbol("_vsh");
  const CSS_VAR_TEXT = Symbol("CSS_VAR_TEXT");
  const displayRE = /(^|;)\s*display\s*:/;
  function patchStyle(el, prev, next) {
    const style = el.style;
    const isCssString = isString(next);
    let hasControlledDisplay = false;
    if (next && !isCssString) {
      if (prev) {
        if (!isString(prev)) {
          for (const key in prev) {
            if (next[key] == null) {
              setStyle(style, key, "");
            }
          }
        } else {
          for (const prevStyle of prev.split(";")) {
            const key = prevStyle.slice(0, prevStyle.indexOf(":")).trim();
            if (next[key] == null) {
              setStyle(style, key, "");
            }
          }
        }
      }
      for (const key in next) {
        if (key === "display") {
          hasControlledDisplay = true;
        }
        setStyle(style, key, next[key]);
      }
    } else {
      if (isCssString) {
        if (prev !== next) {
          const cssVarText = style[CSS_VAR_TEXT];
          if (cssVarText) {
            next += ";" + cssVarText;
          }
          style.cssText = next;
          hasControlledDisplay = displayRE.test(next);
        }
      } else if (prev) {
        el.removeAttribute("style");
      }
    }
    if (vShowOriginalDisplay in el) {
      el[vShowOriginalDisplay] = hasControlledDisplay ? style.display : "";
      if (el[vShowHidden]) {
        style.display = "none";
      }
    }
  }
  const semicolonRE = /[^\\];\s*$/;
  const importantRE = /\s*!important$/;
  function setStyle(style, name, val) {
    if (isArray(val)) {
      val.forEach((v) => setStyle(style, name, v));
    } else {
      if (val == null) val = "";
      {
        if (semicolonRE.test(val)) {
          warn(
            `Unexpected semicolon at the end of '${name}' style value: '${val}'`
          );
        }
      }
      if (name.startsWith("--")) {
        style.setProperty(name, val);
      } else {
        const prefixed = autoPrefix(style, name);
        if (importantRE.test(val)) {
          style.setProperty(
            hyphenate(prefixed),
            val.replace(importantRE, ""),
            "important"
          );
        } else {
          style[prefixed] = val;
        }
      }
    }
  }
  const prefixes = ["Webkit", "Moz", "ms"];
  const prefixCache = {};
  function autoPrefix(style, rawName) {
    const cached = prefixCache[rawName];
    if (cached) {
      return cached;
    }
    let name = camelize(rawName);
    if (name !== "filter" && name in style) {
      return prefixCache[rawName] = name;
    }
    name = capitalize(name);
    for (let i = 0; i < prefixes.length; i++) {
      const prefixed = prefixes[i] + name;
      if (prefixed in style) {
        return prefixCache[rawName] = prefixed;
      }
    }
    return rawName;
  }
  const xlinkNS = "http://www.w3.org/1999/xlink";
  function patchAttr(el, key, value, isSVG, instance, isBoolean2 = isSpecialBooleanAttr(key)) {
    if (isSVG && key.startsWith("xlink:")) {
      if (value == null) {
        el.removeAttributeNS(xlinkNS, key.slice(6, key.length));
      } else {
        el.setAttributeNS(xlinkNS, key, value);
      }
    } else {
      if (value == null || isBoolean2 && !includeBooleanAttr(value)) {
        el.removeAttribute(key);
      } else {
        el.setAttribute(
          key,
          isBoolean2 ? "" : isSymbol(value) ? String(value) : value
        );
      }
    }
  }
  function patchDOMProp(el, key, value, parentComponent, attrName) {
    if (key === "innerHTML" || key === "textContent") {
      if (value != null) {
        el[key] = key === "innerHTML" ? unsafeToTrustedHTML(value) : value;
      }
      return;
    }
    const tag = el.tagName;
    if (key === "value" && tag !== "PROGRESS" && // custom elements may use _value internally
    !tag.includes("-")) {
      const oldValue = tag === "OPTION" ? el.getAttribute("value") || "" : el.value;
      const newValue = value == null ? (
        // #11647: value should be set as empty string for null and undefined,
        // but <input type="checkbox"> should be set as 'on'.
        el.type === "checkbox" ? "on" : ""
      ) : String(value);
      if (oldValue !== newValue || !("_value" in el)) {
        el.value = newValue;
      }
      if (value == null) {
        el.removeAttribute(key);
      }
      el._value = value;
      return;
    }
    let needRemove = false;
    if (value === "" || value == null) {
      const type = typeof el[key];
      if (type === "boolean") {
        value = includeBooleanAttr(value);
      } else if (value == null && type === "string") {
        value = "";
        needRemove = true;
      } else if (type === "number") {
        value = 0;
        needRemove = true;
      }
    }
    try {
      el[key] = value;
    } catch (e) {
      if (!needRemove) {
        warn(
          `Failed setting prop "${key}" on <${tag.toLowerCase()}>: value ${value} is invalid.`,
          e
        );
      }
    }
    needRemove && el.removeAttribute(attrName || key);
  }
  function addEventListener(el, event, handler, options) {
    el.addEventListener(event, handler, options);
  }
  function removeEventListener(el, event, handler, options) {
    el.removeEventListener(event, handler, options);
  }
  const veiKey = Symbol("_vei");
  function patchEvent(el, rawName, prevValue, nextValue, instance = null) {
    const invokers = el[veiKey] || (el[veiKey] = {});
    const existingInvoker = invokers[rawName];
    if (nextValue && existingInvoker) {
      existingInvoker.value = sanitizeEventValue(nextValue, rawName);
    } else {
      const [name, options] = parseName(rawName);
      if (nextValue) {
        const invoker = invokers[rawName] = createInvoker(
          sanitizeEventValue(nextValue, rawName),
          instance
        );
        addEventListener(el, name, invoker, options);
      } else if (existingInvoker) {
        removeEventListener(el, name, existingInvoker, options);
        invokers[rawName] = void 0;
      }
    }
  }
  const optionsModifierRE = /(?:Once|Passive|Capture)$/;
  function parseName(name) {
    let options;
    if (optionsModifierRE.test(name)) {
      options = {};
      let m;
      while (m = name.match(optionsModifierRE)) {
        name = name.slice(0, name.length - m[0].length);
        options[m[0].toLowerCase()] = true;
      }
    }
    const event = name[2] === ":" ? name.slice(3) : hyphenate(name.slice(2));
    return [event, options];
  }
  let cachedNow = 0;
  const p = /* @__PURE__ */ Promise.resolve();
  const getNow = () => cachedNow || (p.then(() => cachedNow = 0), cachedNow = Date.now());
  function createInvoker(initialValue, instance) {
    const invoker = (e) => {
      if (!e._vts) {
        e._vts = Date.now();
      } else if (e._vts <= invoker.attached) {
        return;
      }
      callWithAsyncErrorHandling(
        patchStopImmediatePropagation(e, invoker.value),
        instance,
        5,
        [e]
      );
    };
    invoker.value = initialValue;
    invoker.attached = getNow();
    return invoker;
  }
  function sanitizeEventValue(value, propName) {
    if (isFunction(value) || isArray(value)) {
      return value;
    }
    warn(
      `Wrong type passed as event handler to ${propName} - did you forget @ or : in front of your prop?
Expected function or array of functions, received type ${typeof value}.`
    );
    return NOOP;
  }
  function patchStopImmediatePropagation(e, value) {
    if (isArray(value)) {
      const originalStop = e.stopImmediatePropagation;
      e.stopImmediatePropagation = () => {
        originalStop.call(e);
        e._stopped = true;
      };
      return value.map(
        (fn) => (e2) => !e2._stopped && fn && fn(e2)
      );
    } else {
      return value;
    }
  }
  const isNativeOn = (key) => key.charCodeAt(0) === 111 && key.charCodeAt(1) === 110 && // lowercase letter
  key.charCodeAt(2) > 96 && key.charCodeAt(2) < 123;
  const patchProp = (el, key, prevValue, nextValue, namespace, parentComponent) => {
    const isSVG = namespace === "svg";
    if (key === "class") {
      patchClass(el, nextValue, isSVG);
    } else if (key === "style") {
      patchStyle(el, prevValue, nextValue);
    } else if (isOn(key)) {
      if (!isModelListener(key)) {
        patchEvent(el, key, prevValue, nextValue, parentComponent);
      }
    } else if (key[0] === "." ? (key = key.slice(1), true) : key[0] === "^" ? (key = key.slice(1), false) : shouldSetAsProp(el, key, nextValue, isSVG)) {
      patchDOMProp(el, key, nextValue);
      if (!el.tagName.includes("-") && (key === "value" || key === "checked" || key === "selected")) {
        patchAttr(el, key, nextValue, isSVG, parentComponent, key !== "value");
      }
    } else if (
      // #11081 force set props for possible async custom element
      el._isVueCE && (/[A-Z]/.test(key) || !isString(nextValue))
    ) {
      patchDOMProp(el, camelize(key), nextValue, parentComponent, key);
    } else {
      if (key === "true-value") {
        el._trueValue = nextValue;
      } else if (key === "false-value") {
        el._falseValue = nextValue;
      }
      patchAttr(el, key, nextValue, isSVG);
    }
  };
  function shouldSetAsProp(el, key, value, isSVG) {
    if (isSVG) {
      if (key === "innerHTML" || key === "textContent") {
        return true;
      }
      if (key in el && isNativeOn(key) && isFunction(value)) {
        return true;
      }
      return false;
    }
    if (key === "spellcheck" || key === "draggable" || key === "translate") {
      return false;
    }
    if (key === "form") {
      return false;
    }
    if (key === "list" && el.tagName === "INPUT") {
      return false;
    }
    if (key === "type" && el.tagName === "TEXTAREA") {
      return false;
    }
    if (key === "width" || key === "height") {
      const tag = el.tagName;
      if (tag === "IMG" || tag === "VIDEO" || tag === "CANVAS" || tag === "SOURCE") {
        return false;
      }
    }
    if (isNativeOn(key) && isString(value)) {
      return false;
    }
    return key in el;
  }
  const REMOVAL = {};
  /*! #__NO_SIDE_EFFECTS__ */
  // @__NO_SIDE_EFFECTS__
  function defineCustomElement(options, extraOptions, _createApp) {
    const Comp = /* @__PURE__ */ defineComponent(options, extraOptions);
    if (isPlainObject(Comp)) extend(Comp, extraOptions);
    class VueCustomElement extends VueElement {
      constructor(initialProps) {
        super(Comp, initialProps, _createApp);
      }
    }
    VueCustomElement.def = Comp;
    return VueCustomElement;
  }
  const BaseClass = typeof HTMLElement !== "undefined" ? HTMLElement : class {
  };
  class VueElement extends BaseClass {
    constructor(_def, _props = {}, _createApp = createApp) {
      super();
      this._def = _def;
      this._props = _props;
      this._createApp = _createApp;
      this._isVueCE = true;
      this._instance = null;
      this._app = null;
      this._nonce = this._def.nonce;
      this._connected = false;
      this._resolved = false;
      this._numberProps = null;
      this._styleChildren = /* @__PURE__ */ new WeakSet();
      this._ob = null;
      if (this.shadowRoot && _createApp !== createApp) {
        this._root = this.shadowRoot;
      } else {
        if (this.shadowRoot) {
          warn(
            `Custom element has pre-rendered declarative shadow root but is not defined as hydratable. Use \`defineSSRCustomElement\`.`
          );
        }
        if (_def.shadowRoot !== false) {
          this.attachShadow({ mode: "open" });
          this._root = this.shadowRoot;
        } else {
          this._root = this;
        }
      }
      if (!this._def.__asyncLoader) {
        this._resolveProps(this._def);
      }
    }
    connectedCallback() {
      if (!this.isConnected) return;
      if (!this.shadowRoot) {
        this._parseSlots();
      }
      this._connected = true;
      let parent = this;
      while (parent = parent && (parent.parentNode || parent.host)) {
        if (parent instanceof VueElement) {
          this._parent = parent;
          break;
        }
      }
      if (!this._instance) {
        if (this._resolved) {
          this._setParent();
          this._update();
        } else {
          if (parent && parent._pendingResolve) {
            this._pendingResolve = parent._pendingResolve.then(() => {
              this._pendingResolve = void 0;
              this._resolveDef();
            });
          } else {
            this._resolveDef();
          }
        }
      }
    }
    _setParent(parent = this._parent) {
      if (parent) {
        this._instance.parent = parent._instance;
        this._instance.provides = parent._instance.provides;
      }
    }
    disconnectedCallback() {
      this._connected = false;
      nextTick(() => {
        if (!this._connected) {
          if (this._ob) {
            this._ob.disconnect();
            this._ob = null;
          }
          this._app && this._app.unmount();
          if (this._instance) this._instance.ce = void 0;
          this._app = this._instance = null;
        }
      });
    }
    /**
     * resolve inner component definition (handle possible async component)
     */
    _resolveDef() {
      if (this._pendingResolve) {
        return;
      }
      for (let i = 0; i < this.attributes.length; i++) {
        this._setAttr(this.attributes[i].name);
      }
      this._ob = new MutationObserver((mutations) => {
        for (const m of mutations) {
          this._setAttr(m.attributeName);
        }
      });
      this._ob.observe(this, { attributes: true });
      const resolve = (def2, isAsync = false) => {
        this._resolved = true;
        this._pendingResolve = void 0;
        const { props, styles } = def2;
        let numberProps;
        if (props && !isArray(props)) {
          for (const key in props) {
            const opt = props[key];
            if (opt === Number || opt && opt.type === Number) {
              if (key in this._props) {
                this._props[key] = toNumber(this._props[key]);
              }
              (numberProps || (numberProps = /* @__PURE__ */ Object.create(null)))[camelize(key)] = true;
            }
          }
        }
        this._numberProps = numberProps;
        if (isAsync) {
          this._resolveProps(def2);
        }
        if (this.shadowRoot) {
          this._applyStyles(styles);
        } else if (styles) {
          warn(
            "Custom element style injection is not supported when using shadowRoot: false"
          );
        }
        this._mount(def2);
      };
      const asyncDef = this._def.__asyncLoader;
      if (asyncDef) {
        this._pendingResolve = asyncDef().then(
          (def2) => resolve(this._def = def2, true)
        );
      } else {
        resolve(this._def);
      }
    }
    _mount(def2) {
      if (!def2.name) {
        def2.name = "VueElement";
      }
      this._app = this._createApp(def2);
      if (def2.configureApp) {
        def2.configureApp(this._app);
      }
      this._app._ceVNode = this._createVNode();
      this._app.mount(this._root);
      const exposed = this._instance && this._instance.exposed;
      if (!exposed) return;
      for (const key in exposed) {
        if (!hasOwn(this, key)) {
          Object.defineProperty(this, key, {
            // unwrap ref to be consistent with public instance behavior
            get: () => unref(exposed[key])
          });
        } else {
          warn(`Exposed property "${key}" already exists on custom element.`);
        }
      }
    }
    _resolveProps(def2) {
      const { props } = def2;
      const declaredPropKeys = isArray(props) ? props : Object.keys(props || {});
      for (const key of Object.keys(this)) {
        if (key[0] !== "_" && declaredPropKeys.includes(key)) {
          this._setProp(key, this[key]);
        }
      }
      for (const key of declaredPropKeys.map(camelize)) {
        Object.defineProperty(this, key, {
          get() {
            return this._getProp(key);
          },
          set(val) {
            this._setProp(key, val, true, true);
          }
        });
      }
    }
    _setAttr(key) {
      if (key.startsWith("data-v-")) return;
      const has = this.hasAttribute(key);
      let value = has ? this.getAttribute(key) : REMOVAL;
      const camelKey = camelize(key);
      if (has && this._numberProps && this._numberProps[camelKey]) {
        value = toNumber(value);
      }
      this._setProp(camelKey, value, false, true);
    }
    /**
     * @internal
     */
    _getProp(key) {
      return this._props[key];
    }
    /**
     * @internal
     */
    _setProp(key, val, shouldReflect = true, shouldUpdate = false) {
      if (val !== this._props[key]) {
        if (val === REMOVAL) {
          delete this._props[key];
        } else {
          this._props[key] = val;
          if (key === "key" && this._app) {
            this._app._ceVNode.key = val;
          }
        }
        if (shouldUpdate && this._instance) {
          this._update();
        }
        if (shouldReflect) {
          const ob = this._ob;
          ob && ob.disconnect();
          if (val === true) {
            this.setAttribute(hyphenate(key), "");
          } else if (typeof val === "string" || typeof val === "number") {
            this.setAttribute(hyphenate(key), val + "");
          } else if (!val) {
            this.removeAttribute(hyphenate(key));
          }
          ob && ob.observe(this, { attributes: true });
        }
      }
    }
    _update() {
      render(this._createVNode(), this._root);
    }
    _createVNode() {
      const baseProps = {};
      if (!this.shadowRoot) {
        baseProps.onVnodeMounted = baseProps.onVnodeUpdated = this._renderSlots.bind(this);
      }
      const vnode = createVNode(this._def, extend(baseProps, this._props));
      if (!this._instance) {
        vnode.ce = (instance) => {
          this._instance = instance;
          instance.ce = this;
          instance.isCE = true;
          {
            instance.ceReload = (newStyles) => {
              if (this._styles) {
                this._styles.forEach((s) => this._root.removeChild(s));
                this._styles.length = 0;
              }
              this._applyStyles(newStyles);
              this._instance = null;
              this._update();
            };
          }
          const dispatch = (event, args) => {
            this.dispatchEvent(
              new CustomEvent(
                event,
                isPlainObject(args[0]) ? extend({ detail: args }, args[0]) : { detail: args }
              )
            );
          };
          instance.emit = (event, ...args) => {
            dispatch(event, args);
            if (hyphenate(event) !== event) {
              dispatch(hyphenate(event), args);
            }
          };
          this._setParent();
        };
      }
      return vnode;
    }
    _applyStyles(styles, owner) {
      if (!styles) return;
      if (owner) {
        if (owner === this._def || this._styleChildren.has(owner)) {
          return;
        }
        this._styleChildren.add(owner);
      }
      const nonce = this._nonce;
      for (let i = styles.length - 1; i >= 0; i--) {
        const s = document.createElement("style");
        if (nonce) s.setAttribute("nonce", nonce);
        s.textContent = styles[i];
        this.shadowRoot.prepend(s);
        {
          if (owner) {
            if (owner.__hmrId) {
              if (!this._childStyles) this._childStyles = /* @__PURE__ */ new Map();
              let entry = this._childStyles.get(owner.__hmrId);
              if (!entry) {
                this._childStyles.set(owner.__hmrId, entry = []);
              }
              entry.push(s);
            }
          } else {
            (this._styles || (this._styles = [])).push(s);
          }
        }
      }
    }
    /**
     * Only called when shadowRoot is false
     */
    _parseSlots() {
      const slots = this._slots = {};
      let n;
      while (n = this.firstChild) {
        const slotName = n.nodeType === 1 && n.getAttribute("slot") || "default";
        (slots[slotName] || (slots[slotName] = [])).push(n);
        this.removeChild(n);
      }
    }
    /**
     * Only called when shadowRoot is false
     */
    _renderSlots() {
      const outlets = (this._teleportTarget || this).querySelectorAll("slot");
      const scopeId = this._instance.type.__scopeId;
      for (let i = 0; i < outlets.length; i++) {
        const o = outlets[i];
        const slotName = o.getAttribute("name") || "default";
        const content = this._slots[slotName];
        const parent = o.parentNode;
        if (content) {
          for (const n of content) {
            if (scopeId && n.nodeType === 1) {
              const id = scopeId + "-s";
              const walker = document.createTreeWalker(n, 1);
              n.setAttribute(id, "");
              let child;
              while (child = walker.nextNode()) {
                child.setAttribute(id, "");
              }
            }
            parent.insertBefore(n, o);
          }
        } else {
          while (o.firstChild) parent.insertBefore(o.firstChild, o);
        }
        parent.removeChild(o);
      }
    }
    /**
     * @internal
     */
    _injectChildStyle(comp) {
      this._applyStyles(comp.styles, comp);
    }
    /**
     * @internal
     */
    _removeChildStyle(comp) {
      {
        this._styleChildren.delete(comp);
        if (this._childStyles && comp.__hmrId) {
          const oldStyles = this._childStyles.get(comp.__hmrId);
          if (oldStyles) {
            oldStyles.forEach((s) => this._root.removeChild(s));
            oldStyles.length = 0;
          }
        }
      }
    }
  }
  const getModelAssigner = (vnode) => {
    const fn = vnode.props["onUpdate:modelValue"] || false;
    return isArray(fn) ? (value) => invokeArrayFns(fn, value) : fn;
  };
  function onCompositionStart(e) {
    e.target.composing = true;
  }
  function onCompositionEnd(e) {
    const target = e.target;
    if (target.composing) {
      target.composing = false;
      target.dispatchEvent(new Event("input"));
    }
  }
  const assignKey = Symbol("_assign");
  const vModelText = {
    created(el, { modifiers: { lazy, trim, number } }, vnode) {
      el[assignKey] = getModelAssigner(vnode);
      const castToNumber = number || vnode.props && vnode.props.type === "number";
      addEventListener(el, lazy ? "change" : "input", (e) => {
        if (e.target.composing) return;
        let domValue = el.value;
        if (trim) {
          domValue = domValue.trim();
        }
        if (castToNumber) {
          domValue = looseToNumber(domValue);
        }
        el[assignKey](domValue);
      });
      if (trim) {
        addEventListener(el, "change", () => {
          el.value = el.value.trim();
        });
      }
      if (!lazy) {
        addEventListener(el, "compositionstart", onCompositionStart);
        addEventListener(el, "compositionend", onCompositionEnd);
        addEventListener(el, "change", onCompositionEnd);
      }
    },
    // set value on mounted so it's after min/max for type="range"
    mounted(el, { value }) {
      el.value = value == null ? "" : value;
    },
    beforeUpdate(el, { value, oldValue, modifiers: { lazy, trim, number } }, vnode) {
      el[assignKey] = getModelAssigner(vnode);
      if (el.composing) return;
      const elValue = (number || el.type === "number") && !/^0\d/.test(el.value) ? looseToNumber(el.value) : el.value;
      const newValue = value == null ? "" : value;
      if (elValue === newValue) {
        return;
      }
      if (document.activeElement === el && el.type !== "range") {
        if (lazy && value === oldValue) {
          return;
        }
        if (trim && el.value.trim() === newValue) {
          return;
        }
      }
      el.value = newValue;
    }
  };
  const vModelCheckbox = {
    // #4096 array checkboxes need to be deep traversed
    deep: true,
    created(el, _, vnode) {
      el[assignKey] = getModelAssigner(vnode);
      addEventListener(el, "change", () => {
        const modelValue = el._modelValue;
        const elementValue = getValue(el);
        const checked = el.checked;
        const assign = el[assignKey];
        if (isArray(modelValue)) {
          const index = looseIndexOf(modelValue, elementValue);
          const found = index !== -1;
          if (checked && !found) {
            assign(modelValue.concat(elementValue));
          } else if (!checked && found) {
            const filtered = [...modelValue];
            filtered.splice(index, 1);
            assign(filtered);
          }
        } else if (isSet(modelValue)) {
          const cloned = new Set(modelValue);
          if (checked) {
            cloned.add(elementValue);
          } else {
            cloned.delete(elementValue);
          }
          assign(cloned);
        } else {
          assign(getCheckboxValue(el, checked));
        }
      });
    },
    // set initial checked on mount to wait for true-value/false-value
    mounted: setChecked,
    beforeUpdate(el, binding, vnode) {
      el[assignKey] = getModelAssigner(vnode);
      setChecked(el, binding, vnode);
    }
  };
  function setChecked(el, { value, oldValue }, vnode) {
    el._modelValue = value;
    let checked;
    if (isArray(value)) {
      checked = looseIndexOf(value, vnode.props.value) > -1;
    } else if (isSet(value)) {
      checked = value.has(vnode.props.value);
    } else {
      if (value === oldValue) return;
      checked = looseEqual(value, getCheckboxValue(el, true));
    }
    if (el.checked !== checked) {
      el.checked = checked;
    }
  }
  function getValue(el) {
    return "_value" in el ? el._value : el.value;
  }
  function getCheckboxValue(el, checked) {
    const key = checked ? "_trueValue" : "_falseValue";
    return key in el ? el[key] : checked;
  }
  const systemModifiers = ["ctrl", "shift", "alt", "meta"];
  const modifierGuards = {
    stop: (e) => e.stopPropagation(),
    prevent: (e) => e.preventDefault(),
    self: (e) => e.target !== e.currentTarget,
    ctrl: (e) => !e.ctrlKey,
    shift: (e) => !e.shiftKey,
    alt: (e) => !e.altKey,
    meta: (e) => !e.metaKey,
    left: (e) => "button" in e && e.button !== 0,
    middle: (e) => "button" in e && e.button !== 1,
    right: (e) => "button" in e && e.button !== 2,
    exact: (e, modifiers) => systemModifiers.some((m) => e[`${m}Key`] && !modifiers.includes(m))
  };
  const withModifiers = (fn, modifiers) => {
    const cache = fn._withMods || (fn._withMods = {});
    const cacheKey = modifiers.join(".");
    return cache[cacheKey] || (cache[cacheKey] = (event, ...args) => {
      for (let i = 0; i < modifiers.length; i++) {
        const guard = modifierGuards[modifiers[i]];
        if (guard && guard(event, modifiers)) return;
      }
      return fn(event, ...args);
    });
  };
  const rendererOptions = /* @__PURE__ */ extend({ patchProp }, nodeOps);
  let renderer;
  function ensureRenderer() {
    return renderer || (renderer = createRenderer(rendererOptions));
  }
  const render = (...args) => {
    ensureRenderer().render(...args);
  };
  const createApp = (...args) => {
    const app2 = ensureRenderer().createApp(...args);
    {
      injectNativeTagCheck(app2);
      injectCompilerOptionsCheck(app2);
    }
    const { mount } = app2;
    app2.mount = (containerOrSelector) => {
      const container = normalizeContainer(containerOrSelector);
      if (!container) return;
      const component = app2._component;
      if (!isFunction(component) && !component.render && !component.template) {
        component.template = container.innerHTML;
      }
      if (container.nodeType === 1) {
        container.textContent = "";
      }
      const proxy = mount(container, false, resolveRootNamespace(container));
      if (container instanceof Element) {
        container.removeAttribute("v-cloak");
        container.setAttribute("data-v-app", "");
      }
      return proxy;
    };
    return app2;
  };
  function resolveRootNamespace(container) {
    if (container instanceof SVGElement) {
      return "svg";
    }
    if (typeof MathMLElement === "function" && container instanceof MathMLElement) {
      return "mathml";
    }
  }
  function injectNativeTagCheck(app2) {
    Object.defineProperty(app2.config, "isNativeTag", {
      value: (tag) => isHTMLTag(tag) || isSVGTag(tag) || isMathMLTag(tag),
      writable: false
    });
  }
  function injectCompilerOptionsCheck(app2) {
    {
      const isCustomElement = app2.config.isCustomElement;
      Object.defineProperty(app2.config, "isCustomElement", {
        get() {
          return isCustomElement;
        },
        set() {
          warn(
            `The \`isCustomElement\` config option is deprecated. Use \`compilerOptions.isCustomElement\` instead.`
          );
        }
      });
      const compilerOptions = app2.config.compilerOptions;
      const msg = `The \`compilerOptions\` config option is only respected when using a build of Vue.js that includes the runtime compiler (aka "full build"). Since you are using the runtime-only build, \`compilerOptions\` must be passed to \`@vue/compiler-dom\` in the build setup instead.
- For vue-loader: pass it via vue-loader's \`compilerOptions\` loader option.
- For vue-cli: see https://cli.vuejs.org/guide/webpack.html#modifying-options-of-a-loader
- For vite: pass it via @vitejs/plugin-vue options. See https://github.com/vitejs/vite-plugin-vue/tree/main/packages/plugin-vue#example-for-passing-options-to-vuecompiler-sfc`;
      Object.defineProperty(app2.config, "compilerOptions", {
        get() {
          warn(msg);
          return compilerOptions;
        },
        set() {
          warn(msg);
        }
      });
    }
  }
  function normalizeContainer(container) {
    if (isString(container)) {
      const res = document.querySelector(container);
      if (!res) {
        warn(
          `Failed to mount app: mount target selector "${container}" returned null.`
        );
      }
      return res;
    }
    if (window.ShadowRoot && container instanceof window.ShadowRoot && container.mode === "closed") {
      warn(
        `mounting on a ShadowRoot with \`{mode: "closed"}\` may lead to unpredictable bugs`
      );
    }
    return container;
  }
  /**
  * vue v3.5.13
  * (c) 2018-present Yuxi (Evan) You and Vue contributors
  * @license MIT
  **/
  function initDev() {
    {
      initCustomFormatter();
    }
  }
  {
    initDev();
  }
  const _export_sfc = (sfc, props) => {
    const target = sfc.__vccOpts || sfc;
    for (const [key, val] of props) {
      target[key] = val;
    }
    return target;
  };
  const _sfc_main$e = {
    __name: "LoadingState",
    setup(__props, { expose: __expose }) {
      __expose();
      const __returned__ = {};
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  };
  const _hoisted_1$d = {
    class: "loading",
    "data-state": "loading"
  };
  function _sfc_render$e(_ctx, _cache, $props, $setup, $data, $options) {
    return openBlock(), createElementBlock("div", _hoisted_1$d, [
      renderSlot(_ctx.$slots, "default", {}, () => [
        _cache[0] || (_cache[0] = createBaseVNode(
          "div",
          { class: "loading-indicator" },
          null,
          -1
          /* HOISTED */
        )),
        _cache[1] || (_cache[1] = createBaseVNode(
          "div",
          { class: "loading-text" },
          "Loading accommodations...",
          -1
          /* HOISTED */
        ))
      ])
    ]);
  }
  const LoadingState = /* @__PURE__ */ _export_sfc(_sfc_main$e, [["render", _sfc_render$e], ["__file", "/home/<USER>/Sites/personal/bookingbear/component/availability-search/src/components/availability-search/LoadingState.vue"]]);
  const _sfc_main$d = {
    __name: "ErrorState",
    props: {
      error: {
        type: String,
        required: true
      }
    },
    emits: ["retry"],
    setup(__props, { expose: __expose }) {
      __expose();
      const __returned__ = {};
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  };
  const _hoisted_1$c = {
    class: "error",
    "data-state": "error"
  };
  const _hoisted_2$c = { class: "error-message" };
  const _hoisted_3$c = {
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "none",
    stroke: "currentColor",
    "stroke-width": "2",
    "stroke-linecap": "round",
    "stroke-linejoin": "round",
    width: "16",
    height: "16",
    style: { "margin-right": "8px" }
  };
  function _sfc_render$d(_ctx, _cache, $props, $setup, $data, $options) {
    return openBlock(), createElementBlock("div", _hoisted_1$c, [
      renderSlot(_ctx.$slots, "default", { error: $props.error }, () => [
        _cache[3] || (_cache[3] = createStaticVNode('<div class="error-icon"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" width="48" height="48"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg></div><h3 class="error-title">Connection Problem</h3>', 2)),
        createBaseVNode(
          "div",
          _hoisted_2$c,
          toDisplayString($props.error),
          1
          /* TEXT */
        ),
        _cache[4] || (_cache[4] = createBaseVNode(
          "p",
          { class: "error-help-text" },
          " If this problem persists, please check your site ID or contact the accommodation provider. ",
          -1
          /* HOISTED */
        )),
        createBaseVNode("button", {
          class: "retry-button",
          onClick: _cache[0] || (_cache[0] = ($event) => _ctx.$emit("retry"))
        }, [
          (openBlock(), createElementBlock("svg", _hoisted_3$c, _cache[1] || (_cache[1] = [
            createBaseVNode(
              "path",
              { d: "M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2" },
              null,
              -1
              /* HOISTED */
            )
          ]))),
          _cache[2] || (_cache[2] = createTextVNode(" Try Again "))
        ])
      ])
    ]);
  }
  const ErrorState = /* @__PURE__ */ _export_sfc(_sfc_main$d, [["render", _sfc_render$d], ["__file", "/home/<USER>/Sites/personal/bookingbear/component/availability-search/src/components/availability-search/ErrorState.vue"]]);
  const _sfc_main$c = {
    __name: "AccommodationCard",
    props: {
      accommodation: {
        type: Object,
        required: true
      },
      apiBaseUrl: {
        type: String,
        required: true
      }
    },
    emits: ["select"],
    setup(__props, { expose: __expose, emit: __emit }) {
      __expose();
      const props = __props;
      const emit2 = __emit;
      const truncatedDescription = computed(() => {
        if (!props.accommodation.description) return "";
        const maxLength = 100;
        const desc = props.accommodation.description;
        return desc.length > maxLength ? `${desc.substring(0, maxLength)}...` : desc;
      });
      const getImageUrl = (accommodation, size = "medium") => {
        const fallbackImage = `${props.apiBaseUrl}/img/placeholder.jpg`;
        if (!accommodation || !accommodation.gallery_images || accommodation.gallery_images.length === 0) {
          return fallbackImage;
        }
        const image = accommodation.gallery_images[0];
        let imageUrl;
        if (size === "large" && image.large) {
          imageUrl = image.large;
        } else if (size === "medium" && image.medium) {
          imageUrl = image.medium;
        } else if (size === "thumbnail" && image.thumbnail) {
          imageUrl = image.thumbnail;
        } else {
          imageUrl = image.original_url || image.full_url || fallbackImage;
        }
        if (imageUrl && !imageUrl.startsWith("http")) {
          if (imageUrl.startsWith("/")) {
            return props.apiBaseUrl + imageUrl;
          } else {
            return `${props.apiBaseUrl}/${imageUrl}`;
          }
        }
        return imageUrl;
      };
      const handleImageError = (event) => {
        console.warn("Image failed to load:", event.target.src);
        const img = event.target;
        const originalSrc = img.src;
        try {
          const baseUrlDomain = new URL(props.apiBaseUrl).hostname;
          if (originalSrc.includes(baseUrlDomain)) {
            const relativeSrc = originalSrc.replace(props.apiBaseUrl, "");
            img.src = relativeSrc;
          } else if (originalSrc.startsWith("/")) {
            const absoluteSrc = props.apiBaseUrl + originalSrc;
            img.src = absoluteSrc;
          } else if (!originalSrc.startsWith("http")) {
            const absoluteSrc = `${props.apiBaseUrl}/${originalSrc}`;
            img.src = absoluteSrc;
          } else {
            img.src = `${props.apiBaseUrl}/img/placeholder.jpg`;
          }
        } catch (error) {
          console.error("Error handling image:", error);
          img.src = `${props.apiBaseUrl}/img/placeholder.jpg`;
        }
      };
      const __returned__ = { props, emit: emit2, truncatedDescription, getImageUrl, handleImageError, computed };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  };
  const _hoisted_1$b = { class: "accommodation-card" };
  const _hoisted_2$b = { class: "accommodation-image" };
  const _hoisted_3$b = ["src", "alt"];
  const _hoisted_4$a = {
    key: 0,
    class: "accommodation-rating"
  };
  const _hoisted_5$a = { class: "accommodation-details" };
  const _hoisted_6$8 = { class: "accommodation-info" };
  const _hoisted_7$6 = { class: "accommodation-name" };
  const _hoisted_8$5 = {
    key: 0,
    class: "accommodation-location"
  };
  const _hoisted_9$5 = {
    key: 1,
    class: "accommodation-description"
  };
  const _hoisted_10$3 = { class: "accommodation-footer" };
  const _hoisted_11$3 = {
    key: 0,
    class: "accommodation-price"
  };
  function _sfc_render$c(_ctx, _cache, $props, $setup, $data, $options) {
    return openBlock(), createElementBlock("div", _hoisted_1$b, [
      createBaseVNode("div", _hoisted_2$b, [
        _cache[2] || (_cache[2] = createBaseVNode(
          "div",
          { class: "accommodation-image-overlay" },
          null,
          -1
          /* HOISTED */
        )),
        createBaseVNode("img", {
          src: $setup.getImageUrl($props.accommodation),
          alt: $props.accommodation.name,
          onError: $setup.handleImageError
        }, null, 40, _hoisted_3$b),
        $props.accommodation.rating ? (openBlock(), createElementBlock("div", _hoisted_4$a, [
          _cache[1] || (_cache[1] = createBaseVNode(
            "svg",
            {
              xmlns: "http://www.w3.org/2000/svg",
              width: "16",
              height: "16",
              viewBox: "0 0 24 24",
              fill: "currentColor",
              stroke: "currentColor",
              "stroke-width": "2",
              "stroke-linecap": "round",
              "stroke-linejoin": "round",
              class: "rating-star"
            },
            [
              createBaseVNode("polygon", { points: "12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" })
            ],
            -1
            /* HOISTED */
          )),
          createBaseVNode(
            "span",
            null,
            toDisplayString($props.accommodation.rating),
            1
            /* TEXT */
          )
        ])) : createCommentVNode("v-if", true)
      ]),
      createBaseVNode("div", _hoisted_5$a, [
        createBaseVNode("div", _hoisted_6$8, [
          createBaseVNode(
            "h3",
            _hoisted_7$6,
            toDisplayString($props.accommodation.name),
            1
            /* TEXT */
          ),
          $props.accommodation.location ? (openBlock(), createElementBlock("p", _hoisted_8$5, [
            _cache[3] || (_cache[3] = createBaseVNode(
              "svg",
              {
                xmlns: "http://www.w3.org/2000/svg",
                width: "14",
                height: "14",
                viewBox: "0 0 24 24",
                fill: "none",
                stroke: "currentColor",
                "stroke-width": "2",
                "stroke-linecap": "round",
                "stroke-linejoin": "round",
                class: "location-icon"
              },
              [
                createBaseVNode("path", { d: "M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" }),
                createBaseVNode("circle", {
                  cx: "12",
                  cy: "10",
                  r: "3"
                })
              ],
              -1
              /* HOISTED */
            )),
            createTextVNode(
              " " + toDisplayString($props.accommodation.location),
              1
              /* TEXT */
            )
          ])) : createCommentVNode("v-if", true),
          $props.accommodation.description ? (openBlock(), createElementBlock(
            "div",
            _hoisted_9$5,
            toDisplayString($setup.truncatedDescription),
            1
            /* TEXT */
          )) : createCommentVNode("v-if", true)
        ]),
        createBaseVNode("div", _hoisted_10$3, [
          $props.accommodation.base_price ? (openBlock(), createElementBlock("p", _hoisted_11$3, [
            _cache[4] || (_cache[4] = createTextVNode("From ")),
            createBaseVNode(
              "span",
              null,
              toDisplayString($props.accommodation.base_price),
              1
              /* TEXT */
            ),
            _cache[5] || (_cache[5] = createTextVNode(" / pn"))
          ])) : createCommentVNode("v-if", true),
          createBaseVNode("button", {
            onClick: _cache[0] || (_cache[0] = withModifiers(($event) => _ctx.$emit("select", $props.accommodation), ["prevent"]))
          }, "Check availability")
        ])
      ])
    ]);
  }
  const AccommodationCard = /* @__PURE__ */ _export_sfc(_sfc_main$c, [["render", _sfc_render$c], ["__file", "/home/<USER>/Sites/personal/bookingbear/component/availability-search/src/components/availability-search/AccommodationCard.vue"]]);
  const _sfc_main$b = {
    __name: "AccommodationList",
    props: {
      accommodations: {
        type: Array,
        required: true
      },
      apiBaseUrl: {
        type: String,
        required: true
      }
    },
    emits: ["select"],
    setup(__props, { expose: __expose }) {
      __expose();
      const props = __props;
      const ungroupedAccommodations = computed(() => {
        return props.accommodations.filter((accommodation) => !accommodation.group);
      });
      const groupedAccommodations = computed(() => {
        const groupMap = /* @__PURE__ */ new Map();
        props.accommodations.forEach((accommodation) => {
          if (accommodation.group) {
            const groupId = accommodation.group.id;
            if (!groupMap.has(groupId)) {
              groupMap.set(groupId, {
                id: groupId,
                name: accommodation.group.name,
                description: accommodation.group.description,
                accommodations: []
              });
            }
            groupMap.get(groupId).accommodations.push(accommodation);
          }
        });
        return Array.from(groupMap.values());
      });
      const __returned__ = { props, ungroupedAccommodations, groupedAccommodations, computed, AccommodationCard };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  };
  const _hoisted_1$a = { class: "accommodations-container" };
  const _hoisted_2$a = { class: "group-title" };
  const _hoisted_3$a = {
    key: 0,
    class: "group-description"
  };
  const _hoisted_4$9 = { class: "accommodations-grid" };
  const _hoisted_5$9 = {
    key: 0,
    class: "accommodation-group"
  };
  const _hoisted_6$7 = { class: "accommodations-grid" };
  function _sfc_render$b(_ctx, _cache, $props, $setup, $data, $options) {
    return openBlock(), createElementBlock("div", _hoisted_1$a, [
      createCommentVNode(" Grouped accommodations "),
      (openBlock(true), createElementBlock(
        Fragment,
        null,
        renderList($setup.groupedAccommodations, (group, index) => {
          return openBlock(), createElementBlock("div", {
            key: index,
            class: "accommodation-group"
          }, [
            createBaseVNode(
              "h3",
              _hoisted_2$a,
              toDisplayString(group.name),
              1
              /* TEXT */
            ),
            group.description ? (openBlock(), createElementBlock(
              "p",
              _hoisted_3$a,
              toDisplayString(group.description),
              1
              /* TEXT */
            )) : createCommentVNode("v-if", true),
            createBaseVNode("div", _hoisted_4$9, [
              (openBlock(true), createElementBlock(
                Fragment,
                null,
                renderList(group.accommodations, (accommodation) => {
                  return openBlock(), createBlock($setup["AccommodationCard"], {
                    key: accommodation.id,
                    accommodation,
                    apiBaseUrl: $props.apiBaseUrl,
                    onSelect: ($event) => _ctx.$emit("select", accommodation)
                  }, null, 8, ["accommodation", "apiBaseUrl", "onSelect"]);
                }),
                128
                /* KEYED_FRAGMENT */
              ))
            ])
          ]);
        }),
        128
        /* KEYED_FRAGMENT */
      )),
      createCommentVNode(" Accommodations with no group "),
      $setup.ungroupedAccommodations.length > 0 ? (openBlock(), createElementBlock("div", _hoisted_5$9, [
        _cache[0] || (_cache[0] = createBaseVNode(
          "h3",
          { class: "group-title" },
          "Other Accommodations",
          -1
          /* HOISTED */
        )),
        createBaseVNode("div", _hoisted_6$7, [
          (openBlock(true), createElementBlock(
            Fragment,
            null,
            renderList($setup.ungroupedAccommodations, (accommodation) => {
              return openBlock(), createBlock($setup["AccommodationCard"], {
                key: accommodation.id,
                accommodation,
                apiBaseUrl: $props.apiBaseUrl,
                onSelect: ($event) => _ctx.$emit("select", accommodation)
              }, null, 8, ["accommodation", "apiBaseUrl", "onSelect"]);
            }),
            128
            /* KEYED_FRAGMENT */
          ))
        ])
      ])) : createCommentVNode("v-if", true)
    ]);
  }
  const AccommodationList = /* @__PURE__ */ _export_sfc(_sfc_main$b, [["render", _sfc_render$b], ["__file", "/home/<USER>/Sites/personal/bookingbear/component/availability-search/src/components/availability-search/AccommodationList.vue"]]);
  const _sfc_main$a = {
    __name: "AccommodationDetail",
    props: {
      accommodation: {
        type: Object,
        required: true
      },
      apiBaseUrl: {
        type: String,
        required: true
      }
    },
    setup(__props, { expose: __expose }) {
      __expose();
      const props = __props;
      const fallbackImage = `${props.apiBaseUrl}/img/placeholder.jpg`;
      const currentIndex = ref(0);
      const images = computed(
        () => props.accommodation.gallery_images && props.accommodation.gallery_images.length ? props.accommodation.gallery_images : [{ thumbnail: fallbackImage, medium: fallbackImage, large: fallbackImage, original_url: fallbackImage }]
      );
      const currentImage = computed(() => images.value[currentIndex.value]);
      function prevImage() {
        currentIndex.value = (currentIndex.value - 1 + images.value.length) % images.value.length;
      }
      function nextImage() {
        currentIndex.value = (currentIndex.value + 1) % images.value.length;
      }
      const getImageUrlFromImage = (image, size = "medium") => {
        let imageUrl;
        if (size === "large" && image.large) {
          imageUrl = image.large;
        } else if (size === "medium" && image.medium) {
          imageUrl = image.medium;
        } else if (size === "thumbnail" && image.thumbnail) {
          imageUrl = image.thumbnail;
        } else {
          imageUrl = image.original_url || image.full_url || fallbackImage;
        }
        if (imageUrl && !imageUrl.startsWith("http")) {
          if (imageUrl.startsWith("/")) {
            return props.apiBaseUrl + imageUrl;
          } else {
            return `${props.apiBaseUrl}/${imageUrl}`;
          }
        }
        return imageUrl;
      };
      const getImageUrl = (accommodation, size = "medium") => {
        if (!accommodation || !accommodation.gallery_images || accommodation.gallery_images.length === 0) {
          return fallbackImage;
        }
        const image = accommodation.gallery_images[0];
        return getImageUrlFromImage(image, size);
      };
      const handleImageError = (event) => {
        console.warn("Image failed to load:", event.target.src);
        const img = event.target;
        const originalSrc = img.src;
        try {
          const baseUrlDomain = new URL(props.apiBaseUrl).hostname;
          if (originalSrc.includes(baseUrlDomain)) {
            const relativeSrc = originalSrc.replace(props.apiBaseUrl, "");
            img.src = relativeSrc;
          } else if (originalSrc.startsWith("/")) {
            const absoluteSrc = props.apiBaseUrl + originalSrc;
            img.src = absoluteSrc;
          } else if (!originalSrc.startsWith("http")) {
            const absoluteSrc = `${props.apiBaseUrl}/${originalSrc}`;
            img.src = absoluteSrc;
          } else {
            img.src = `${props.apiBaseUrl}/img/placeholder.jpg`;
          }
        } catch (error) {
          console.error("Error handling image:", error);
          img.src = `${props.apiBaseUrl}/img/placeholder.jpg`;
        }
      };
      const __returned__ = { props, fallbackImage, currentIndex, images, currentImage, prevImage, nextImage, getImageUrlFromImage, getImageUrl, handleImageError, ref, computed };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  };
  const _hoisted_1$9 = { class: "selected-accommodation-details" };
  const _hoisted_2$9 = { class: "accommodation-image" };
  const _hoisted_3$9 = { class: "slider" };
  const _hoisted_4$8 = ["src", "alt"];
  const _hoisted_5$8 = { class: "accommodation-info" };
  const _hoisted_6$6 = { class: "accommodation-name" };
  const _hoisted_7$5 = {
    key: 0,
    class: "accommodation-location"
  };
  const _hoisted_8$4 = {
    key: 1,
    class: "accommodation-price"
  };
  const _hoisted_9$4 = {
    key: 2,
    class: "accommodation-description"
  };
  function _sfc_render$a(_ctx, _cache, $props, $setup, $data, $options) {
    return openBlock(), createElementBlock("div", _hoisted_1$9, [
      createBaseVNode("div", _hoisted_2$9, [
        createBaseVNode("div", _hoisted_3$9, [
          $setup.images.length > 1 ? (openBlock(), createElementBlock("button", {
            key: 0,
            class: "slider-arrow prev",
            onClick: $setup.prevImage
          }, _cache[0] || (_cache[0] = [
            createBaseVNode(
              "svg",
              {
                viewBox: "0 0 96 96",
                xmlns: "http://www.w3.org/2000/svg"
              },
              [
                createBaseVNode("title"),
                createBaseVNode("path", { d: "M39.3756,48.0022l30.47-25.39a6.0035,6.0035,0,0,0-7.6878-9.223L26.1563,43.3906a6.0092,6.0092,0,0,0,0,9.2231L62.1578,82.615a6.0035,6.0035,0,0,0,7.6878-9.2231Z" })
              ],
              -1
              /* HOISTED */
            )
          ]))) : createCommentVNode("v-if", true),
          createBaseVNode("img", {
            src: $setup.getImageUrlFromImage($setup.currentImage, "large"),
            alt: $props.accommodation.name,
            onError: $setup.handleImageError
          }, null, 40, _hoisted_4$8),
          $setup.images.length > 1 ? (openBlock(), createElementBlock("button", {
            key: 1,
            class: "slider-arrow next",
            onClick: $setup.nextImage
          }, _cache[1] || (_cache[1] = [
            createBaseVNode(
              "svg",
              {
                viewBox: "0 0 96 96",
                xmlns: "http://www.w3.org/2000/svg"
              },
              [
                createBaseVNode("title"),
                createBaseVNode("path", {
                  d: "M69.8437,43.3876,33.8422,13.3863a6.0035,6.0035,0,0,0-7.6878,9.223l30.47,25.39-30.47,25.39a6.0035,6.0035,0,0,0,7.6878,9.2231L69.8437,52.6106a6.0091,6.0091,0,0,0,0-9.223Z",
                  fill: "black"
                })
              ],
              -1
              /* HOISTED */
            )
          ]))) : createCommentVNode("v-if", true)
        ])
      ]),
      createBaseVNode("div", _hoisted_5$8, [
        createBaseVNode(
          "h3",
          _hoisted_6$6,
          toDisplayString($props.accommodation.name),
          1
          /* TEXT */
        ),
        $props.accommodation.location ? (openBlock(), createElementBlock(
          "p",
          _hoisted_7$5,
          toDisplayString($props.accommodation.location),
          1
          /* TEXT */
        )) : createCommentVNode("v-if", true),
        $props.accommodation.base_price ? (openBlock(), createElementBlock(
          "p",
          _hoisted_8$4,
          "From " + toDisplayString($props.accommodation.base_price) + " per night",
          1
          /* TEXT */
        )) : createCommentVNode("v-if", true),
        $props.accommodation.description ? (openBlock(), createElementBlock(
          "div",
          _hoisted_9$4,
          toDisplayString($props.accommodation.description),
          1
          /* TEXT */
        )) : createCommentVNode("v-if", true)
      ])
    ]);
  }
  const AccommodationDetail = /* @__PURE__ */ _export_sfc(_sfc_main$a, [["render", _sfc_render$a], ["__file", "/home/<USER>/Sites/personal/bookingbear/component/availability-search/src/components/availability-search/AccommodationDetail.vue"]]);
  var $e = /d{1,4}|M{1,4}|YY(?:YY)?|S{1,3}|Do|ZZ|Z|([HhMsDm])\1?|[aA]|"[^"]*"|'[^']*'/g, A = "\\d\\d?", E = "\\d\\d", Dt = "\\d{3}", gt = "\\d{4}", te = "[^\\s]+", ze = /\[([^]*?)\]/gm;
  function Ze(a, r) {
    for (var n = [], g = 0, u = a.length; g < u; g++)
      n.push(a[g].substr(0, r));
    return n;
  }
  var He = function(a) {
    return function(r, n) {
      var g = n[a].map(function(S) {
        return S.toLowerCase();
      }), u = g.indexOf(r.toLowerCase());
      return u > -1 ? u : null;
    };
  };
  function L(a) {
    for (var r = [], n = 1; n < arguments.length; n++)
      r[n - 1] = arguments[n];
    for (var g = 0, u = r; g < u.length; g++) {
      var S = u[g];
      for (var C in S)
        a[C] = S[C];
    }
    return a;
  }
  var Le = [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday"
  ], Re = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December"
  ], pt = Ze(Re, 3), Mt = Ze(Le, 3), Je = {
    dayNamesShort: Mt,
    dayNames: Le,
    monthNamesShort: pt,
    monthNames: Re,
    amPm: ["am", "pm"],
    DoFn: function(a) {
      return a + ["th", "st", "nd", "rd"][a % 10 > 3 ? 0 : (a - a % 10 !== 10 ? 1 : 0) * a % 10];
    }
  }, ie = L({}, Je), yt = function(a) {
    return ie = L(ie, a);
  }, Ve = function(a) {
    return a.replace(/[|\\{()[^$+*?.-]/g, "\\$&");
  }, Y = function(a, r) {
    for (r === void 0 && (r = 2), a = String(a); a.length < r; )
      a = "0" + a;
    return a;
  }, kt = {
    D: function(a) {
      return String(a.getDate());
    },
    DD: function(a) {
      return Y(a.getDate());
    },
    Do: function(a, r) {
      return r.DoFn(a.getDate());
    },
    d: function(a) {
      return String(a.getDay());
    },
    dd: function(a) {
      return Y(a.getDay());
    },
    ddd: function(a, r) {
      return r.dayNamesShort[a.getDay()];
    },
    dddd: function(a, r) {
      return r.dayNames[a.getDay()];
    },
    M: function(a) {
      return String(a.getMonth() + 1);
    },
    MM: function(a) {
      return Y(a.getMonth() + 1);
    },
    MMM: function(a, r) {
      return r.monthNamesShort[a.getMonth()];
    },
    MMMM: function(a, r) {
      return r.monthNames[a.getMonth()];
    },
    YY: function(a) {
      return Y(String(a.getFullYear()), 4).substr(2);
    },
    YYYY: function(a) {
      return Y(a.getFullYear(), 4);
    },
    h: function(a) {
      return String(a.getHours() % 12 || 12);
    },
    hh: function(a) {
      return Y(a.getHours() % 12 || 12);
    },
    H: function(a) {
      return String(a.getHours());
    },
    HH: function(a) {
      return Y(a.getHours());
    },
    m: function(a) {
      return String(a.getMinutes());
    },
    mm: function(a) {
      return Y(a.getMinutes());
    },
    s: function(a) {
      return String(a.getSeconds());
    },
    ss: function(a) {
      return Y(a.getSeconds());
    },
    S: function(a) {
      return String(Math.round(a.getMilliseconds() / 100));
    },
    SS: function(a) {
      return Y(Math.round(a.getMilliseconds() / 10), 2);
    },
    SSS: function(a) {
      return Y(a.getMilliseconds(), 3);
    },
    a: function(a, r) {
      return a.getHours() < 12 ? r.amPm[0] : r.amPm[1];
    },
    A: function(a, r) {
      return a.getHours() < 12 ? r.amPm[0].toUpperCase() : r.amPm[1].toUpperCase();
    },
    ZZ: function(a) {
      var r = a.getTimezoneOffset();
      return (r > 0 ? "-" : "+") + Y(Math.floor(Math.abs(r) / 60) * 100 + Math.abs(r) % 60, 4);
    },
    Z: function(a) {
      var r = a.getTimezoneOffset();
      return (r > 0 ? "-" : "+") + Y(Math.floor(Math.abs(r) / 60), 2) + ":" + Y(Math.abs(r) % 60, 2);
    }
  }, Fe = function(a) {
    return +a - 1;
  }, We = [null, A], Pe = [null, te], Ee = [
    "isPm",
    te,
    function(a, r) {
      var n = a.toLowerCase();
      return n === r.amPm[0] ? 0 : n === r.amPm[1] ? 1 : null;
    }
  ], Ae = [
    "timezoneOffset",
    "[^\\s]*?[\\+\\-]\\d\\d:?\\d\\d|[^\\s]*?Z?",
    function(a) {
      var r = (a + "").match(/([+-]|\d\d)/gi);
      if (r) {
        var n = +r[1] * 60 + parseInt(r[2], 10);
        return r[0] === "+" ? n : -n;
      }
      return 0;
    }
  ], bt = {
    D: ["day", A],
    DD: ["day", E],
    Do: ["day", A + te, function(a) {
      return parseInt(a, 10);
    }],
    M: ["month", A, Fe],
    MM: ["month", E, Fe],
    YY: [
      "year",
      E,
      function(a) {
        var r = /* @__PURE__ */ new Date(), n = +("" + r.getFullYear()).substr(0, 2);
        return +("" + (+a > 68 ? n - 1 : n) + a);
      }
    ],
    h: ["hour", A, void 0, "isPm"],
    hh: ["hour", E, void 0, "isPm"],
    H: ["hour", A],
    HH: ["hour", E],
    m: ["minute", A],
    mm: ["minute", E],
    s: ["second", A],
    ss: ["second", E],
    YYYY: ["year", gt],
    S: ["millisecond", "\\d", function(a) {
      return +a * 100;
    }],
    SS: ["millisecond", E, function(a) {
      return +a * 10;
    }],
    SSS: ["millisecond", Dt],
    d: We,
    dd: We,
    ddd: Pe,
    dddd: Pe,
    MMM: ["month", te, He("monthNamesShort")],
    MMMM: ["month", te, He("monthNames")],
    a: Ee,
    A: Ee,
    ZZ: Ae,
    Z: Ae
  }, oe = {
    default: "ddd MMM DD YYYY HH:mm:ss",
    shortDate: "M/D/YY",
    mediumDate: "MMM D, YYYY",
    longDate: "MMMM D, YYYY",
    fullDate: "dddd, MMMM D, YYYY",
    isoDate: "YYYY-MM-DD",
    isoDateTime: "YYYY-MM-DDTHH:mm:ssZ",
    shortTime: "HH:mm",
    mediumTime: "HH:mm:ss",
    longTime: "HH:mm:ss.SSS"
  }, Yt = function(a) {
    return L(oe, a);
  }, _t = function(a, r, n) {
    if (r === void 0 && (r = oe.default), n === void 0 && (n = {}), typeof a == "number" && (a = new Date(a)), Object.prototype.toString.call(a) !== "[object Date]" || isNaN(a.getTime()))
      throw new Error("Invalid Date pass to format");
    r = oe[r] || r;
    var g = [];
    r = r.replace(ze, function(S, C) {
      return g.push(C), "@@@";
    });
    var u = L(L({}, ie), n);
    return r = r.replace($e, function(S) {
      return kt[S](a, u);
    }), r.replace(/@@@/g, function() {
      return g.shift();
    });
  };
  function wt(a, r, n) {
    if (n === void 0 && (n = {}), typeof r != "string")
      throw new Error("Invalid format in fecha parse");
    if (r = oe[r] || r, a.length > 1e3)
      return null;
    var g = /* @__PURE__ */ new Date(), u = {
      year: g.getFullYear(),
      month: 0,
      day: 1,
      hour: 0,
      minute: 0,
      second: 0,
      millisecond: 0,
      isPm: null,
      timezoneOffset: null
    }, S = [], C = [], $ = r.replace(ze, function(m, o) {
      return C.push(Ve(o)), "@@@";
    }), R = {}, F = {};
    $ = Ve($).replace($e, function(m) {
      var o = bt[m], Z = o[0], ue = o[1], ae = o[3];
      if (R[Z])
        throw new Error("Invalid format. " + Z + " specified twice in format");
      return R[Z] = true, ae && (F[ae] = true), S.push(o), "(" + ue + ")";
    }), Object.keys(F).forEach(function(m) {
      if (!R[m])
        throw new Error("Invalid format. " + m + " is required in specified format");
    }), $ = $.replace(/@@@/g, function() {
      return C.shift();
    });
    var _ = a.match(new RegExp($, "i"));
    if (!_)
      return null;
    for (var f = L(L({}, ie), n), c = 1; c < _.length; c++) {
      var W = S[c - 1], M = W[0], z = W[2], ne = z ? z(_[c], f) : +_[c];
      if (ne == null)
        return null;
      u[M] = ne;
    }
    u.isPm === 1 && u.hour != null && +u.hour != 12 ? u.hour = +u.hour + 12 : u.isPm === 0 && +u.hour == 12 && (u.hour = 0);
    var I;
    if (u.timezoneOffset == null) {
      I = new Date(u.year, u.month, u.day, u.hour, u.minute, u.second, u.millisecond);
      for (var N = [
        ["month", "getMonth"],
        ["day", "getDate"],
        ["hour", "getHours"],
        ["minute", "getMinutes"],
        ["second", "getSeconds"]
      ], c = 0, v = N.length; c < v; c++)
        if (R[N[c][0]] && u[N[c][0]] !== I[N[c][1]]())
          return null;
    } else if (I = new Date(Date.UTC(u.year, u.month, u.day, u.hour, u.minute - u.timezoneOffset, u.second, u.millisecond)), u.month > 11 || u.month < 0 || u.day > 31 || u.day < 1 || u.hour > 23 || u.hour < 0 || u.minute > 59 || u.minute < 0 || u.second > 59 || u.second < 0)
      return null;
    return I;
  }
  var O = {
    format: _t,
    parse: wt,
    defaultI18n: Je,
    setGlobalDateI18n: yt,
    setGlobalDateMasks: Yt
  };
  const St = (a, r) => {
    const n = a.__vccOpts || a;
    for (const [g, u] of r)
      n[g] = u;
    return n;
  }, Ot = { style: { "pointer-events": "none" } }, Ct = { class: "h_datepicker_month_control_panel" }, It = { class: "h_datepicker_month_control_item" }, Nt = ["onClick"], Tt = { class: "h_datepicker_month_control_item" }, xt = { class: "h_datepicker_month_control_item" }, Bt = ["onClick"], Ht = { class: "h_datepicker_month_box" }, Vt = { class: "h_datepicker_weeks_container" }, Ft = { class: "h_datepicker_week_name" }, Wt = { class: "h_datepicker_dates_container" }, Pt = ["onClick", "onMouseover"], Et = {
    __name: "Vue3HotelDatePicker",
    props: {
      format: {
        default: "YYYY-MM-DD",
        type: String
      },
      startOfWeek: {
        default: "monday",
        type: String
      },
      separator: {
        default: "-",
        type: String
      },
      selectedDates: {
        default: false,
        type: [Array, Boolean]
      },
      startDate: {
        default: false,
        type: [String, Boolean]
      },
      endDate: {
        default: false,
        type: [String, Boolean]
      },
      minDate: {
        default: () => /* @__PURE__ */ new Date()
      },
      // The start view date. All the dates before this date will be disabled.
      maxDate: {
        default: () => false,
        type: Boolean
      },
      // The end view date. All the dates after this date will be disabled.
      disabledDaysOfWeek: {
        default: () => []
      },
      moveBothMonths: {
        default: false,
        type: Boolean
      },
      noCheckOutDates: {
        default: () => []
      },
      noCheckInDates: {
        default: () => []
      },
      noCheckInDaysOfWeek: {
        default: () => []
      },
      noCheckOutDaysOfWeek: {
        default: () => []
      },
      maxNights: 0,
      minNights: 1,
      singleMonthBreakpoint: {
        default: 768,
        type: [Number, String]
      },
      selectForward: Boolean,
      showSingleMonth: Boolean,
      disabledDates: {
        default: false,
        type: [Array, Boolean]
      },
      enableCheckout: Boolean,
      weekDays: {
        default: () => ["sun", "mon", "tue", "wed", "thu", "fri", "sat"],
        type: Array
      },
      monthNames: {
        default: () => ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"],
        type: Array
      },
      i18n: {
        default: () => ({
          "not selected": "Not selected",
          night: "Night",
          nights: "Nights"
        }),
        type: Object
      }
    },
    emits: {
      selected: ({ start: a, end: r }) => true
    },
    setup(a, { emit: r }) {
      const n = a, g = r, u = ref(true);
      ref(false);
      const S = ref(false), C = ref(false), $ = ref(false), R = ref(false), F = ref(n.maxNights), _ = ref(n.minNights), f = ref(n.startDate ?? false), c = ref(n.endDate ?? false);
      ref(null);
      const W = ref(null), M = ref({}), z = ref(0);
      ref(null);
      const ne = ref(null);
      S.value = false, C.value = false, $.value = false, R.value = false;
      const I = ref(n.startDate), N = ref(n.endDate), v = ref([]), m = ref({
        start: null,
        end: null
      }), o = ref({
        months: [],
        open: true,
        submitButton: false,
        clearButton: false,
        showSingleMonth: n.showSingleMonth
      });
      computed({
        error: false,
        textContent: "",
        show: false
      });
      const Z = (e) => e in n.i18n ? n.i18n[e] : "", ue = () => {
        let e = [];
        if (n.startOfWeek === "monday") {
          for (let t = 0; t < 7; t++)
            e.push(n.weekDays[(1 + t) % 7]);
          return e;
        }
        for (let t = 0; t < 7; t++)
          e.push(n.weekDays[t]);
        return e;
      }, ae = (e) => n.monthNames[e], J = (e) => {
        const t = new Date(e.valueOf());
        return new Date(t.setMonth(t.getMonth() + 1, 1));
      }, fe = (e) => {
        const t = new Date(e.valueOf());
        return new Date(t.setMonth(t.getMonth() - 1, 1));
      }, y = (e, t = null) => {
        let l = t ?? n.format;
        return O.format(e, l);
      }, K = (e, t = null) => {
        let l = t ?? n.format;
        return O.parse(e, l);
      }, Ue = () => {
        _.value = n.minNights > 1 ? n.minNights + 1 : 2, F.value = n.maxNights > 0 ? n.maxNights + 1 : 0, n.startDate && typeof n.startDate == "string" && (I.value = K(n.startDate)), n.endDate && typeof n.endDate == "string" && (N.value = K(N.value)), n.disabledDates.length > 0 && st(), n.disabledDaysOfWeek.length > 0 && lt(), I.value && N.value ? it(I.value, N.value) : ke(), ne.value && (u.value || !f.value && !c.value) && (o.value.clearButton = true), z.value = 0, W.value = false;
      }, P = (e, t) => {
        e = new Date(e);
        let l = {
          name: ae(e.getMonth()),
          month: e.getMonth(),
          year: e.getFullYear(),
          id: e.getFullYear() + e.getMonth(),
          days: [],
          nextBtn: true,
          prevBtn: true
        };
        e.setHours(0, 0, 0, 0), l.days = je(e), o.value.months.splice(t - 1, 1, l), Se(), M.value["month" + t] = e;
      }, Me = {
        date: {},
        type: "",
        day: "",
        time: "",
        isValid: false,
        isTmp: false,
        isCurrentMonth: false,
        isToday: false,
        isNoCheckIn: false,
        isNoCheckOut: false,
        isDisabled: false,
        isDayOfWeekDisabled: false,
        isFirstEnabledDate: false,
        isCheckInOnly: false,
        isDayBeforeDisabledDate: false
      }, je = (e) => {
        const t = [], l = [];
        let s;
        e.setDate(1);
        let i = e.getDay();
        const D = e.getMonth();
        if (i === 0 && n.startOfWeek === "monday" && (i = 7), i > 0)
          for (let p2 = i; p2 > 0; p2--) {
            const h = new Date(e.getTime() - 864e5 * p2);
            s = le(h.getTime()), (n.minDate && k(h, n.minDate) < 0 || n.maxDate && k(h, n.maxDate) > 0) && (s = false), t.push(
              Object.assign({}, Me, {
                date: h,
                type: "lastMonth",
                day: h.getDate(),
                time: h.getTime(),
                isValid: s
              })
            );
          }
        for (let p2 = 0; p2 < 40; p2++) {
          const h = Q(e, p2);
          s = le(h.getTime()), (n.minDate && k(h, n.minDate) < 0 || n.maxDate && k(h, n.maxDate) > 0) && (s = false), t.push(Object.assign({}, Me, {
            date: h,
            type: h.getMonth() === D ? "visibleMonth" : "nextMonth",
            day: h.getDate(),
            time: h.getTime(),
            isCurrentMonth: h.getMonth() === D,
            isValid: s
          }));
        }
        for (let p2 = 0; p2 < 6 && t[p2 * 7].type !== "nextMonth"; p2++)
          for (let h = 0; h < 7; h++) {
            let w = n.startOfWeek === "monday" ? h + 1 : h;
            w = t[p2 * 7 + w], l.push(ye(w));
          }
        return l;
      }, ye = (e) => {
        const t = y(e.time) === y(/* @__PURE__ */ new Date()), l = y(e.time) === y(n.minDate);
        let s = false, i = false, D = false, p2 = false, h = false, w = false;
        if (e.valid || e.type === "visibleMonth") {
          const me = y(e.time, "YYYY-MM-DD");
          if (n.disabledDates.length > 0) {
            const x = Ce(e.date);
            if (x[0] === false && (x[0] = _e(I.value, 1)), x[0] && x[1] && k(e.date, x[0]) && T(x[0], x[1]) - 2 > 0) {
              const De = T(x[1], e.date) - 1, ut = T(e.date, x[0]) - 1;
              (n.selectForward && De < _.value || !n.selectForward && De < _.value && ut < _.value) && (e.isValid = false), !e.isValid && n.enableCheckout && De === 2 && (w = true);
            }
            n.disabledDates.indexOf(me) > -1 ? (e.isValid = false, s = true, z.value++, W.value = e.date) : z.value = 0, e.isValid && W.value && k(e.date, W.value) > 0 && T(e.date, W.value) === 2 && (h = true);
          }
          n.disabledDaysOfWeek.length > 0 && n.disabledDaysOfWeek.indexOf(O.format(e.time, "ddd")) > -1 && (e.isValid = false, p2 = true), n.noCheckInDates.length > 0 && n.noCheckInDates.indexOf(me) > -1 && (i = true, h = false), n.noCheckOutDates.length > 0 && n.noCheckOutDates.indexOf(me) > -1 && (D = true), n.noCheckInDaysOfWeek.length > 0 && n.noCheckInDaysOfWeek.indexOf(O.format(e.time, "ddd")) > -1 && (i = true, h = false), n.noCheckOutDaysOfWeek.length > 0 && n.noCheckOutDaysOfWeek.indexOf(O.format(e.time, "ddd")) > -1 && (D = true);
        }
        return e.isToday = t, e.isDisabled = s, e.isCheckOutEnabled = !(s && n.enableCheckout && z.value === 1), e.isDayBeforeDisabledDate = w, e.isCheckInOnly = l || h, e.isNoCheckIn = i, e.isNoCheckOut = D, e.isDayOfWeekDisabled = p2, e;
      }, Ge = () => {
        for (let e = 0; e < o.value.months.length; e++)
          for (let t = 0; t < o.value.months[e].days.length; t++) {
            const l = parseInt(o.value.months[e].days[t].time, 10), s = new Date(l);
            let i;
            i = le(s.getTime()), (n.minDate && k(s, n.minDate) < 0 || n.maxDate && k(s, n.maxDate) > 0) && (i = false), o.value.months[e].days[t].isValid = i, o.value.months[e].days[t] = ye(o.value.months[e].days[t]);
          }
      }, ke = () => {
        P(n.minDate, 1), P(J(n.minDate), 2), j();
      }, qe = (e, t, ...l) => {
        if (l.length > 0 && l[0] !== void 0 && l[0], e.getTime() > t.getTime()) {
          let i = t;
          t = e, e = i, i = null;
        }
        let s = true;
        if ((n.minDate && k(e, n.minDate) < 0 || n.maxDate && k(t, n.maxDate) > 0) && (s = false), !s) {
          ke();
          return;
        }
        e.setTime(e.getTime() + 12 * 60 * 60 * 1e3), t.setTime(t.getTime() + 12 * 60 * 60 * 1e3), f.value = e.getTime(), c.value = t.getTime(), k(e, t) > 0 && re(e, t) === 0 && (t = J(e)), re(e, t) === 0 && (t = J(e)), P(e, 1), P(t, 2), se(), j(), Ye();
      }, se = () => {
        if (!(!f.value && !c.value))
          for (let e = 0; e < o.value.months.length; e++)
            for (let t = 0; t < o.value.months[e].days.length; t++) {
              const l = o.value.months[e].days[t], s = parseInt(l.time, 10);
              f.value && c.value && c.value >= s && f.value <= s || f.value && !c.value && y(f.value, "YYYY-MM-DD") === y(s, "YYYY-MM-DD") ? l.isSelected = true : l.isSelected = false, f.value && y(f.value, "YYYY-MM-DD") === y(s, "YYYY-MM-DD") ? l.isFirstDaySelected = true : l.isFirstDaySelected = false, c.value && y(c.value, "YYYY-MM-DD") === y(s, "YYYY-MM-DD") ? l.isLastDaySelected = true : l.isLastDaySelected = false, o.value.months[e].days[t] = Object.assign({}, l);
            }
      }, Ke = (e, t) => {
        if (!e.isValid || !e.isCurrentMonth)
          return;
        const l = m.value.start === null, s = parseInt(e.time, 10);
        if (!(m.value.start && m.value.start.time === e.time)) {
          if (l) {
            if (e.isNoCheckIn)
              return;
          } else if (f.value && (f.value > s && e.isNoCheckIn || m.value.start && m.value.start.isNoCheckIn && f.value > s || e.isNoCheckOut && s > f.value))
            return;
          if (l ? (m.value.start = e, f.value = s, c.value = false) : f.value && (m.value.end = e, c.value = s, Ie()), f.value && c.value && f.value > c.value) {
            const i = c.value;
            c.value = f.value, f.value = i;
          }
          f.value = parseInt(f.value, 10), c.value = parseInt(c.value, 10), at(), f.value && !c.value && Oe(e), Se(), Ye(), f.value && c.value && Ge(), se(), c.value && g("selected", { start: f.value, end: c.value }), m.value.end && Qe();
        }
      }, Qe = () => {
        m.value.start = null, m.value.end = null;
      }, le = (e) => {
        if (e = parseInt(e, 10), n.minDate && k(e, n.minDate) < 0 || n.maxDate && k(e, n.maxDate) > 0)
          return false;
        if (f.value && !c.value) {
          if (F.value > 0 && T(e, f.value) > F.value || _.value > 0 && T(e, f.value) > 1 && T(e, f.value) < _.value || n.selectForward && e < f.value)
            return false;
          if (n.disabledDates.length > 0) {
            const t = Ce(new Date(parseInt(f.value, 10)));
            if (t[0] && k(e, t[0]) <= 0 || t[1] && k(e, t[1]) >= 0)
              return false;
          }
          if (n.disabledDaysOfWeek.length > 0) {
            const t = rt(new Date(parseInt(f.value, 10)));
            if (t[0] && k(e, t[0]) <= 0 || t[1] && k(e, t[1]) >= 0)
              return false;
          }
        }
        return true;
      }, be = () => {
        for (let e = 0; e < o.value.months.length; e++)
          for (let t = 0; t < o.value.months[e].days.length; t++)
            o.value.months[e].days[t].selected = false, o.value.months[e].days[t].isFirstDaySelected = false, o.value.months[e].days[t].isLastDaySelected = false;
        return true;
      }, Ye = () => {
        const e = T(c.value, f.value);
        (F.value && e > F.value || _.value && e < _.value) && (f.value = false, c.value = false, be());
      }, Q = (e, t) => {
        const l = new Date(e);
        return l.setDate(l.getDate() + t), l;
      }, _e = (e, t) => {
        const l = new Date(e);
        return l.setDate(l.getDate() - t), l;
      }, T = (e, t) => Math.abs(we(e) - we(t)) + 1, k = (e, t) => {
        const l = parseInt(y(e, "YYYYMMDD"), 10) - parseInt(y(t, "YYYYMMDD"), 10);
        return l > 0 ? 1 : l === 0 ? 0 : -1;
      }, re = (e, t) => {
        const l = parseInt(y(e, "YYYYMM"), 10) - parseInt(y(t, "YYYYMM"), 10);
        return l > 0 ? 1 : l === 0 ? 0 : -1;
      }, we = (e) => Math.round(Xe(e) / 864e5), Xe = (e) => (typeof e == "object" && e.getTime && (e = e.getTime()), typeof e == "string" && !e.match(/\d{13}/) && (e = K(e).getTime()), e = parseInt(e, 10) - (/* @__PURE__ */ new Date()).getTimezoneOffset() * 60 * 1e3, e), et = (e, t) => {
        const l = t === 1;
        let s = l ? M.value.month2 : M.value.month1;
        return s = J(s), !ce() && !l && re(s, M.value.month2) >= 0 || U(s) ? false : (n.moveBothMonths && l && P(M.value.month2, 1), P(s, t + 1), se(), j(), true);
      }, tt2 = (e, t) => {
        const l = t === 1;
        let s = l ? M.value.month2 : M.value.month1;
        return s = fe(s), l && re(s, M.value.month1) <= 0 || U(s) ? false : (n.moveBothMonths && !l && P(M.value.month1, 2), P(s, t + 1), se(), j(), true);
      }, ce = () => n.showSingleMonth || nt(), nt = () => typeof window > "u" ? false : window.innerWidth < +n.singleMonthBreakpoint, U = (e) => {
        const t = new Date(e.valueOf());
        return n.minDate && new Date(t.getFullYear(), t.getMonth() + 1, 0, 23, 59, 59) < n.minDate || n.maxDate && new Date(t.getFullYear(), t.getMonth(), 1) > n.maxDate;
      }, j = () => {
        if (ce()) {
          U(fe(M.value.month1)) ? o.value.months[0].prevBtn = false : o.value.months[0].prevBtn = true, U(J(M.value.month1)) ? o.value.months[0].nextBtn = false : o.value.months[0].nextBtn = true;
          return;
        }
        const e = parseInt(y(M.value.month1, "YYYYMM"), 10), t = parseInt(y(M.value.month2, "YYYYMM"), 10), l = Math.abs(e - t);
        l > 1 && l !== 89 ? (o.value.months[0].nextBtn = true, o.value.months[1].prevBtn = true) : (o.value.months[0].nextBtn = false, o.value.months[1].prevBtn = false), U(fe(M.value.month1)) ? o.value.months[0].prevBtn = false : o.value.months[0].prevBtn = true, U(J(M.value.month2)) ? o.value.months[1].nextBtn = false : o.value.months[1].nextBtn = true;
      }, Se = () => {
        const e = f.value && !c.value;
        for (let t = 0; t < o.value.months.length; t++)
          for (let l = 0; l < o.value.months[t].days.length; l++) {
            let s = o.value.months[t].days[l];
            if (!s.isValid && s.isTmp && (s.isTmp = false, s.isTmpValid ? s.isValid = true : s.isTmpValid = true), e) {
              if (s.isCurrentMonth && (s.isValid || s.isDisabled || s.isBeforeDisabledDate)) {
                const i = parseInt(s.time, 10);
                le(i) ? (s.isValid = true, s.isTmp = true, s.isDisabled = false) : (s.valid || (s.isTmpValid = false), s.isValid = false, s.isTmp = true);
              }
            } else
              (s.checkOutEnabled || s.beforeDisabledDate) && (s.isValid = false, s.beforeDisabledDate || (s.isDisabled = true));
            o.value.months[t].days.splice(l, 1, s);
          }
        return true;
      }, Oe = (e, t) => {
        const l = parseInt(e.time, 10);
        if (e.isValid && Ne(e)) {
          for (let s = 0; s < o.value.months.length; s++)
            for (let i = 0; i < o.value.months[s].days.length; i++) {
              const D = parseInt(o.value.months[s].days[i].time, 10);
              D === l ? o.value.months[s].days[i].isHovering = true : o.value.months[s].days[i].isHovering = false, f.value && !c.value && (f.value < D && l >= D || f.value > D && l <= D) ? o.value.months[s].days[i].isHovering = true : o.value.months[s].days[i].isHovering = false;
            }
          f.value && !c.value && (b.count = T(l, f.value) - 1);
        }
      }, at = () => {
        for (let e = 0; e < o.value.months.length; e++)
          for (let t = 0; t < o.value.months[e].days.length; t++)
            o.value.months[e].days[t].isHovering = false;
      }, st = () => {
        const e = [];
        for (let t = 0; t < n.disabledDates.length; t++)
          e[t] = O.parse(n.disabledDates[t], "YYYY-MM-DD");
        e.sort((t, l) => t - l), v.value = e;
      }, Ce = (e) => {
        let t = [false, false];
        if (e < v.value[0])
          n.enableCheckout ? t = [false, Q(v.value[0], 1)] : t = [false, v.value[0]];
        else if (e > v.value[v.value.length - 1])
          t = [v.value[v.value.length - 1], false];
        else {
          let l = v.value.length, s = v.value.length;
          const i = Math.abs(new Date(0, 0, 0).valueOf());
          let D = i, p2 = -i, h = 0, w;
          for (w = 0; w < v.value.length; ++w)
            h = e - v.value[w], h < 0 && h > p2 && (s = w, p2 = h), h > 0 && h < D && (l = w, D = h);
          v.value[l] && (t[0] = v.value[l]), typeof v.value[l] > "u" ? t[1] = false : n.enableCheckout ? t[1] = Q(v.value[s], 1) : t[1] = v.value[s];
        }
        return t;
      }, he = ref([]), lt = () => {
        const e = [], t = [], l = /* @__PURE__ */ new Date();
        for (let s = 0; s < 7; s++) {
          const i = Q(l, s);
          e[O.format(i, "d")] = O.format(i, "ddd");
        }
        for (let s = 0; s < n.disabledDaysOfWeek.length; s++)
          t.push(e.indexOf(n.disabledDaysOfWeek[s]));
        t.sort(), he.value = t;
      }, rt = (e) => {
        const t = [false, false];
        for (let l = 0; l < 7; l++) {
          const s = _e(e, l);
          if (he.value.indexOf(parseInt(O.format(s, "d"), 10)) > -1) {
            t[0] = s;
            break;
          }
        }
        for (let l = 0; l < 7; l++) {
          const s = Q(e, l);
          if (he.value.indexOf(parseInt(O.format(s, "d"), 10)) > -1) {
            t[1] = s;
            break;
          }
        }
        return t;
      }, it = (e, t) => {
        typeof e == "string" && typeof t == "string" ? (e = K(e), t = K(t)) : (e = new Date(e.getTime()), t = new Date(t.getTime())), qe(e, t);
      }, b = reactive(
        {
          show: false,
          top: 0,
          left: 0,
          width: 0,
          count: 0,
          error: false
        }
      ), Ie = () => b.show = false, Ne = (e) => !(!e.isValid || !e.isCurrentMonth), ot = (e, t) => {
        if (!m.value.start || m.value.start && m.value.end || !Ne(t))
          return false;
        b.show = true;
        const l = Te.value.getBoundingClientRect(), s = e.target.getBoundingClientRect();
        b.top = s.top - l.top - 35, b.left = s.left - l.left, b.width = s.width;
      }, Te = ref(null), de = () => {
        o.value.showSingleMonth = ce();
      };
      watch(() => o.value.showSingleMonth, () => {
        j();
      });
      const xe = () => typeof client < "u";
      onUnmounted(() => {
        window.removeEventListener("resize", () => de());
      });
      const ve = ref(false);
      return onMounted(() => {
        window.addEventListener("resize", () => de()), de(), j(), ve.value = true;
      }), Ue(), (e, t) => (openBlock(), createElementBlock("div", {
        class: "h_datepicker",
        ref_key: "parent",
        ref: Te
      }, [
        createBaseVNode("div", {
          class: normalizeClass([{ h_datepicker_invisible: !b.show, visible: b.show }, "h_datepicker_popup"]),
          style: normalizeStyle(
            { top: b.top + "px", left: b.left + "px", width: b.width + "px" }
          )
        }, [
          createBaseVNode("div", Ot, [
            renderSlot(e.$slots, "popup", {
              nights: b.count
            }, () => [
              createTextVNode(toDisplayString(b.count ? b.count + " " + (b.count > 1 ? Z("nights") : Z("night")) : Z("not selected")), 1)
            ], true)
          ])
        ], 6),
        (openBlock(true), createElementBlock(Fragment, null, renderList(o.value.months, (l, s) => (openBlock(), createElementBlock("div", {
          key: l.id,
          class: normalizeClass(["h_datepicker_month", {
            h_datepicker_hidden: s === 1 && o.value.showSingleMonth,
            h_datepicker_two_month_display: !o.value.showSingleMonth,
            h_datepicker_one_month_display: o.value.showSingleMonth,
            "h_datepicker_month-1": s === 0,
            "h_datepicker_month-2": s === 1
          }])
        }, [
          createBaseVNode("div", Ct, [
            createBaseVNode("div", It, [
              ve.value || !xe ? (openBlock(), createElementBlock("div", {
                key: 0,
                onClick: (i) => tt2(l, s),
                class: normalizeClass(["h_datepicker_month_control_btn", { h_datepicker_invisible: !l.prevBtn }])
              }, [
                renderSlot(e.$slots, "prev", {}, () => [
                  createTextVNode(" <<")
                ], true)
              ], 10, Nt)) : createCommentVNode("", true)
            ]),
            createBaseVNode("div", Tt, [
              renderSlot(e.$slots, "month", { month: l }, () => [
                createTextVNode(toDisplayString(l.name) + " " + toDisplayString(l.year), 1)
              ], true)
            ]),
            createBaseVNode("div", xt, [
              ve.value || !xe ? (openBlock(), createElementBlock("div", {
                key: 0,
                onClick: (i) => et(l, s),
                class: normalizeClass(["h_datepicker_month_control_btn", { h_datepicker_invisible: !l.nextBtn }])
              }, [
                renderSlot(e.$slots, "next", {}, () => [
                  createTextVNode(" >>")
                ], true)
              ], 10, Bt)) : createCommentVNode("", true)
            ])
          ]),
          createBaseVNode("div", Ht, [
            createBaseVNode("div", Vt, [
              (openBlock(true), createElementBlock(Fragment, null, renderList(ue(), (i) => (openBlock(), createElementBlock("div", Ft, [
                renderSlot(e.$slots, "weekday", { weekday: i }, () => [
                  createTextVNode(toDisplayString(i), 1)
                ], true)
              ]))), 256))
            ]),
            createBaseVNode("div", Wt, [
              (openBlock(true), createElementBlock(Fragment, null, renderList(l.days, (i) => (openBlock(), createElementBlock("div", {
                onClick: (D) => Ke(i),
                onMouseout: t[0] || (t[0] = (D) => Ie()),
                onMouseover: (D) => Oe(i) || ot(D, i),
                class: normalizeClass([{
                  h_datepicker_notCurrentMonth: !i.isCurrentMonth,
                  h_datepicker_valid: i.isValid,
                  h_datepicker_invalid: !i.isValid,
                  h_datepicker_tmp_invalid: !i.isTmpValid,
                  h_datepicker_tmp_valid: i.isTmpValid,
                  h_datepicker_disabled: i.isDisabled,
                  h_datepicker_checkout_enabled: i.isCheckOutEnabled,
                  h_datepicker_checkout_disabled: !i.isCheckOutEnabled,
                  h_datepicker_checkin_enabled: !i.isNoCheckIn,
                  h_datepicker_checkin_disabled: i.isNoCheckIn,
                  h_datepicker_before_disabled_date: i.isDayBeforeDisabledDate,
                  h_datepicker_first_day_selected: i.isFirstDaySelected,
                  h_datepicker_last_day_selected: i.isLastDaySelected,
                  h_datepicker_selected: i.isSelected,
                  h_datepicker_hovering: i.isHovering
                }, "h_datepicker_day"])
              }, [
                renderSlot(e.$slots, "day", { day: i }, () => [
                  createTextVNode(toDisplayString(i.day), 1)
                ], true)
              ], 42, Pt))), 256))
            ])
          ])
        ], 2))), 128))
      ], 512));
    }
  }, $t = /* @__PURE__ */ St(Et, [["__scopeId", "data-v-e09819b5"]]);
  const dateFormat = "YYYY-MM-DD";
  const _sfc_main$9 = {
    __name: "DateRangePicker",
    props: {
      accommodation: {
        type: Object,
        required: true
      },
      apiBaseUrl: {
        type: String,
        required: true
      },
      authToken: {
        type: String,
        required: true
      },
      componentVersion: {
        type: String,
        required: true
      },
      // Optional props to override default behavior
      customMinDate: {
        type: [String, Date],
        default: null
      },
      customMaxDate: {
        type: [String, Date],
        default: null
      },
      dateRangeMonths: {
        type: Number,
        default: 12
        // How many months ahead to fetch unavailability data
      }
    },
    emits: ["date-range-selected", "error", "unavailability-loaded"],
    setup(__props, { expose: __expose, emit: __emit }) {
      __expose();
      const props = __props;
      const emit2 = __emit;
      const unavailabilityData = ref(null);
      const disabledDates = ref([]);
      const loading = ref(false);
      const error = ref(null);
      const minDate = computed(() => {
        var _a;
        if (props.customMinDate) {
          return props.customMinDate;
        }
        const today = /* @__PURE__ */ new Date();
        const minBookingNotice = ((_a = unavailabilityData.value) == null ? void 0 : _a.minimum_booking_notice) || 0;
        if (minBookingNotice > 0) {
          const minDate2 = new Date(today);
          minDate2.setDate(today.getDate() + minBookingNotice);
          return minDate2.toISOString().split("T")[0];
        }
        return today.toISOString().split("T")[0];
      });
      const maxDate = computed(() => {
        if (props.customMaxDate) {
          return props.customMaxDate;
        }
        const maxDate2 = /* @__PURE__ */ new Date();
        maxDate2.setFullYear(maxDate2.getFullYear() + 2);
        return maxDate2.toISOString().split("T")[0];
      });
      const minNights = computed(() => {
        var _a;
        return ((_a = unavailabilityData.value) == null ? void 0 : _a.minimum_stay) || 1;
      });
      const maxNights = computed(() => {
        return 0;
      });
      const getAuthHeaders = () => {
        return {
          "Content-Type": "application/json",
          "Accept": "application/json",
          "X-Widget-Version": props.componentVersion,
          "Authorization": `Bearer ${props.authToken}`
        };
      };
      const fetchUnavailabilityData = async () => {
        var _a;
        if (!((_a = props.accommodation) == null ? void 0 : _a.id)) {
          return;
        }
        loading.value = true;
        error.value = null;
        try {
          const startDate = /* @__PURE__ */ new Date();
          const endDate = /* @__PURE__ */ new Date();
          endDate.setMonth(endDate.getMonth() + props.dateRangeMonths);
          const url = `${props.apiBaseUrl}/api/accommodations/${props.accommodation.id}/unavailability-data`;
          const params = new URLSearchParams({
            format: "dates_array",
            start_date: startDate.toISOString().split("T")[0],
            end_date: endDate.toISOString().split("T")[0]
          });
          const response = await fetch(`${url}?${params}`, {
            method: "GET",
            headers: getAuthHeaders()
          });
          if (!response.ok) {
            throw new Error(`Failed to fetch unavailability data: ${response.status}`);
          }
          const result = await response.json();
          unavailabilityData.value = result.data;
          disabledDates.value = result.data.unavailable_dates || [];
          emit2("unavailability-loaded", result.data);
        } catch (err) {
          error.value = err.message;
          emit2("error", { error: err.message, originalError: err });
        } finally {
          loading.value = false;
        }
      };
      const handleDateSelection = (dates) => {
        const startDate = typeof dates.start === "number" ? new Date(dates.start * 1e3) : new Date(dates.start);
        const endDate = typeof dates.end === "number" ? new Date(dates.end * 1e3) : new Date(dates.end);
        const dateRange = {
          start: startDate,
          end: endDate
        };
        emit2("date-range-selected", dateRange);
      };
      watch(() => {
        var _a;
        return (_a = props.accommodation) == null ? void 0 : _a.id;
      }, (newId, oldId) => {
        if (newId && newId !== oldId) {
          fetchUnavailabilityData();
        }
      });
      onMounted(() => {
        var _a;
        if ((_a = props.accommodation) == null ? void 0 : _a.id) {
          fetchUnavailabilityData();
        }
      });
      const __returned__ = { props, emit: emit2, unavailabilityData, disabledDates, loading, error, dateFormat, minDate, maxDate, minNights, maxNights, getAuthHeaders, fetchUnavailabilityData, handleDateSelection, ref, onMounted, watch, computed, get Vue3HotelDatePicker() {
        return $t;
      } };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  };
  const _hoisted_1$8 = { class: "date-picker-container" };
  const _hoisted_2$8 = {
    key: 0,
    class: "loading-state"
  };
  const _hoisted_3$8 = {
    key: 1,
    class: "error-state"
  };
  function _sfc_render$9(_ctx, _cache, $props, $setup, $data, $options) {
    return openBlock(), createElementBlock("div", _hoisted_1$8, [
      createCommentVNode(" Loading state "),
      $setup.loading ? (openBlock(), createElementBlock("div", _hoisted_2$8, _cache[0] || (_cache[0] = [
        createBaseVNode(
          "div",
          { class: "loading-spinner" },
          null,
          -1
          /* HOISTED */
        ),
        createBaseVNode(
          "span",
          null,
          "Loading available dates...",
          -1
          /* HOISTED */
        )
      ]))) : createCommentVNode("v-if", true),
      createCommentVNode(" Error state "),
      $setup.error && !$setup.loading ? (openBlock(), createElementBlock("div", _hoisted_3$8, [
        createBaseVNode(
          "span",
          null,
          toDisplayString($setup.error),
          1
          /* TEXT */
        ),
        createBaseVNode("button", {
          onClick: $setup.fetchUnavailabilityData,
          class: "retry-button"
        }, "Retry")
      ])) : createCommentVNode("v-if", true),
      createCommentVNode(" Date picker "),
      !$setup.loading ? (openBlock(), createBlock($setup["Vue3HotelDatePicker"], {
        key: 2,
        "disabled-dates": $setup.disabledDates,
        "min-date": $setup.minDate,
        "max-date": $setup.maxDate,
        "min-nights": $setup.minNights,
        "max-nights": $setup.maxNights,
        format: $setup.dateFormat,
        onSelected: $setup.handleDateSelection
      }, null, 8, ["disabled-dates", "min-date", "max-date", "min-nights", "max-nights"])) : createCommentVNode("v-if", true)
    ]);
  }
  const DateRangePicker = /* @__PURE__ */ _export_sfc(_sfc_main$9, [["render", _sfc_render$9], ["__scopeId", "data-v-139a8c91"], ["__file", "/home/<USER>/Sites/personal/bookingbear/component/availability-search/src/components/availability-search/DateRangePicker.vue"]]);
  const _sfc_main$8 = {
    __name: "OccupancySelector",
    props: {
      modelValue: {
        type: Number,
        required: true
      },
      minOccupancy: {
        type: Number,
        default: 1
      },
      maxOccupancy: {
        type: Number,
        default: 10
      },
      showLimits: {
        type: Boolean,
        default: true
      }
    },
    emits: ["update:modelValue"],
    setup(__props, { expose: __expose, emit: __emit }) {
      __expose();
      const props = __props;
      const emit2 = __emit;
      const updateValue = (value) => {
        const numValue = parseInt(value, 10);
        if (isNaN(numValue)) return;
        const boundedValue = Math.min(Math.max(numValue, props.minOccupancy), props.maxOccupancy);
        emit2("update:modelValue", boundedValue);
      };
      const incrementOccupancy = () => {
        if (props.modelValue < props.maxOccupancy) {
          emit2("update:modelValue", props.modelValue + 1);
        }
      };
      const decrementOccupancy = () => {
        if (props.modelValue > props.minOccupancy) {
          emit2("update:modelValue", props.modelValue - 1);
        }
      };
      const __returned__ = { props, emit: emit2, updateValue, incrementOccupancy, decrementOccupancy, computed };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  };
  const _hoisted_1$7 = { class: "occupancy-selector" };
  const _hoisted_2$7 = { class: "occupancy-input-container" };
  const _hoisted_3$7 = ["disabled"];
  const _hoisted_4$7 = ["value", "min", "max"];
  const _hoisted_5$7 = ["disabled"];
  const _hoisted_6$5 = {
    key: 0,
    class: "occupancy-limits"
  };
  function _sfc_render$8(_ctx, _cache, $props, $setup, $data, $options) {
    return openBlock(), createElementBlock("div", _hoisted_1$7, [
      _cache[1] || (_cache[1] = createBaseVNode(
        "label",
        { for: "number-of-persons" },
        "Number of Persons:",
        -1
        /* HOISTED */
      )),
      createBaseVNode("div", _hoisted_2$7, [
        createBaseVNode("button", {
          class: "occupancy-button",
          onClick: $setup.decrementOccupancy,
          disabled: $props.modelValue <= $props.minOccupancy
        }, "-", 8, _hoisted_3$7),
        createBaseVNode("input", {
          type: "number",
          id: "number-of-persons",
          value: $props.modelValue,
          onInput: _cache[0] || (_cache[0] = ($event) => $setup.updateValue($event.target.value)),
          min: $props.minOccupancy,
          max: $props.maxOccupancy,
          class: "occupancy-input"
        }, null, 40, _hoisted_4$7),
        createBaseVNode("button", {
          class: "occupancy-button",
          onClick: $setup.incrementOccupancy,
          disabled: $props.modelValue >= $props.maxOccupancy
        }, "+", 8, _hoisted_5$7)
      ]),
      $props.showLimits ? (openBlock(), createElementBlock("div", _hoisted_6$5, [
        createBaseVNode(
          "small",
          null,
          "Min: " + toDisplayString($props.minOccupancy) + ", Max: " + toDisplayString($props.maxOccupancy),
          1
          /* TEXT */
        )
      ])) : createCommentVNode("v-if", true)
    ]);
  }
  const OccupancySelector = /* @__PURE__ */ _export_sfc(_sfc_main$8, [["render", _sfc_render$8], ["__file", "/home/<USER>/Sites/personal/bookingbear/component/availability-search/src/components/availability-search/OccupancySelector.vue"]]);
  const _sfc_main$7 = {
    __name: "BookingForm",
    props: {
      accommodationId: {
        type: [Number, String],
        required: true
      },
      isSubmitting: {
        type: Boolean,
        default: false
      }
    },
    emits: ["submit", "show-terms"],
    setup(__props, { expose: __expose, emit: __emit }) {
      __expose();
      const props = __props;
      const emit2 = __emit;
      const formData = reactive({
        first_name: "",
        last_name: "",
        email: "",
        contact_number: "",
        notes: "",
        terms: false
      });
      const errors = reactive({});
      const validateForm = () => {
        const newErrors = {};
        if (!formData.first_name.trim()) {
          newErrors.first_name = "First name is required";
        }
        if (!formData.last_name.trim()) {
          newErrors.last_name = "Last name is required";
        }
        if (!formData.email.trim()) {
          newErrors.email = "Email is required";
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
          newErrors.email = "Please enter a valid email address";
        }
        if (!formData.contact_number.trim()) {
          newErrors.contact_number = "Contact number is required";
        }
        if (!formData.terms) {
          newErrors.terms = "You must agree to the terms and conditions";
        }
        Object.keys(errors).forEach((key) => {
          delete errors[key];
        });
        Object.keys(newErrors).forEach((key) => {
          errors[key] = newErrors[key];
        });
        return Object.keys(newErrors).length === 0;
      };
      const submitForm = () => {
        if (validateForm()) {
          emit2("submit", { ...formData });
        }
      };
      const __returned__ = { props, emit: emit2, formData, errors, validateForm, submitForm, ref, reactive };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  };
  const _hoisted_1$6 = { class: "booking-form" };
  const _hoisted_2$6 = {
    key: 0,
    class: "error-message"
  };
  const _hoisted_3$6 = {
    key: 0,
    class: "error-message"
  };
  const _hoisted_4$6 = {
    key: 0,
    class: "error-message"
  };
  const _hoisted_5$6 = {
    key: 0,
    class: "error-message"
  };
  const _hoisted_6$4 = {
    key: 0,
    class: "error-message"
  };
  const _hoisted_7$4 = { class: "checkbox-container" };
  const _hoisted_8$3 = {
    key: 0,
    class: "error-message"
  };
  const _hoisted_9$3 = ["value"];
  const _hoisted_10$2 = { class: "form-actions" };
  const _hoisted_11$2 = ["disabled"];
  function _sfc_render$7(_ctx, _cache, $props, $setup, $data, $options) {
    return openBlock(), createElementBlock("div", _hoisted_1$6, [
      _cache[15] || (_cache[15] = createBaseVNode(
        "h4",
        { class: "form-title" },
        "Request your booking",
        -1
        /* HOISTED */
      )),
      createBaseVNode(
        "form",
        {
          onSubmit: withModifiers($setup.submitForm, ["prevent"])
        },
        [
          createBaseVNode(
            "div",
            {
              class: normalizeClass(["form-group", { "has-error": $setup.errors.first_name }])
            },
            [
              _cache[7] || (_cache[7] = createBaseVNode(
                "label",
                { for: "first-name" },
                "First Name *",
                -1
                /* HOISTED */
              )),
              withDirectives(createBaseVNode(
                "input",
                {
                  type: "text",
                  id: "first-name",
                  "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => $setup.formData.first_name = $event),
                  required: "",
                  class: "form-control"
                },
                null,
                512
                /* NEED_PATCH */
              ), [
                [vModelText, $setup.formData.first_name]
              ]),
              $setup.errors.first_name ? (openBlock(), createElementBlock(
                "div",
                _hoisted_2$6,
                toDisplayString($setup.errors.first_name),
                1
                /* TEXT */
              )) : createCommentVNode("v-if", true)
            ],
            2
            /* CLASS */
          ),
          createBaseVNode(
            "div",
            {
              class: normalizeClass(["form-group", { "has-error": $setup.errors.last_name }])
            },
            [
              _cache[8] || (_cache[8] = createBaseVNode(
                "label",
                { for: "last-name" },
                "Last Name *",
                -1
                /* HOISTED */
              )),
              withDirectives(createBaseVNode(
                "input",
                {
                  type: "text",
                  id: "last-name",
                  "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => $setup.formData.last_name = $event),
                  required: "",
                  class: "form-control"
                },
                null,
                512
                /* NEED_PATCH */
              ), [
                [vModelText, $setup.formData.last_name]
              ]),
              $setup.errors.last_name ? (openBlock(), createElementBlock(
                "div",
                _hoisted_3$6,
                toDisplayString($setup.errors.last_name),
                1
                /* TEXT */
              )) : createCommentVNode("v-if", true)
            ],
            2
            /* CLASS */
          ),
          createBaseVNode(
            "div",
            {
              class: normalizeClass(["form-group", { "has-error": $setup.errors.email }])
            },
            [
              _cache[9] || (_cache[9] = createBaseVNode(
                "label",
                { for: "email" },
                "Email *",
                -1
                /* HOISTED */
              )),
              withDirectives(createBaseVNode(
                "input",
                {
                  type: "email",
                  id: "email",
                  "onUpdate:modelValue": _cache[2] || (_cache[2] = ($event) => $setup.formData.email = $event),
                  required: "",
                  class: "form-control"
                },
                null,
                512
                /* NEED_PATCH */
              ), [
                [vModelText, $setup.formData.email]
              ]),
              $setup.errors.email ? (openBlock(), createElementBlock(
                "div",
                _hoisted_4$6,
                toDisplayString($setup.errors.email),
                1
                /* TEXT */
              )) : createCommentVNode("v-if", true)
            ],
            2
            /* CLASS */
          ),
          createBaseVNode(
            "div",
            {
              class: normalizeClass(["form-group", { "has-error": $setup.errors.contact_number }])
            },
            [
              _cache[10] || (_cache[10] = createBaseVNode(
                "label",
                { for: "contact-number" },
                "Contact Number *",
                -1
                /* HOISTED */
              )),
              withDirectives(createBaseVNode(
                "input",
                {
                  type: "tel",
                  id: "contact-number",
                  "onUpdate:modelValue": _cache[3] || (_cache[3] = ($event) => $setup.formData.contact_number = $event),
                  required: "",
                  class: "form-control"
                },
                null,
                512
                /* NEED_PATCH */
              ), [
                [vModelText, $setup.formData.contact_number]
              ]),
              $setup.errors.contact_number ? (openBlock(), createElementBlock(
                "div",
                _hoisted_5$6,
                toDisplayString($setup.errors.contact_number),
                1
                /* TEXT */
              )) : createCommentVNode("v-if", true)
            ],
            2
            /* CLASS */
          ),
          createBaseVNode(
            "div",
            {
              class: normalizeClass(["form-group", { "has-error": $setup.errors.notes }])
            },
            [
              _cache[11] || (_cache[11] = createBaseVNode(
                "label",
                { for: "booking-notes" },
                "Notes",
                -1
                /* HOISTED */
              )),
              withDirectives(createBaseVNode(
                "textarea",
                {
                  id: "booking-notes",
                  "onUpdate:modelValue": _cache[4] || (_cache[4] = ($event) => $setup.formData.notes = $event),
                  class: "form-control",
                  rows: "3",
                  placeholder: "Special requests or additional information about your booking"
                },
                null,
                512
                /* NEED_PATCH */
              ), [
                [vModelText, $setup.formData.notes]
              ]),
              $setup.errors.notes ? (openBlock(), createElementBlock(
                "div",
                _hoisted_6$4,
                toDisplayString($setup.errors.notes),
                1
                /* TEXT */
              )) : createCommentVNode("v-if", true)
            ],
            2
            /* CLASS */
          ),
          createBaseVNode(
            "div",
            {
              class: normalizeClass(["form-group terms-checkbox", { "has-error": $setup.errors.terms }])
            },
            [
              createBaseVNode("label", _hoisted_7$4, [
                withDirectives(createBaseVNode(
                  "input",
                  {
                    type: "checkbox",
                    id: "terms",
                    "onUpdate:modelValue": _cache[5] || (_cache[5] = ($event) => $setup.formData.terms = $event)
                  },
                  null,
                  512
                  /* NEED_PATCH */
                ), [
                  [vModelCheckbox, $setup.formData.terms]
                ]),
                _cache[12] || (_cache[12] = createBaseVNode(
                  "span",
                  { class: "checkmark" },
                  null,
                  -1
                  /* HOISTED */
                )),
                _cache[13] || (_cache[13] = createTextVNode(" I agree to the ")),
                createBaseVNode("a", {
                  href: "#",
                  onClick: _cache[6] || (_cache[6] = withModifiers(($event) => _ctx.$emit("show-terms"), ["prevent"]))
                }, "terms and conditions")
              ]),
              $setup.errors.terms ? (openBlock(), createElementBlock(
                "div",
                _hoisted_8$3,
                toDisplayString($setup.errors.terms),
                1
                /* TEXT */
              )) : createCommentVNode("v-if", true)
            ],
            2
            /* CLASS */
          ),
          createCommentVNode(" Hidden fields "),
          createBaseVNode("input", {
            type: "hidden",
            name: "accommodation_id",
            value: $props.accommodationId
          }, null, 8, _hoisted_9$3),
          _cache[14] || (_cache[14] = createBaseVNode(
            "input",
            {
              type: "hidden",
              name: "booking_status_id",
              value: "2"
            },
            null,
            -1
            /* HOISTED */
          )),
          createBaseVNode("div", _hoisted_10$2, [
            createBaseVNode("button", {
              type: "submit",
              class: "submit-booking-button",
              disabled: $props.isSubmitting
            }, toDisplayString($props.isSubmitting ? "Processing..." : "Request Booking"), 9, _hoisted_11$2)
          ])
        ],
        32
        /* NEED_HYDRATION */
      )
    ]);
  }
  const BookingForm = /* @__PURE__ */ _export_sfc(_sfc_main$7, [["render", _sfc_render$7], ["__file", "/home/<USER>/Sites/personal/bookingbear/component/availability-search/src/components/availability-search/BookingForm.vue"]]);
  const _sfc_main$6 = {
    __name: "PricingBreakdown",
    props: {
      availabilityResult: {
        type: Object,
        required: true
      },
      dateRange: {
        type: Object,
        required: true
      },
      numberOfPersons: {
        type: Number,
        required: true
      }
    },
    setup(__props, { expose: __expose }) {
      __expose();
      const props = __props;
      const formatPrice = (price) => {
        if (!price) return "R0.00";
        return `R${parseFloat(price).toFixed(2)}`;
      };
      const formatDate = (date) => {
        if (!date) return "";
        const options = { year: "numeric", month: "long", day: "numeric" };
        return date.toLocaleDateString("en-ZA", options);
      };
      const calculateNights = () => {
        if (!props.dateRange.start || !props.dateRange.end) return 0;
        const startDate = new Date(props.dateRange.start.toDateString());
        const endDate = new Date(props.dateRange.end.toDateString());
        const diffTime = endDate.getTime() - startDate.getTime();
        const diffDays = Math.ceil(diffTime / (1e3 * 60 * 60 * 24));
        return diffDays;
      };
      const __returned__ = { props, formatPrice, formatDate, calculateNights };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  };
  const _hoisted_1$5 = { class: "pricing-breakdown" };
  const _hoisted_2$5 = { class: "price-details" };
  const _hoisted_3$5 = { class: "price-row" };
  const _hoisted_4$5 = { class: "price-value" };
  const _hoisted_5$5 = {
    key: 0,
    class: "price-row"
  };
  const _hoisted_6$3 = { class: "price-value" };
  const _hoisted_7$3 = { class: "price-row total" };
  const _hoisted_8$2 = { class: "price-value" };
  const _hoisted_9$2 = { class: "booking-dates" };
  const _hoisted_10$1 = { class: "date-row" };
  const _hoisted_11$1 = { class: "date-value" };
  const _hoisted_12$1 = { class: "date-row" };
  const _hoisted_13$1 = { class: "date-value" };
  const _hoisted_14$1 = { class: "date-row" };
  const _hoisted_15$1 = { class: "date-value" };
  const _hoisted_16$1 = { class: "date-row" };
  const _hoisted_17 = { class: "date-value" };
  function _sfc_render$6(_ctx, _cache, $props, $setup, $data, $options) {
    return openBlock(), createElementBlock("div", _hoisted_1$5, [
      _cache[7] || (_cache[7] = createBaseVNode(
        "div",
        { class: "availability-title-wrapper" },
        [
          createBaseVNode("div", { class: "availability-icon" }, "✓"),
          createBaseVNode("h4", { class: "availability-title" }, "Available for your selected dates!")
        ],
        -1
        /* HOISTED */
      )),
      createBaseVNode("div", _hoisted_2$5, [
        createBaseVNode("div", _hoisted_3$5, [
          _cache[0] || (_cache[0] = createBaseVNode(
            "span",
            { class: "price-label" },
            "Base price:",
            -1
            /* HOISTED */
          )),
          createBaseVNode(
            "span",
            _hoisted_4$5,
            toDisplayString($setup.formatPrice($props.availabilityResult.base_price || $props.availabilityResult.total_price)),
            1
            /* TEXT */
          )
        ]),
        $props.availabilityResult.additional_person_price ? (openBlock(), createElementBlock("div", _hoisted_5$5, [
          _cache[1] || (_cache[1] = createBaseVNode(
            "span",
            { class: "price-label" },
            "Additional person(s):",
            -1
            /* HOISTED */
          )),
          createBaseVNode(
            "span",
            _hoisted_6$3,
            toDisplayString($setup.formatPrice($props.availabilityResult.additional_person_price)),
            1
            /* TEXT */
          )
        ])) : createCommentVNode("v-if", true),
        createBaseVNode("div", _hoisted_7$3, [
          _cache[2] || (_cache[2] = createBaseVNode(
            "span",
            { class: "price-label" },
            "Total price:",
            -1
            /* HOISTED */
          )),
          createBaseVNode(
            "span",
            _hoisted_8$2,
            toDisplayString($setup.formatPrice($props.availabilityResult.total_price)),
            1
            /* TEXT */
          )
        ]),
        createBaseVNode("div", _hoisted_9$2, [
          createBaseVNode("div", _hoisted_10$1, [
            _cache[3] || (_cache[3] = createBaseVNode(
              "span",
              { class: "date-label" },
              "Check-in:",
              -1
              /* HOISTED */
            )),
            createBaseVNode(
              "span",
              _hoisted_11$1,
              toDisplayString($setup.formatDate($props.dateRange.start)),
              1
              /* TEXT */
            )
          ]),
          createBaseVNode("div", _hoisted_12$1, [
            _cache[4] || (_cache[4] = createBaseVNode(
              "span",
              { class: "date-label" },
              "Check-out:",
              -1
              /* HOISTED */
            )),
            createBaseVNode(
              "span",
              _hoisted_13$1,
              toDisplayString($setup.formatDate($props.dateRange.end)),
              1
              /* TEXT */
            )
          ]),
          createBaseVNode("div", _hoisted_14$1, [
            _cache[5] || (_cache[5] = createBaseVNode(
              "span",
              { class: "date-label" },
              "Nights:",
              -1
              /* HOISTED */
            )),
            createBaseVNode(
              "span",
              _hoisted_15$1,
              toDisplayString($setup.calculateNights()),
              1
              /* TEXT */
            )
          ]),
          createBaseVNode("div", _hoisted_16$1, [
            _cache[6] || (_cache[6] = createBaseVNode(
              "span",
              { class: "date-label" },
              "Guests:",
              -1
              /* HOISTED */
            )),
            createBaseVNode(
              "span",
              _hoisted_17,
              toDisplayString($props.numberOfPersons),
              1
              /* TEXT */
            )
          ])
        ])
      ])
    ]);
  }
  const PricingBreakdown = /* @__PURE__ */ _export_sfc(_sfc_main$6, [["render", _sfc_render$6], ["__file", "/home/<USER>/Sites/personal/bookingbear/component/availability-search/src/components/availability-search/PricingBreakdown.vue"]]);
  const _sfc_main$5 = {
    __name: "BookingSuccess",
    props: {
      accommodation: {
        type: Object,
        required: true
      },
      dateRange: {
        type: Object,
        required: true
      },
      numberOfPersons: {
        type: Number,
        required: true
      },
      totalPrice: {
        type: Number,
        required: true
      },
      bookingReference: {
        type: String,
        required: true
      },
      notes: {
        type: String,
        default: ""
      }
    },
    emits: ["reset"],
    setup(__props, { expose: __expose }) {
      __expose();
      const formatPrice = (price) => {
        if (!price) return "R0.00";
        return `R${parseFloat(price).toFixed(2)}`;
      };
      const formatDate = (date) => {
        if (!date) return "";
        const options = { year: "numeric", month: "long", day: "numeric" };
        return date.toLocaleDateString("en-ZA", options);
      };
      const __returned__ = { formatPrice, formatDate };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  };
  const _hoisted_1$4 = { class: "booking-success" };
  const _hoisted_2$4 = { class: "booking-summary" };
  const _hoisted_3$4 = { class: "summary-row" };
  const _hoisted_4$4 = { class: "summary-value" };
  const _hoisted_5$4 = { class: "summary-row" };
  const _hoisted_6$2 = { class: "summary-value" };
  const _hoisted_7$2 = { class: "summary-row" };
  const _hoisted_8$1 = { class: "summary-value" };
  const _hoisted_9$1 = { class: "summary-row" };
  const _hoisted_10 = { class: "summary-value" };
  const _hoisted_11 = { class: "summary-row" };
  const _hoisted_12 = { class: "summary-value" };
  const _hoisted_13 = { class: "summary-row" };
  const _hoisted_14 = { class: "summary-value" };
  const _hoisted_15 = {
    key: 0,
    class: "summary-row"
  };
  const _hoisted_16 = { class: "summary-value" };
  function _sfc_render$5(_ctx, _cache, $props, $setup, $data, $options) {
    return openBlock(), createElementBlock("div", _hoisted_1$4, [
      _cache[9] || (_cache[9] = createStaticVNode('<div class="success-icon"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg></div><h3 class="success-title">Booking Requested!</h3><p class="success-message">Your booking has been requested. We will be in touch shortly to complete the booking.</p>', 3)),
      createBaseVNode("div", _hoisted_2$4, [
        _cache[8] || (_cache[8] = createBaseVNode(
          "h4",
          null,
          "Booking Details",
          -1
          /* HOISTED */
        )),
        createBaseVNode("div", _hoisted_3$4, [
          _cache[1] || (_cache[1] = createBaseVNode(
            "span",
            { class: "summary-label" },
            "Accommodation:",
            -1
            /* HOISTED */
          )),
          createBaseVNode(
            "span",
            _hoisted_4$4,
            toDisplayString($props.accommodation.name),
            1
            /* TEXT */
          )
        ]),
        createBaseVNode("div", _hoisted_5$4, [
          _cache[2] || (_cache[2] = createBaseVNode(
            "span",
            { class: "summary-label" },
            "Check-in:",
            -1
            /* HOISTED */
          )),
          createBaseVNode(
            "span",
            _hoisted_6$2,
            toDisplayString($setup.formatDate($props.dateRange.start)),
            1
            /* TEXT */
          )
        ]),
        createBaseVNode("div", _hoisted_7$2, [
          _cache[3] || (_cache[3] = createBaseVNode(
            "span",
            { class: "summary-label" },
            "Check-out:",
            -1
            /* HOISTED */
          )),
          createBaseVNode(
            "span",
            _hoisted_8$1,
            toDisplayString($setup.formatDate($props.dateRange.end)),
            1
            /* TEXT */
          )
        ]),
        createBaseVNode("div", _hoisted_9$1, [
          _cache[4] || (_cache[4] = createBaseVNode(
            "span",
            { class: "summary-label" },
            "Guests:",
            -1
            /* HOISTED */
          )),
          createBaseVNode(
            "span",
            _hoisted_10,
            toDisplayString($props.numberOfPersons),
            1
            /* TEXT */
          )
        ]),
        createBaseVNode("div", _hoisted_11, [
          _cache[5] || (_cache[5] = createBaseVNode(
            "span",
            { class: "summary-label" },
            "Total Price:",
            -1
            /* HOISTED */
          )),
          createBaseVNode(
            "span",
            _hoisted_12,
            toDisplayString($setup.formatPrice($props.totalPrice)),
            1
            /* TEXT */
          )
        ]),
        createBaseVNode("div", _hoisted_13, [
          _cache[6] || (_cache[6] = createBaseVNode(
            "span",
            { class: "summary-label" },
            "Booking Reference:",
            -1
            /* HOISTED */
          )),
          createBaseVNode(
            "span",
            _hoisted_14,
            toDisplayString($props.bookingReference),
            1
            /* TEXT */
          )
        ]),
        $props.notes ? (openBlock(), createElementBlock("div", _hoisted_15, [
          _cache[7] || (_cache[7] = createBaseVNode(
            "span",
            { class: "summary-label" },
            "Notes:",
            -1
            /* HOISTED */
          )),
          createBaseVNode(
            "span",
            _hoisted_16,
            toDisplayString($props.notes),
            1
            /* TEXT */
          )
        ])) : createCommentVNode("v-if", true)
      ]),
      createBaseVNode("button", {
        class: "new-search-button",
        onClick: _cache[0] || (_cache[0] = ($event) => _ctx.$emit("reset"))
      }, " Make Another Booking ")
    ]);
  }
  const BookingSuccess = /* @__PURE__ */ _export_sfc(_sfc_main$5, [["render", _sfc_render$5], ["__file", "/home/<USER>/Sites/personal/bookingbear/component/availability-search/src/components/availability-search/BookingSuccess.vue"]]);
  const _sfc_main$4 = {
    __name: "AvailabilityChecker",
    props: {
      accommodation: {
        type: Object,
        required: true
      },
      apiBaseUrl: {
        type: String,
        required: true
      },
      authToken: {
        type: String,
        required: true
      },
      componentVersion: {
        type: String,
        required: true
      }
    },
    emits: [
      "check-availability",
      "availability-result",
      "booking-created",
      "error",
      "show-terms",
      "reset",
      "unavailability-loaded"
    ],
    setup(__props, { expose: __expose, emit: __emit }) {
      var _a;
      const props = __props;
      const emit2 = __emit;
      const dateRange = ref({ start: null, end: null });
      const numberOfPersons = ref(((_a = props.accommodation) == null ? void 0 : _a.min_occupancy) || 1);
      const availabilityResult = ref(null);
      const isChecking = ref(false);
      const bookingForm = ref({
        first_name: "",
        last_name: "",
        email: "",
        contact_number: "",
        notes: "",
        terms: false
      });
      const bookingInProgress = ref(false);
      const bookingSuccess = ref(false);
      const bookingReference = ref("");
      const setDateRange = (range) => {
        dateRange.value = range;
      };
      const handleDatePickerError = (errorData) => {
        emit2("error", errorData);
      };
      const handleUnavailabilityLoaded = (unavailabilityData) => {
        emit2("unavailability-loaded", unavailabilityData);
      };
      const getAuthHeaders = () => {
        return {
          "Content-Type": "application/json",
          "Accept": "application/json",
          "X-Widget-Version": props.componentVersion,
          "Authorization": `Bearer ${props.authToken}`
        };
      };
      const normalizeToLocalMidnight = (date) => {
        const localDate = new Date(date);
        localDate.setMinutes(localDate.getMinutes() - localDate.getTimezoneOffset());
        return localDate.toISOString().split("T")[0];
      };
      const getUnavailabilityData = async (format = "detailed") => {
        if (!props.accommodation) {
          return null;
        }
        try {
          const url = `${props.apiBaseUrl}/api/accommodations/{id}/unavailability-data`.replace("{id}", props.accommodation.id);
          const params = new URLSearchParams({
            format
            // Optional: add date range filters
            // start_date: '2024-01-01',
            // end_date: '2024-12-31'
          });
          const response = await fetch(`${url}?${params}`, {
            method: "GET",
            headers: getAuthHeaders()
          });
          if (!response.ok) {
            throw new Error(`API error: ${response.status}`);
          }
          const result = await response.json();
          return result.data;
        } catch (err) {
          emit2("error", { error: `Failed to fetch unavailability data: ${err.message}`, originalError: err });
          return null;
        }
      };
      const checkAvailability = async () => {
        if (!props.accommodation || !dateRange.value.start || !dateRange.value.end) {
          return;
        }
        isChecking.value = true;
        availabilityResult.value = null;
        try {
          const url = `${props.apiBaseUrl}/api/accommodations/{id}/check-availability`.replace("{id}", props.accommodation.id);
          const requestData = {
            start_date: normalizeToLocalMidnight(dateRange.value.start),
            end_date: normalizeToLocalMidnight(dateRange.value.end),
            number_of_persons: numberOfPersons.value
          };
          const response = await fetch(url, {
            method: "POST",
            headers: getAuthHeaders(),
            body: JSON.stringify(requestData)
          });
          if (!response.ok) {
            throw new Error(`API error: ${response.status}`);
          }
          const result = await response.json();
          availabilityResult.value = result;
          emit2("availability-result", { result });
        } catch (err) {
          emit2("error", { error: `Failed to check availability: ${err.message}`, originalError: err });
        } finally {
          isChecking.value = false;
        }
      };
      const submitBooking = async (formData) => {
        bookingInProgress.value = true;
        bookingForm.value = formData;
        try {
          const bookingData = {
            accommodation_id: props.accommodation.id,
            first_name: formData.first_name,
            last_name: formData.last_name,
            email: formData.email,
            contact_number: formData.contact_number,
            start_date: normalizeToLocalMidnight(dateRange.value.start),
            end_date: normalizeToLocalMidnight(dateRange.value.end),
            occupancy: numberOfPersons.value,
            booking_status_id: 2,
            // Pending status
            notes: formData.notes
          };
          const bookingsEndpoint = `${props.apiBaseUrl}/api/bookings`;
          const response = await fetch(bookingsEndpoint, {
            method: "POST",
            headers: getAuthHeaders(),
            body: JSON.stringify(bookingData)
          });
          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || "Failed to create booking");
          }
          const result = await response.json();
          processBookingResult(result);
        } catch (err) {
          emit2("error", { error: `Failed to create booking: ${err.message}`, originalError: err });
        } finally {
          bookingInProgress.value = false;
        }
      };
      const processBookingResult = (result) => {
        bookingReference.value = `#${result.booking.id}`;
        bookingSuccess.value = true;
        emit2("booking-created", { booking: result.booking });
      };
      watch(dateRange, () => {
        if (availabilityResult.value) {
          availabilityResult.value = null;
          bookingSuccess.value = false;
        }
      }, { deep: true });
      watch(numberOfPersons, () => {
        if (availabilityResult.value) {
          availabilityResult.value = null;
          bookingSuccess.value = false;
        }
      });
      __expose({
        getUnavailabilityData,
        checkAvailability
      });
      const __returned__ = { props, emit: emit2, dateRange, numberOfPersons, availabilityResult, isChecking, bookingForm, bookingInProgress, bookingSuccess, bookingReference, setDateRange, handleDatePickerError, handleUnavailabilityLoaded, getAuthHeaders, normalizeToLocalMidnight, getUnavailabilityData, checkAvailability, submitBooking, processBookingResult, ref, watch, DateRangePicker, OccupancySelector, BookingForm, PricingBreakdown, BookingSuccess };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  };
  const _hoisted_1$3 = { class: "availability-check" };
  const _hoisted_2$3 = ["disabled"];
  const _hoisted_3$3 = {
    key: 0,
    class: "availability-result"
  };
  const _hoisted_4$3 = {
    key: 0,
    class: "available"
  };
  const _hoisted_5$3 = { class: "availability-result-container" };
  const _hoisted_6$1 = { class: "not-available" };
  const _hoisted_7$1 = { class: "availability-message" };
  const _hoisted_8 = { key: 0 };
  const _hoisted_9 = { key: 1 };
  function _sfc_render$4(_ctx, _cache, $props, $setup, $data, $options) {
    var _a, _b;
    return openBlock(), createElementBlock("div", _hoisted_1$3, [
      _cache[5] || (_cache[5] = createBaseVNode(
        "h3",
        null,
        "Check Availability",
        -1
        /* HOISTED */
      )),
      createVNode($setup["DateRangePicker"], {
        accommodation: $props.accommodation,
        "api-base-url": $props.apiBaseUrl,
        "auth-token": $props.authToken,
        "component-version": $props.componentVersion,
        onDateRangeSelected: $setup.setDateRange,
        onError: $setup.handleDatePickerError,
        onUnavailabilityLoaded: $setup.handleUnavailabilityLoaded
      }, null, 8, ["accommodation", "api-base-url", "auth-token", "component-version"]),
      createVNode($setup["OccupancySelector"], {
        modelValue: $setup.numberOfPersons,
        "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => $setup.numberOfPersons = $event),
        minOccupancy: ((_a = $props.accommodation) == null ? void 0 : _a.min_occupancy) || 1,
        maxOccupancy: ((_b = $props.accommodation) == null ? void 0 : _b.max_occupancy) || 10,
        showLimits: !!$props.accommodation
      }, null, 8, ["modelValue", "minOccupancy", "maxOccupancy", "showLimits"]),
      createBaseVNode("button", {
        class: "check-availability-button",
        onClick: $setup.checkAvailability,
        disabled: !$setup.dateRange.start || !$setup.dateRange.end || $setup.isChecking
      }, toDisplayString($setup.isChecking ? "Checking..." : "Check Availability"), 9, _hoisted_2$3),
      $setup.availabilityResult ? (openBlock(), createElementBlock("div", _hoisted_3$3, [
        $setup.availabilityResult.available && !$setup.bookingSuccess ? (openBlock(), createElementBlock("div", _hoisted_4$3, [
          createBaseVNode("div", _hoisted_5$3, [
            createCommentVNode(" Left column: Booking form (2/3 width) "),
            createVNode($setup["BookingForm"], {
              accommodationId: $props.accommodation.id,
              isSubmitting: $setup.bookingInProgress,
              onSubmit: $setup.submitBooking,
              onShowTerms: _cache[1] || (_cache[1] = ($event) => _ctx.$emit("show-terms"))
            }, null, 8, ["accommodationId", "isSubmitting"]),
            createCommentVNode(" Right column: Pricing breakdown (1/3 width) "),
            createVNode($setup["PricingBreakdown"], {
              availabilityResult: $setup.availabilityResult,
              dateRange: $setup.dateRange,
              numberOfPersons: $setup.numberOfPersons
            }, null, 8, ["availabilityResult", "dateRange", "numberOfPersons"])
          ])
        ])) : createCommentVNode("v-if", true),
        createCommentVNode(" Booking Success View "),
        $setup.bookingSuccess ? (openBlock(), createBlock($setup["BookingSuccess"], {
          key: 1,
          accommodation: $props.accommodation,
          dateRange: $setup.dateRange,
          numberOfPersons: $setup.numberOfPersons,
          totalPrice: $setup.availabilityResult.total_price,
          bookingReference: $setup.bookingReference,
          notes: $setup.bookingForm.notes,
          onReset: _cache[2] || (_cache[2] = ($event) => _ctx.$emit("reset"))
        }, null, 8, ["accommodation", "dateRange", "numberOfPersons", "totalPrice", "bookingReference", "notes"])) : !$setup.availabilityResult.available ? (openBlock(), createElementBlock(
          Fragment,
          { key: 2 },
          [
            createCommentVNode(" Not Available View "),
            createBaseVNode("div", _hoisted_6$1, [
              _cache[4] || (_cache[4] = createBaseVNode(
                "div",
                { class: "availability-icon" },
                "✗",
                -1
                /* HOISTED */
              )),
              createBaseVNode("div", _hoisted_7$1, [
                _cache[3] || (_cache[3] = createBaseVNode(
                  "p",
                  null,
                  "Sorry, not available for the selected dates.",
                  -1
                  /* HOISTED */
                )),
                $setup.availabilityResult.message ? (openBlock(), createElementBlock(
                  "p",
                  _hoisted_8,
                  toDisplayString($setup.availabilityResult.message),
                  1
                  /* TEXT */
                )) : createCommentVNode("v-if", true),
                $setup.availabilityResult.reason === "minimum_notice" && $setup.availabilityResult.details ? (openBlock(), createElementBlock(
                  "p",
                  _hoisted_9,
                  " This accommodation requires bookings to be made at least " + toDisplayString($setup.availabilityResult.details.minimum_booking_notice) + " days in advance. ",
                  1
                  /* TEXT */
                )) : createCommentVNode("v-if", true)
              ])
            ])
          ],
          2112
          /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
        )) : createCommentVNode("v-if", true)
      ])) : createCommentVNode("v-if", true)
    ]);
  }
  const AvailabilityChecker = /* @__PURE__ */ _export_sfc(_sfc_main$4, [["render", _sfc_render$4], ["__file", "/home/<USER>/Sites/personal/bookingbear/component/availability-search/src/components/availability-search/AvailabilityChecker.vue"]]);
  const _sfc_main$3 = {
    __name: "TermsModal",
    props: {
      termsContent: {
        type: String,
        default: ""
      }
    },
    emits: ["close", "accept"],
    setup(__props, { expose: __expose }) {
      __expose();
      const __returned__ = {};
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  };
  const _hoisted_1$2 = { class: "terms-modal" };
  const _hoisted_2$2 = { class: "terms-modal-content" };
  const _hoisted_3$2 = { class: "terms-modal-header" };
  const _hoisted_4$2 = { class: "terms-modal-body" };
  const _hoisted_5$2 = ["innerHTML"];
  const _hoisted_6 = { key: 1 };
  const _hoisted_7 = { class: "terms-modal-footer" };
  function _sfc_render$3(_ctx, _cache, $props, $setup, $data, $options) {
    return openBlock(), createElementBlock("div", _hoisted_1$2, [
      createBaseVNode("div", _hoisted_2$2, [
        createBaseVNode("div", _hoisted_3$2, [
          _cache[2] || (_cache[2] = createBaseVNode(
            "h3",
            null,
            "Terms and Conditions",
            -1
            /* HOISTED */
          )),
          createBaseVNode("button", {
            class: "close-button",
            onClick: _cache[0] || (_cache[0] = ($event) => _ctx.$emit("close"))
          }, "×")
        ]),
        createBaseVNode("div", _hoisted_4$2, [
          $props.termsContent ? (openBlock(), createElementBlock("div", {
            key: 0,
            innerHTML: $props.termsContent
          }, null, 8, _hoisted_5$2)) : (openBlock(), createElementBlock("div", _hoisted_6, _cache[3] || (_cache[3] = [
            createStaticVNode("<h4>Booking Terms</h4><p>By making a booking, you agree to the following terms and conditions:</p><ul><li>Full payment is required to confirm your booking.</li><li>Cancellations made more than 14 days before check-in will receive a full refund.</li><li>Cancellations made 7-14 days before check-in will receive a 50% refund.</li><li>Cancellations made less than 7 days before check-in are non-refundable.</li><li>Check-in time is from 2:00 PM, and check-out time is by 10:00 AM.</li><li>The number of guests must not exceed the maximum occupancy stated.</li><li>Pets are not allowed unless specifically stated in the accommodation details.</li><li>Smoking is not permitted inside the accommodation.</li></ul><h4>Privacy Policy</h4><p>Your personal information will be used solely for the purpose of managing your booking and will not be shared with third parties except as required by law.</p>", 5)
          ])))
        ]),
        createBaseVNode("div", _hoisted_7, [
          createBaseVNode("button", {
            class: "accept-button",
            onClick: _cache[1] || (_cache[1] = ($event) => _ctx.$emit("accept"))
          }, "Accept")
        ])
      ])
    ]);
  }
  const TermsModal = /* @__PURE__ */ _export_sfc(_sfc_main$3, [["render", _sfc_render$3], ["__file", "/home/<USER>/Sites/personal/bookingbear/component/availability-search/src/components/availability-search/TermsModal.vue"]]);
  const _sfc_main$2 = {
    __name: "BrandingFooter",
    props: {
      userSettings: {
        type: Object,
        required: true
      }
    },
    setup(__props, { expose: __expose }) {
      __expose();
      const props = __props;
      const showBranding = computed(() => {
        return props.userSettings.subscription_plan === "Free";
      });
      const showText = ref(window.innerWidth > 768);
      const handleResize = () => {
        showText.value = window.innerWidth > 768;
      };
      onMounted(() => {
        window.addEventListener("resize", handleResize);
      });
      onUnmounted(() => {
        window.removeEventListener("resize", handleResize);
      });
      watch(() => props.userSettings, (newSettings) => {
      }, { deep: true });
      const __returned__ = { props, showBranding, showText, handleResize, ref, computed, onMounted, onUnmounted, watch };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  };
  const _hoisted_1$1 = {
    key: 0,
    class: "branding-footer"
  };
  const _hoisted_2$1 = { class: "branding-content" };
  const _hoisted_3$1 = { key: 0 };
  const _hoisted_4$1 = {
    href: "https://www.bookingbear.co.za",
    target: "_blank",
    rel: "noopener noreferrer",
    class: "branding-link"
  };
  const _hoisted_5$1 = { key: 0 };
  function _sfc_render$2(_ctx, _cache, $props, $setup, $data, $options) {
    return $setup.showBranding ? (openBlock(), createElementBlock("div", _hoisted_1$1, [
      createBaseVNode("div", _hoisted_2$1, [
        $setup.showText ? (openBlock(), createElementBlock("span", _hoisted_3$1, "Powered by")) : createCommentVNode("v-if", true),
        createBaseVNode("a", _hoisted_4$1, [
          _cache[0] || (_cache[0] = createStaticVNode('<svg version="1.2" xmlns="http://www.w3.org/2000/svg" viewBox="0.0019912506953868037 -0.00040560360087562447 118.17527760076872 127.00778362750553" width="24" height="24"><g id="Layer 1"><path fill="#000000" d="m82.3 8.1q0.2-0.2 0.3-0.4c3-4.1 7.3-6.7 12.3-7.5 5-0.7 10.2 0.3 14.3 3.4q0.7 0.5 1.4 1.2q0.7 0.6 1.3 1.3q0.6 0.7 1.1 1.4q0.6 0.7 1 1.5q0.5 0.8 0.9 1.7q0.4 0.8 0.6 1.7q0.3 0.8 0.6 1.7q0.2 0.9 0.3 1.8c1 8-2.5 13.2-7.1 19.1 1.8 6.3 2.3 12.2 2.4 18.6 7.4 10.6 6.4 17.9 6.4 30.1 0 5 0.3 10.2-0.4 15.3-1.2 9.1-5.5 15.9-12.7 21.5q-1.2 0.7-2.4 1.5q-1.4 0.8-2.8 1.5q-1.5 0.8-3 1.3q-1.6 0.6-3.2 1-1.5 0.4-3.1 0.6c-8.1 1.1-18.5 0.4-26.8 0.4-10.6 0-21.4 0.5-32 0-2.5-0.2-5.1-0.5-7.5-1.1q-0.7-0.2-1.5-0.4q-0.7-0.2-1.5-0.5q-0.7-0.3-1.4-0.6q-0.7-0.3-1.4-0.6q-0.7-0.4-1.4-0.8q-0.7-0.3-1.4-0.7q-0.6-0.4-1.3-0.9q-0.6-0.4-1.3-0.9q-0.6-0.5-1.2-1-0.6-0.5-1.1-1-0.6-0.5-1.2-1.1q-0.5-0.5-1-1.1q-0.5-0.6-1-1.2q-0.5-0.6-1-1.3q-0.4-0.6-0.9-1.2q-0.4-0.7-0.8-1.4c-1.9-3.3-3.2-7-3.9-10.7-1.2-6.7-1.4-29.9 0.3-36.1 1.1-4.1 4.5-7.3 5.4-11.3 0.6-2.7 0.4-5.7 0.6-8.5 0.3-3.2 1-6.3 1.8-9.4-4-4-6.7-8.2-7.3-14-0.6-5.1 0.6-10.4 3.8-14.4 3.5-4.2 7.8-6 13.2-6.5 1-0.1 2-0.1 3.1 0 6.1 0.7 10.3 3.4 14.2 8.1 8.6-1.5 15.6-1.4 23.8 1.9 7.4-3.2 14.6-3.4 22.5-2z"></path><path fill="#b07b42" d="m59.5 113.6c-3.2-3.6-6.8-7-10.2-10.4l-16-16.2c-4.8-4.9-10.4-9.7-14-15.6-2.6-4.2-4.1-8.9-4.9-13.8-1.8-9.8 0.2-20.4 5.9-28.6 5.1-7.1 12.3-12.4 21-14 6.5-1.1 12.7 0.3 18.1 4 6.1-3.7 12.7-5.2 19.7-3.4 8.6 2.1 15.5 8.1 20 15.5 6 10 6.9 21.3 4.1 32.5-1.1 4.5-2.9 8.5-5.6 12.2-2.8 4-6.5 7.4-9.9 11l-15.7 15.9c-2.9 3-8.4 9.8-11.6 11.7-0.4-0.3-0.7-0.4-0.9-0.8z"></path><path fill="#6d471f" d="m59.4 19c6.1-3.7 12.7-5.2 19.7-3.4 8.6 2.1 15.5 8.1 20 15.5 6 10 6.9 21.3 4.1 32.5-1.1 4.5-2.9 8.5-5.6 12.2-2.8 4-6.5 7.4-9.9 11l-15.7 15.9c-2.9 3-8.4 9.8-11.6 11.7-0.4-0.3-0.7-0.4-0.9-0.8 1.1-2.2 0.4-15.6 0-18.7 3.5-1 6.5-0.7 10.1-2.4 5-2.4 8.8-7 10.6-12.2 2.4-7.1 2.4-17 0.5-24.3-0.8-2.9-2.1-5.7-3.4-8.4-1.4-1.4-2.6-3-3.9-4.4-4.4-2.5-8.3-4.1-13.4-4.6-1-3.1 0.2-14.7-0.6-19.6z"></path><path fill="#000000" d="m73.4 43.2c-0.3-2.4-0.2-4.7 1.3-6.8 1.1-1.4 2.7-2.1 4.4-2.3 2.3-0.3 4.7 0.2 6.5 1.7 1.5 1.1 2.6 2.9 2.8 4.8 0.2 1.9-0.5 3.6-1.7 5-1.3 1.6-3 2.4-5.1 2.6-1.6 0.1-2.9-0.2-4.3-0.6-1.4-1.4-2.6-3-3.9-4.4z"></path><path fill="#f7f7f8" d="m80.5 37.6q0.1 0 0.2 0c0.9 0 1.3 0.4 1.8 1 0.2 0.9 0.1 1-0.3 1.7q-0.4 0.1-0.8 0.2c-0.7 0.1-0.9 0.1-1.4-0.4-0.2-0.9 0.2-1.7 0.5-2.5z"></path><path fill="#b07b42" d="m60 38.6c5.1 0.5 9 2.1 13.4 4.6 1.3 1.4 2.5 3 3.9 4.4 1.3 2.7 2.6 5.5 3.4 8.4 1.9 7.3 1.9 17.2-0.5 24.3-1.8 5.2-5.6 9.8-10.6 12.2-3.6 1.7-6.6 1.4-10.1 2.4l-0.8-0.2c-3.4-0.3-6.6-0.9-9.6-2.4-5.3-2.5-8.8-7-10.6-12.4-2.3-6.9-2.5-16.4-0.6-23.4 0.8-2.9 2.1-5.6 3.5-8.3l4.6-5.3c4.4-2.3 8.7-4.3 13.8-4.3z"></path><path fill="#e9c48f" d="m46 42.9c4.4-2.3 8.7-4.3 13.8-4.3q-0.1 5.3-0.2 10.6c-3.3 0-6.7 0.5-9.2 3.1-1.3 1.4-2 3.1-2.4 5.1 0.7 2.9 1.4 5.3 4.2 7 1.7 1.1 3.8 1.9 5.7 2.4 0.2 2.9 0.9 6.9-1.1 9.3q-1.2 1.4-3 1.5c-3.3 0.1-5-2.4-7.6-3.7-0.4 0.9 0 2.5 0.3 3.4 1 2.7 3.5 4.6 6 5.7 1.6 0.7 3.4 1.1 5.1 1.4q0.9 0.2 1.8 0.3c0.1 2 0.6 8.5-0.2 9.9l-0.5 0.1c-3.4-0.3-6.6-0.9-9.6-2.4-5.3-2.5-8.8-7-10.6-12.4-2.3-6.9-2.5-16.4-0.6-23.4 0.8-2.9 2.1-5.6 3.5-8.3z"></path><path fill="#000000" d="m59.6 49.2c3.7 0.4 7 1 9.4 4 1.3 1.5 1.9 3.5 1.7 5.5-0.3 2.3-1.7 4.3-3.6 5.7-1.8 1.3-3.8 1.9-6.1 2.3 0 2.9-0.5 8.2 1.8 10.4 0.7 0.7 1.6 0.9 2.5 0.8 2.2-0.1 6.3-3.7 7-3.6 0.4 0.8 0.2 1.6-0.1 2.4-1.2 3-3.1 5.2-6.1 6.4-2.7 1.2-5.7 1.3-8.5 1.3-1.7-0.3-3.5-0.7-5.1-1.4-2.5-1.1-5-3-6-5.7-0.3-0.9-0.7-2.5-0.3-3.4 2.6 1.3 4.3 3.8 7.6 3.7q1.8-0.1 3-1.5c2-2.4 1.3-6.4 1.1-9.3-1.9-0.5-4-1.3-5.7-2.4-2.8-1.7-3.5-4.1-4.2-7 0.4-2 1.1-3.7 2.4-5.1 2.5-2.6 5.9-3.1 9.2-3.1z"></path><path fill="#000000" d="m41.4 48.2c-0.9 0.1-1.7 0.2-2.5 0.2-2.1 0.1-4.4-0.5-6-2-1.4-1.3-2.1-3.2-2.1-5q0-0.4 0-0.8 0-0.3 0.1-0.7 0-0.4 0.1-0.7 0.1-0.4 0.3-0.7 0.1-0.3 0.3-0.7 0.2-0.3 0.4-0.6 0.2-0.3 0.4-0.6 0.2-0.3 0.5-0.6 0.2-0.2 0.5-0.5 0.3-0.2 0.6-0.4 0.3-0.2 0.6-0.4 0.3-0.1 0.7-0.3 0.3-0.1 0.7-0.2 0.3-0.1 0.7-0.2 0.3-0.1 0.7-0.1 0.3 0 0.7 0c2.2 0 4.5 1 6 2.6 1.7 1.8 1.9 4 1.9 6.4z"></path><path fill="#f7f7f8" d="m38.1 37.9q0-0.1 0.1-0.1 0.1 0 0.1 0 0.1 0 0.2 0 0.1 0 0.1 0 0.1 0 0.2 0 0 0 0.1 0.1 0.1 0 0.2 0 0 0 0.1 0.1 0 0 0.1 0.1 0.1 0 0.1 0 0.1 0.1 0.1 0.1 0.1 0.1 0.1 0.2 0.1 0 0.1 0.1 0 0 0.1 0.1 0 0.1 0 0.1 0.1 0.1 0.1 0.2 0 0 0 0.1 0 0.1 0 0.1 0 0.1 0 0.2 0 0.1 0 0.1 0 0.1 0 0.2 0 0 0 0.1 0 0.1-0.1 0.1 0 0.1 0 0.2-0.1 0-0.1 0.1 0 0.1-0.1 0.1 0 0.1-0.1 0.1 0 0.1-0.1 0.1-0.1 0.1-0.1 0-0.1 0.1-0.1 0-0.2 0 0 0.1-0.1 0.1-0.1 0-0.1 0-0.1 0-0.2 0-0.1 0.1-0.1 0.1-0.1 0-0.2-0.1-0.1 0-0.1 0-0.1 0-0.2 0 0 0-0.1-0.1-0.1 0-0.1 0-0.1-0.1-0.2-0.1 0 0-0.1-0.1 0 0-0.1-0.1 0 0-0.1-0.1 0-0.1-0.1-0.1 0-0.1 0-0.1-0.1-0.1-0.1-0.2 0 0 0-0.1-0.1-0.1-0.1-0.2 0 0 0-0.1 0-0.1 0-0.1 0-0.1 0-0.2 0-0.1 0-0.1 0-0.1 0.1-0.2 0 0 0-0.1 0-0.1 0.1-0.1 0-0.1 0.1-0.2 0 0 0-0.1 0.1 0 0.1-0.1 0.1 0 0.2-0.1 0 0 0.1-0.1 0 0 0.1 0 0.1-0.1 0.1-0.1 0.1 0 0.2 0z"></path><path fill="#089eaf" d="m8.1 64c0.8 0.6 1.7 4 2.1 5.1 1.6 3.8 3.8 7.6 6.4 10.8 3.4 4 7.3 7.6 10.9 11.3l18.9 19.2c2.7 2.8 6.1 5.6 8.5 8.7l-0.4 0.4c-7.3 0.2-20 0.9-26.9-0.3q-0.6-0.1-1.2-0.3-0.6-0.1-1.2-0.2-0.5-0.2-1.1-0.4-0.6-0.2-1.1-0.4-0.6-0.2-1.1-0.4-0.6-0.3-1.1-0.6-0.5-0.2-1-0.5-0.6-0.3-1.1-0.6-0.5-0.3-0.9-0.7-0.5-0.3-1-0.7-0.5-0.4-0.9-0.7-0.5-0.4-0.9-0.8-0.4-0.5-0.8-0.9-0.4-0.4-0.8-0.9-0.4-0.4-0.8-0.9-0.3-0.5-0.7-1c-1.9-2.7-3.1-5.8-3.8-9-1.5-7.2-1.3-28.9 0-36.2z"></path><path fill="#088d82" d="m110.4 66.1c0.7 1.2 0.5 4.8 0.5 6.4v13.8c0.1 9.5 0.3 18.4-6.9 25.7-6.9 7-15.4 7.5-24.8 7.6h-12.9l-0.2-0.3c1.2-2.1 6.9-7 8.9-9 5.3-5.3 10.5-10.7 15.8-16.1 4.9-5 10.2-9.9 14-15.9 2.5-3.8 4-7.9 5.6-12.2z"></path><path fill="#6d471f" d="m8.8 21.8c-0.2-3.2-0.2-6.3 1.3-9.2 1.2-2.3 3.2-4 5.7-4.8 3.3-1 7.1-0.5 10.2 1.1 1.1 0.6 2.4 1.5 2.8 2.7-0.5 0.7-1.2 0.9-2 1.2-6.9 4.8-10 8.2-14.6 15.2-1.5-1.7-3.2-3.8-3.4-6.2z"></path><path fill="#b07b42" d="m8.8 21.8c-0.2-3.2-0.2-6.3 1.3-9.2 1.2-2.3 3.2-4 5.7-4.8 3.3-1 7.1-0.5 10.2 1.1 1.1 0.6 2.4 1.5 2.8 2.7-0.5 0.7-1.2 0.9-2 1.2-3.5-1.1-8.2-1.5-11.4 0.2-3.4 1.7-4.7 5.7-5.7 9l-0.2 0.5c-0.4-0.2-0.4-0.3-0.7-0.7z"></path><path fill="#533419" d="m88.8 11.9c2.7-2.8 5.5-4.6 9.6-4.6 2.8 0 5.7 0.9 7.7 3 1.5 1.4 2.6 3.6 3 5.6q0.1 0.8 0.2 1.6c0.4 4.1-1.2 7.5-3.7 10.6-3.4-6.8-8-10.8-14-15.3z"></path><path fill="#6d471f" d="m88.8 11.9c2.7-2.8 5.5-4.6 9.6-4.6 2.8 0 5.7 0.9 7.7 3 1.5 1.4 2.6 3.6 3 5.6q-0.1 2.2-0.2 4.5c-1.5-2.6-3.6-5.7-6.3-7.3-3-1.7-7.8-1.2-11-0.3z"></path></g></svg>', 1)),
          $setup.showText ? (openBlock(), createElementBlock("span", _hoisted_5$1, "Booking Bear")) : createCommentVNode("v-if", true)
        ])
      ])
    ])) : createCommentVNode("v-if", true);
  }
  const BrandingFooter = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["render", _sfc_render$2], ["__file", "/home/<USER>/Sites/personal/bookingbear/component/availability-search/src/components/availability-search/BrandingFooter.vue"]]);
  const componentName = "AvailabilitySearch";
  const _sfc_main$1 = {
    __name: "AvailabilitySearchMain",
    props: {
      siteId: {
        type: String,
        required: true
      },
      groupId: {
        type: String,
        required: false,
        default: null
      }
    },
    emits: ["loaded", "error", "retry", "update", "accommodation-selected", "availability-checked", "booking-created"],
    setup(__props, { expose: __expose, emit: __emit }) {
      const API_BASE_URL = "https://laravel-app.lndo.site";
      const props = __props;
      const emit2 = __emit;
      const loading = ref(true);
      const error = ref(null);
      const accommodations = ref([]);
      const selectedAccommodation = ref(null);
      const componentVersion = "1.0.0";
      const instance = getCurrentInstance();
      const authToken = ref(null);
      const authInProgress = ref(false);
      const userSettings = ref({
        booking_terms_and_conditions: "",
        subscription_plan: "Free"
        // Default to Free plan until we get the actual value from the API
      });
      const showTerms = ref(false);
      const emitEvent = (type, detail = {}) => {
        var _a;
        emit2(type, detail);
        const event = new CustomEvent(`bookingbear:${type}`, {
          bubbles: true,
          composed: true,
          // Allows event to cross shadow DOM boundary
          detail: { ...detail, source: "booking-bear-component", version: componentVersion }
        });
        if ((_a = instance == null ? void 0 : instance.proxy) == null ? void 0 : _a.$el) {
          instance.proxy.$el.dispatchEvent(event);
        }
      };
      const getCachedData = () => {
        try {
          const cacheKey = `bookingbear-cache:${API_BASE_URL}/api/accommodations`;
          const cachedItem = localStorage.getItem(cacheKey);
          if (cachedItem) {
            const { data, timestamp } = JSON.parse(cachedItem);
            const cacheAge = Date.now() - timestamp;
            if (cacheAge < 5 * 60 * 1e3) {
              return data;
            }
          }
        } catch (err) {
          console.warn("Cache retrieval failed:", err);
        }
        return null;
      };
      const setCachedData = (newData) => {
        try {
          const cacheKey = `bookingbear-cache:${API_BASE_URL}/api/accommodations`;
          const cacheItem = JSON.stringify({
            data: newData,
            timestamp: Date.now()
          });
          localStorage.setItem(cacheKey, cacheItem);
        } catch (err) {
          console.warn("Cache storage failed:", err);
        }
      };
      const fetchData = async (silent = false) => {
        if (!silent) {
          loading.value = true;
        }
        error.value = null;
        try {
          if (!authToken.value) {
            const authenticated = await authenticate();
            if (!authenticated) {
              return;
            }
          }
          let url = `${API_BASE_URL}/api/accommodations?published=true`;
          if (props.groupId) {
            url += `&group_id=${encodeURIComponent(props.groupId)}`;
          }
          const response = await fetch(url, {
            headers: getAuthHeaders()
          });
          if (!response.ok) {
            if (response.status === 401) {
              const authenticated = await authenticate();
              if (authenticated) {
                const retryResponse = await fetch(url, {
                  headers: getAuthHeaders()
                });
                if (!retryResponse.ok) {
                  let errorMessage;
                  switch (retryResponse.status) {
                    case 403:
                      errorMessage = "You don't have permission to view these accommodations.";
                      break;
                    case 404:
                      errorMessage = "The accommodations could not be found.";
                      break;
                    case 500:
                      errorMessage = "The booking service is currently experiencing technical difficulties.";
                      break;
                    default:
                      errorMessage = `Unable to load accommodations (Error ${retryResponse.status}).`;
                  }
                  throw new Error(errorMessage);
                }
                const newData2 = await retryResponse.json();
                processAccommodationsData(newData2);
                return;
              } else {
                return;
              }
            } else {
              let errorMessage;
              switch (response.status) {
                case 403:
                  errorMessage = "You don't have permission to view these accommodations.";
                  break;
                case 404:
                  errorMessage = "The accommodations could not be found.";
                  break;
                case 500:
                  errorMessage = "The booking service is currently experiencing technical difficulties.";
                  break;
                default:
                  errorMessage = `Unable to load accommodations (Error ${response.status}).`;
              }
              throw new Error(errorMessage);
            }
          }
          const newData = await response.json();
          processAccommodationsData(newData);
        } catch (err) {
          error.value = err.message || "Failed to load accommodations. Please try again later.";
          console.error("API fetch error:", err);
          emitEvent("error", { error: error.value, originalError: err });
        } finally {
          loading.value = false;
        }
      };
      const processAccommodationsData = (newData) => {
        if (JSON.stringify(accommodations.value) !== JSON.stringify(newData.data)) {
          accommodations.value = newData.data.map((accommodation) => {
            if (accommodation.city && accommodation.province) {
              accommodation.location = `${accommodation.city}, ${accommodation.province}`;
            } else if (accommodation.city) {
              accommodation.location = accommodation.city;
            } else if (accommodation.province) {
              accommodation.location = accommodation.province;
            }
            if (accommodation.default_price) {
              accommodation.base_price = `R${accommodation.default_price}`;
            }
            return accommodation;
          });
          emitEvent("update", { accommodations: accommodations.value });
        }
        error.value = null;
        setCachedData(newData);
        emitEvent("loaded", { accommodations: accommodations.value });
      };
      const progressiveLoad = () => {
        const cachedData = getCachedData();
        if (cachedData) {
          accommodations.value = cachedData.data;
          loading.value = false;
          emitEvent("cache-loaded", { source: "cache" });
        }
        fetchData();
      };
      const selectAccommodation = (accommodation) => {
        selectedAccommodation.value = accommodation;
        emitEvent("accommodation-selected", { accommodation });
      };
      const authenticate = async () => {
        if (authInProgress.value) return;
        authInProgress.value = true;
        try {
          const domain = window.location.origin;
          const response = await fetch(`${API_BASE_URL}/api/widget-auth`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Accept": "application/json",
              "X-Widget-Version": componentVersion
            },
            body: JSON.stringify({
              site_id: props.siteId,
              domain
            })
          });
          if (!response.ok) {
            let errorMessage;
            switch (response.status) {
              case 401:
                errorMessage = "This website is not authorized to display accommodations. Please contact the accommodation provider.";
                break;
              case 404:
                errorMessage = "The booking service could not be found. Please check your site ID or contact the accommodation provider.";
                break;
              case 422:
                errorMessage = "Invalid site configuration. Please contact the accommodation provider.";
                break;
              case 500:
                errorMessage = "The booking service is currently experiencing technical difficulties. Please try again later.";
                break;
              default:
                errorMessage = `Connection error (${response.status}). Please try again later or contact the accommodation provider.`;
            }
            throw new Error(errorMessage);
          }
          const result = await response.json();
          if (!result.token) {
            throw new Error("Unable to connect to the booking service. Please try again later.");
          }
          authToken.value = result.token;
          return true;
        } catch (err) {
          console.error("Authentication error:", err);
          error.value = err.message || "Unable to connect to the booking service. Please try again later.";
          emitEvent("error", { error: error.value, originalError: err });
          return false;
        } finally {
          authInProgress.value = false;
          if (error.value) {
            loading.value = false;
          }
        }
      };
      const getAuthHeaders = () => {
        const headers = {
          "Content-Type": "application/json",
          "Accept": "application/json",
          "X-Widget-Version": componentVersion
        };
        if (authToken.value) {
          headers["Authorization"] = `Bearer ${authToken.value}`;
        }
        return headers;
      };
      const fetchUserSettings = async () => {
        try {
          if (!authToken.value) {
            const authenticated = await authenticate();
            if (!authenticated) {
              throw new Error("Authentication failed");
            }
          }
          const response = await fetch(`${API_BASE_URL}/api/user/settings`, {
            headers: getAuthHeaders()
          });
          if (!response.ok) {
            if (response.status === 401) {
              const authenticated = await authenticate();
              if (authenticated) {
                const retryResponse = await fetch(`${API_BASE_URL}/api/user/settings`, {
                  headers: getAuthHeaders()
                });
                if (!retryResponse.ok) {
                  throw new Error(`API error after re-authentication: ${retryResponse.status}`);
                }
                const settingsData2 = await retryResponse.json();
                userSettings.value = settingsData2;
                return;
              } else {
                throw new Error("Re-authentication failed");
              }
            } else {
              throw new Error(`API error: ${response.status}`);
            }
          }
          const settingsData = await response.json();
          userSettings.value = settingsData;
        } catch (err) {
          console.error("Failed to fetch user settings:", err.message);
        }
      };
      const handleAvailabilityResult = (data) => {
        emitEvent("availability-checked", data);
      };
      const handleBookingCreated = (data) => {
        emitEvent("booking-created", data);
      };
      const handleError2 = (data) => {
        error.value = data.error;
        emitEvent("error", data);
      };
      const resetBooking = () => {
        selectedAccommodation.value = null;
      };
      const acceptTerms = () => {
        showTerms.value = false;
      };
      onMounted(() => {
        emitEvent("mounted", { version: componentVersion });
        authenticate().then((success) => {
          if (success) {
            progressiveLoad();
            fetchUserSettings();
          }
        });
      });
      __expose({
        fetchData,
        selectAccommodation,
        resetBooking,
        fetchUserSettings
      });
      const __returned__ = { componentName, API_BASE_URL, props, emit: emit2, loading, error, accommodations, selectedAccommodation, componentVersion, instance, authToken, authInProgress, userSettings, showTerms, emitEvent, getCachedData, setCachedData, fetchData, processAccommodationsData, progressiveLoad, selectAccommodation, authenticate, getAuthHeaders, fetchUserSettings, handleAvailabilityResult, handleBookingCreated, handleError: handleError2, resetBooking, acceptTerms, ref, onMounted, getCurrentInstance, defineComponent, LoadingState, ErrorState, AccommodationList, AccommodationDetail, AvailabilityChecker, TermsModal, BrandingFooter };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  };
  const _hoisted_1 = { class: "booking-bear-component" };
  const _hoisted_2 = {
    key: 2,
    class: "content",
    "data-state": "loaded"
  };
  const _hoisted_3 = { class: "accommodations-container" };
  const _hoisted_4 = { key: 0 };
  const _hoisted_5 = {
    key: 1,
    class: "selected-accommodation"
  };
  function _sfc_render$1(_ctx, _cache, $props, $setup, $data, $options) {
    return openBlock(), createElementBlock("div", _hoisted_1, [
      $setup.loading ? (openBlock(), createBlock($setup["LoadingState"], { key: 0 })) : $setup.error ? (openBlock(), createBlock($setup["ErrorState"], {
        key: 1,
        error: $setup.error,
        onRetry: $setup.fetchData
      }, null, 8, ["error"])) : (openBlock(), createElementBlock("div", _hoisted_2, [
        renderSlot(_ctx.$slots, "header", { accommodations: $setup.accommodations }),
        createBaseVNode("div", _hoisted_3, [
          !$setup.selectedAccommodation ? (openBlock(), createElementBlock("div", _hoisted_4, [
            createVNode($setup["AccommodationList"], {
              accommodations: $setup.accommodations,
              apiBaseUrl: $setup.API_BASE_URL,
              onSelect: $setup.selectAccommodation
            }, null, 8, ["accommodations", "apiBaseUrl"])
          ])) : (openBlock(), createElementBlock("div", _hoisted_5, [
            createBaseVNode("button", {
              class: "back-button",
              onClick: _cache[0] || (_cache[0] = ($event) => $setup.selectedAccommodation = null)
            }, "← Back to list"),
            createVNode($setup["AccommodationDetail"], {
              accommodation: $setup.selectedAccommodation,
              apiBaseUrl: $setup.API_BASE_URL
            }, null, 8, ["accommodation", "apiBaseUrl"]),
            createVNode($setup["AvailabilityChecker"], {
              accommodation: $setup.selectedAccommodation,
              apiBaseUrl: $setup.API_BASE_URL,
              authToken: $setup.authToken,
              componentVersion: $setup.componentVersion,
              onAvailabilityResult: $setup.handleAvailabilityResult,
              onBookingCreated: $setup.handleBookingCreated,
              onError: $setup.handleError,
              onShowTerms: _cache[1] || (_cache[1] = ($event) => $setup.showTerms = true),
              onReset: $setup.resetBooking
            }, null, 8, ["accommodation", "apiBaseUrl", "authToken", "componentVersion"])
          ]))
        ]),
        renderSlot(_ctx.$slots, "footer", { accommodations: $setup.accommodations }),
        createCommentVNode(" Branding Footer - only shows for Free plan users "),
        createVNode($setup["BrandingFooter"], { userSettings: $setup.userSettings }, null, 8, ["userSettings"])
      ])),
      createCommentVNode(" Terms and Conditions Modal "),
      $setup.showTerms ? (openBlock(), createBlock($setup["TermsModal"], {
        key: 3,
        termsContent: $setup.userSettings.booking_terms_and_conditions,
        onClose: _cache[2] || (_cache[2] = ($event) => $setup.showTerms = false),
        onAccept: $setup.acceptTerms
      }, null, 8, ["termsContent"])) : createCommentVNode("v-if", true)
    ]);
  }
  const AvailabilitySearchMain = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["render", _sfc_render$1], ["__file", "/home/<USER>/Sites/personal/bookingbear/component/availability-search/src/components/availability-search/AvailabilitySearchMain.vue"]]);
  const _style_0 = `
/* Import all component styles here to ensure they're included in the shadow DOM */
/* CSS Variables for the BookingBear component */
/*
 * Note: The actual CSS variables are now defined in the :host selector
 * of the AvailabilitySearch.ce.vue component to ensure they are properly
 * scoped within the Shadow DOM and can access external custom properties.
 */
.h_datepicker[data-v-e09819b5]{-webkit-user-select:none;user-select:none;display:flex;justify-content:space-between;max-width:1000px;max-height:600px;height:500px;position:relative;overflow:hidden}
.h_datepicker_invisible[data-v-e09819b5]{visibility:hidden}
.h_datepicker_popup[data-v-e09819b5]{color:#fff;text-align:center;position:absolute;pointer-events:none;border-radius:8px;font-size:11px;background-color:#0b253c;padding:.5rem;box-sizing:border-box;z-index:100}
.h_datepicker_dates_container[data-v-e09819b5],.h_datepicker_weeks_container[data-v-e09819b5]{display:flex;flex-wrap:wrap;justify-content:space-between;align-content:flex-start;width:100%}
.h_datepicker_weeks_container[data-v-e09819b5]{height:15%}
.h_datepicker_dates_container[data-v-e09819b5]{height:85%}
.h_datepicker_month[data-v-e09819b5]{min-width:320px}
.h_datepicker_one_month_display[data-v-e09819b5]{width:100%}
.h_datepicker_two_month_display[data-v-e09819b5]{width:45%}
.h_datepicker_month_control_panel[data-v-e09819b5]{display:flex;width:100%;height:14%;justify-content:space-between;align-items:center;margin-bottom:1%}
.h_datepicker_hidden[data-v-e09819b5]{display:none}
.h_datepicker_month_control_item[data-v-e09819b5]{display:flex;justify-content:center;align-items:center;font-weight:700;font-size:1.5rem;line-height:2rem;white-space:nowrap;text-align:center}
.h_datepicker_month_control_btn[data-v-e09819b5]{cursor:pointer}
.h_datepicker_week_name[data-v-e09819b5]{width:13%;border-radius:20px;height:100%;margin-bottom:5px;display:flex;justify-content:center;align-items:center;box-sizing:border-box}
.h_datepicker_month_box[data-v-e09819b5]{display:flex;flex-wrap:wrap;justify-content:space-between;width:100%;height:85%;text-transform:uppercase;font-weight:400}
.h_datepicker_day[data-v-e09819b5]{color:#333;text-align:center;width:13%;border-radius:20px;height:15%;margin-bottom:1%;display:flex;justify-content:center;align-items:center;box-sizing:border-box}
.h_datepicker_notCurrentMonth[data-v-e09819b5]{opacity:0;cursor:default!important}
.h_datepicker_valid[data-v-e09819b5]{cursor:pointer;-webkit-user-select:none;-moz-user-select:none;user-select:none}
.h_datepicker_invalid[data-v-e09819b5]{color:#e8ebf4}
.h_datepicker_last_day_selected[data-v-e09819b5],.h_datepicker_first_day_selected[data-v-e09819b5]{color:#fff;background-color:#005172!important}
.h_datepicker_hovering[data-v-e09819b5]{color:#fff;background-color:#008ebd}
.h_datepicker_selected[data-v-e09819b5]{color:#fff;background-color:#1fc5ff}
.h_datepicker_disabled[data-v-e09819b5]{background-color:#fcb2be}
@media (max-width: 768px){.h_datepicker_month-2[data-v-e09819b5]{display:none!important}.h_datepicker_month[data-v-e09819b5]{width:100%}.h_datepicker_day[data-v-e09819b5]{height:65px}}
/* Loading state styles */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
}
.loading-indicator {
  width: 40px;
  height: 40px;
  border: 4px solid var(--color-border);
  border-top-color: var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
@keyframes spin {
to { transform: rotate(360deg);
}
}
.loading-text {
  margin-top: var(--spacing-md);
  color: var(--color-text-light);
}
/* Error state styles */
.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  text-align: center;
  background-color: #f8f9fa;
  border-radius: var(--border-radius);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  max-width: 500px;
  margin: 0 auto;
}
.error-icon {
  color: #d32f2f;
  margin-bottom: var(--spacing-md);
}
.error-title {
  font-size: 1.5rem;
  color: var(--color-primary);
  margin-bottom: var(--spacing-sm);
}
.error-message {
  color: #555;
  margin-bottom: var(--spacing-md);
  font-size: 1.1rem;
  line-height: 1.5;
}
.error-help-text {
  color: #777;
  margin-bottom: var(--spacing-lg);
  font-size: 0.9rem;
  max-width: 400px;
}
.retry-button {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: var(--spacing-sm) var(--spacing-lg);
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}
.retry-button:hover {
  background-color: #002a41;
}
/* Accommodations container and group styles */
.accommodations-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}
.accommodation-group {
  margin-bottom: var(--spacing-lg);
  background-color: transparent;
}
.group-title {
  font-size: 2rem;
  margin-top: 0;
  margin-bottom: var(--spacing-lg);
  color: var(--color-primary);
}
.group-description {
  margin-top: 0;
  margin-bottom: var(--spacing-md);
  color: var(--color-text-light);
  font-size: 0.95rem;
  line-height: 1.5;
}
/* Accommodations grid styles */
.accommodations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-sm);
}
@media (max-width: 768px) {
.accommodations-grid {
    grid-template-columns: 1fr;
}
}
/* Accommodation card styles */
.accommodation-card {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  text-decoration: none;
  transition: all 0.3s ease;
  display: block;
  position: relative;
  height: 100%;
  border: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
}
.accommodation-card:before {
  content: "";
  position: absolute;
  top: -10px;
  right: -10px;
  width: 100px;
  height: 100px;
  background-image: radial-gradient(circle, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
  background-size: 10px 10px;
  z-index: 0;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}
.accommodation-card:hover:before {
  opacity: 1;
}
.accommodation-image {
  height: 220px;
  overflow: hidden;
  position: relative;
}
.accommodation-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 60%, rgba(0, 0, 0, 0.4) 100%);
  z-index: 1;
  pointer-events: none;
}
.accommodation-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}
.accommodation-rating {
  position: absolute;
  top: 12px;
  right: 12px;
  background-color: rgba(255, 255, 255, 0.9);
  color: #000;
  border-radius: 20px;
  padding: 4px 10px;
  font-size: 0.85rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
  z-index: 2;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.rating-star {
  color: #f59e0b; /* Amber color for star */
}
.accommodation-details {
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex-grow: 1;
}
.accommodation-info {
  margin-bottom: 16px;
}
.accommodation-name {
  margin-top: 0;
  margin-bottom: 8px;
  color: var(--color-primary, #1e3a8a);
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.accommodation-location {
  margin: 0 0 12px;
  color: var(--color-text-light, #6b7280);
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 6px;
}
.location-icon {
  flex-shrink: 0;
  color: var(--color-text-light, #6b7280);
}
.accommodation-description {
  margin: 0;
  color: var(--color-text, #374151);
  font-size: 0.9rem;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.accommodation-footer {
  padding-top: 12px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  margin-top: auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
  align-items: center;
}
@media (min-width: 768px) {
.accommodation-footer {
		flex-direction: row;
		justify-content: space-between;
}
}
.accommodation-footer button {
	padding: 8px 16px;
	background: var(--color-primary);
	color: white;
	border: none;
	border-radius: 4px;
	transition: opacity 0.3s ease-in-out;
}
.accommodation-footer button:hover {
	opacity: 0.8;
}
.accommodation-price {
  margin: 0;
  color: var(--color-text, #374151);
  font-size: 0.95rem;
}
.accommodation-price span {
  color: var(--color-accent-primary, #2563eb);
  font-weight: 700;
  font-size: 1.1rem;
}
/* Accommodation detail styles */
.selected-accommodation-details {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}
@media (max-width: 768px) {
.selected-accommodation-details {
    grid-template-columns: 1fr;
}
}
.accommodation-image {
  height: 300px;
  overflow: hidden;
  position: relative;
}
.accommodation-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
  border-radius: 12px;
}
.slider-arrow {
  position: absolute;
  top: 50%;
  width: 32px;
  height: 32px;
  font-size: 24px;
  line-height: 10px;
  transform: translateY(-50%);
  background: rgba(255,255,255,0.8);
  color: #000;
  border: none;
  padding: var(--spacing-sm);
  cursor: pointer;
  border-radius: 50%;
  z-index: 10;
  transition: opacity 0.3s, transform 0.3s;
}
.slider {
	position: relative;
	height: 100%;
}
.slider-arrow.prev {
  left: var(--spacing-sm);
}
.slider-arrow.next {
  right: var(--spacing-sm);
}
.slider-arrow:hover {
  opacity: 0.8;
  transform: translateY(-50%) scale(1.1);
}
.accommodation-info {
  padding: var(--spacing-md) 0;
}
.accommodation-name {
  font-size: 1.5rem;
  margin-top: 0;
  margin-bottom: var(--spacing-md);
  color: var(--color-primary);
}
.accommodation-location {
  margin: 0 0 var(--spacing-sm);
  color: var(--color-text-light);
  font-size: 0.9rem;
}
.accommodation-price {
  margin: 0 0 var(--spacing-sm);
  color: var(--color-accent-primary);
  font-weight: bold;
}
.accommodation-description {
  margin-top: var(--spacing-md);
  line-height: 1.6;
  font-size: 1rem;
  color: var(--color-text);
}
/* Date picker styles */
.date-picker-container {
  margin-bottom: var(--spacing-lg);
}
/* Additional custom styles for the date picker */
.hotel-datepicker {
  width: 100% !important;
  max-width: 600px;
}
.datepicker__month-day--valid {
  color: var(--color-text) !important;
}
.datepicker__month-day--selected {
  background-color: var(--color-primary) !important;
  color: white !important;
}
.datepicker__month-day--first-day-selected,
.datepicker__month-day--last-day-selected {
  background-color: var(--color-primary) !important;
  color: white !important;
}
.datepicker__month-day--in-range {
  background-color: rgba(0, 59, 92, 0.2) !important;
}
.datepicker__month-day--disabled {
  background-color: rgba(255, 191, 0, 0.1) !important;
  color: #ccc !important;
  text-decoration: line-through;
}
/* Occupancy selector styles */
.occupancy-selector {
  margin-bottom: var(--spacing-lg);
}
.occupancy-selector label {
  display: block;
  margin-bottom: var(--spacing-sm);
  color: var(--color-primary);
  font-weight: bold;
}
.occupancy-input-container {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}
.occupancy-button {
  background-color: var(--color-primary);
  color: white;
  border: none;
  width: 36px;
  height: 36px;
  font-size: 1.2rem;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}
.occupancy-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}
.occupancy-input {
  width: 60px;
  height: 36px;
  text-align: center;
  border: 1px solid var(--color-border);
  margin: 0 var(--spacing-sm);
  font-size: 1rem;
}
.occupancy-limits {
  color: var(--color-text-light);
  font-size: 0.8rem;
}
/* Availability check styles */
.availability-check {
  background-color: #f9f9f9;
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}
.availability-check h3 {
  margin-top: 0;
  margin-bottom: var(--spacing-md);
  color: var(--color-primary);
}
.check-availability-button {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: var(--spacing-md) var(--spacing-lg);
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
  width: 100%;
  margin-bottom: var(--spacing-lg);
}
.check-availability-button:hover:not(:disabled) {
  background-color: #e05a73;
}
.check-availability-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}
/* Availability results */
.availability-result {
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  margin-top: var(--spacing-md);
}
.available, .not-available {
  display: flex;
  align-items: center;
}
.available {
  color: var(--color-accent-primary);
}
.not-available {
  background-color: rgba(246, 120, 145, 0.1);
  color: var(--color-accent-cta);
}
.availability-icon {
  font-size: 2rem;
  flex-grow: 0;
}
.availability-message p {
  margin: var(--spacing-sm) 0;
}
.price {
  font-weight: bold;
  font-size: 1.1rem;
}
/* Availability result container */
.availability-result-container {
  display: grid;
  grid-template-columns: 2fr 1fr; /* Form takes 2/3, pricing breakdown takes 1/3 */
  gap: var(--spacing-lg);
}
@media (max-width: 768px) {
.availability-result-container {
    grid-template-columns: 1fr;
    grid-template-areas:
      "pricing"
      "form";
}
}
/* Booking form styles */
.booking-form {
  background-color: white;
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.form-title {
  color: var(--color-primary);
  margin-top: 0;
  margin-bottom: var(--spacing-lg);
  font-size: 1.2rem;
}
.form-group {
  margin-bottom: var(--spacing-md);
}
.form-group label {
  display: block;
  margin-bottom: var(--spacing-sm);
  color: var(--color-text);
  font-weight: 500;
}
.form-control {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s;
  box-sizing: border-box;
  background: white;
}
textarea.form-control {
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
  line-height: 1.5;
}
.form-control:focus {
  border-color: var(--color-primary);
  outline: none;
}
.form-group.has-error .form-control {
  border-color: #d32f2f;
}
.error-message {
  color: #d32f2f;
  font-size: 0.85rem;
  margin-top: 4px;
}
.terms-checkbox {
  margin-top: var(--spacing-lg);
}
.checkbox-container {
  display: flex;
  align-items: center;
  position: relative;
  padding-left: 30px;
  cursor: pointer;
  font-size: 0.9rem;
  user-select: none;
}
.checkbox-container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}
.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 20px;
  width: 20px;
  background-color: #eee;
  border-radius: 3px;
}
.checkbox-container:hover input ~ .checkmark {
  background-color: #ccc;
}
.checkbox-container input:checked ~ .checkmark {
  background-color: var(--color-primary);
}
.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}
.checkbox-container input:checked ~ .checkmark:after {
  display: block;
}
.checkbox-container .checkmark:after {
  left: 7px;
  top: 3px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}
.form-actions {
  margin-top: var(--spacing-lg);
}
.submit-booking-button {
  background-color: var(--color-accent-cta);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: var(--spacing-md) var(--spacing-lg);
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
  width: 100%;
}
.submit-booking-button:hover:not(:disabled) {
  background-color: #e05a73;
}
.submit-booking-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}
/* Pricing breakdown styles */
.availability-title-wrapper {
	display: flex;
	align-items: center;
	gap: 20px;
}
.pricing-breakdown {
  background-color: rgba(0, 155, 119, 0.05);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
}
.availability-title {
  color: var(--color-accent-primary);
  margin-top: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  font-size: 1.2rem;
}
.price-details {
  margin-top: var(--spacing-md);
}
.price-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.price-row.total {
  font-weight: bold;
  font-size: 1.1rem;
  color: var(--color-accent-primary);
  border-bottom: none;
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-sm);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}
.booking-dates {
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-md);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}
.date-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
}
.date-label, .price-label {
  color: var(--color-text-light);
}
.date-value, .price-value {
  font-weight: 500;
}
/* Booking success styles */
.booking-success {
  background-color: white;
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  text-align: center;
  box-shadow: var(--shadow);
}
.success-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--spacing-md);
  color: var(--color-accent-primary);
}
.success-icon svg {
  width: 100%;
  height: 100%;
}
.success-title {
  color: var(--color-accent-primary);
  margin-bottom: var(--spacing-sm);
  font-size: 1.5rem;
}
.success-message {
  color: var(--color-text);
  margin-bottom: var(--spacing-lg);
}
.booking-summary {
  background-color: #f9f9f9;
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  text-align: left;
}
.booking-summary h4 {
  margin-top: 0;
  margin-bottom: var(--spacing-md);
  color: var(--color-primary);
}
.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.summary-label {
  color: var(--color-text-light);
}
.summary-value {
  font-weight: 500;
}
.new-search-button {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: var(--spacing-md) var(--spacing-lg);
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
}
.new-search-button:hover {
  background-color: #002a41;
}
/* Terms and conditions modal styles */
.terms-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.terms-modal-content {
  background-color: white;
  border-radius: var(--border-radius);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.terms-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--color-border);
}
.terms-modal-header h3 {
  margin: 0;
  color: var(--color-primary);
}
.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--color-text-light);
}
.terms-modal-body {
  padding: var(--spacing-lg);
  overflow-y: auto;
  flex-grow: 1;
}
.terms-modal-body h4 {
  color: var(--color-primary);
  margin-top: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
}
.terms-modal-body h4:first-child {
  margin-top: 0;
}
.terms-modal-body p {
  margin-bottom: var(--spacing-md);
  line-height: 1.5;
}
.terms-modal-body ul {
  margin-bottom: var(--spacing-lg);
  padding-left: var(--spacing-lg);
}
.terms-modal-body li {
  margin-bottom: var(--spacing-sm);
  line-height: 1.5;
}
.terms-modal-footer {
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid var(--color-border);
  text-align: right;
}
.accept-button {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
}
.accept-button:hover {
  background-color: #002a41;
}
/* Not available message styles */
.not-available {
  display: flex;
  align-items: center;
  background-color: rgba(246, 120, 145, 0.1);
  color: var(--color-accent-cta);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  gap: 20px;
}
.availability-icon {
  font-size: 2rem;
  flex-grow: 0;
}
.availability-message p {
  margin: var(--spacing-sm) 0;
}
/* Branding Footer Styles */
.branding-footer {
  margin-top: 20px;
  padding: 10px 0;
  text-align: center;
  font-size: 12px;
  color: var(--color-text-light);
  border-top: 1px solid var(--color-border);
}
.branding-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}
.branding-link {
  display: flex;
  align-items: center;
  gap: 5px;
  color: var(--color-primary);
  text-decoration: none;
  font-weight: 500;
  transition: opacity 0.2s ease;
}
.branding-link:hover {
  opacity: 0.8;
}
/* Responsive adjustments */
@media (max-width: 768px) {
.branding-footer {
    font-size: 10px;
}
}
/* Main component styles */
.booking-bear-component {
  border-radius: var(--border-radius);
}
/* Content */
.title {
  color: var(--color-primary);
  margin-top: 0;
  margin-bottom: var(--spacing-lg);
  font-size: 1.8rem;
}
/* Selected accommodation */
.selected-accommodation {
  background-color: white;
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow);
}
.back-button {
  background-color: transparent;
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
  border-radius: var(--border-radius);
  padding: var(--spacing-sm) var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s, color 0.2s;
}
.back-button:hover {
  background-color: var(--color-primary);
  color: white;
}
:host {
  /* Map external CSS variables to internal ones */
  --color-background: var(--bb-color-background, #E5F3FF);
  --color-primary: var(--bb-color-primary, #003B5C);
  --color-accent-primary: var(--bb-color-accent-primary, #009B77);
  --color-accent-secondary: var(--bb-color-accent-secondary, #FFBF00);
  --color-accent-cta: var(--bb-color-accent-cta, #F67891);

  /* Text colors */
  --color-text: #333;
  --color-text-light: #666;

  /* Border and spacing */
  --color-border: #ddd;
  --border-radius: 8px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;

  /* Effects */
  --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  /* Allow component to inherit custom properties from parent */
  color: var(--color-text);
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  display: block;
  width: 100%;
}
`;
  const _sfc_main = {
    __name: "AvailabilitySearch.ce",
    props: {
      siteId: {
        type: String,
        required: true
      },
      groupId: {
        type: String,
        required: false,
        default: null
      }
    },
    setup(__props, { expose: __expose }) {
      __expose();
      const props = __props;
      const __returned__ = { props, defineComponent, AvailabilitySearchMain };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  };
  function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
    return openBlock(), createBlock($setup["AvailabilitySearchMain"], {
      siteId: $props.siteId,
      groupId: $props.groupId
    }, null, 8, ["siteId", "groupId"]);
  }
  const AvailabilitySearch2 = /* @__PURE__ */ _export_sfc(_sfc_main, [["render", _sfc_render], ["styles", [_style_0]], ["__file", "/home/<USER>/Sites/personal/bookingbear/component/availability-search/src/components/AvailabilitySearch.ce.vue"]]);
  console.log("Running in development mode with Vue DevTools enabled");
  function registerCustomElement() {
    const AvailabilitySearchElement2 = /* @__PURE__ */ defineCustomElement(AvailabilitySearch2);
    if (customElements.get("availability-search")) {
      console.log("Re-registering custom element for HMR");
      document.querySelectorAll("availability-search").forEach((el) => {
        const attributes = Array.from(el.attributes);
        const parent = el.parentNode;
        const nextSibling = el.nextSibling;
        const computedStyle = window.getComputedStyle(el);
        const cssVars = {
          "--bb-color-background": computedStyle.getPropertyValue("--bb-color-background"),
          "--bb-color-primary": computedStyle.getPropertyValue("--bb-color-primary"),
          "--bb-color-accent-primary": computedStyle.getPropertyValue("--bb-color-accent-primary"),
          "--bb-color-accent-secondary": computedStyle.getPropertyValue("--bb-color-accent-secondary"),
          "--bb-color-accent-cta": computedStyle.getPropertyValue("--bb-color-accent-cta")
        };
        parent.removeChild(el);
        const newEl = document.createElement("availability-search");
        attributes.forEach((attr) => {
          newEl.setAttribute(attr.name, attr.value);
        });
        Object.entries(cssVars).forEach(([prop, value]) => {
          if (value && value.trim()) {
            newEl.style.setProperty(prop, value);
          }
        });
        if (nextSibling) {
          parent.insertBefore(newEl, nextSibling);
        } else {
          parent.appendChild(newEl);
        }
      });
    } else {
      customElements.define("availability-search", AvailabilitySearchElement2);
      console.log("Custom element registered: availability-search");
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === "childList") {
            mutation.addedNodes.forEach((node) => {
              if (node.nodeName && node.nodeName.toLowerCase() === "availability-search") {
                applyCssVariablesToElement(node);
              }
            });
          }
        });
      });
      observer.observe(document.body, { childList: true, subtree: true });
    }
    return AvailabilitySearchElement2;
  }
  function applyCssVariablesToElement(element) {
    const inlineStyle = element.getAttribute("style");
    if (inlineStyle && inlineStyle.includes("--bb-color")) {
      return;
    }
    if (element.className) {
      const tempEl = document.createElement("div");
      tempEl.className = element.className;
      document.body.appendChild(tempEl);
      const classStyle = window.getComputedStyle(tempEl);
      [
        "--bb-color-background",
        "--bb-color-primary",
        "--bb-color-accent-primary",
        "--bb-color-accent-secondary",
        "--bb-color-accent-cta"
      ].forEach((prop) => {
        const value = classStyle.getPropertyValue(prop);
        if (value && value.trim()) {
          element.style.setProperty(prop, value);
        }
      });
      document.body.removeChild(tempEl);
    }
  }
  const AvailabilitySearchElement = registerCustomElement();
  document.querySelectorAll("availability-search").forEach(applyCssVariablesToElement);
  const app = createApp({
    name: "BookingBearDevApp",
    template: "<div>BookingBear DevTools Connector</div>"
  });
  const devtoolsConnector = document.createElement("div");
  devtoolsConnector.style.display = "none";
  document.body.appendChild(devtoolsConnector);
  app.mount(devtoolsConnector);
  exports.AvailabilitySearchElement = AvailabilitySearchElement;
  Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
  return exports;
}({});
//# sourceMappingURL=availability-search-v1.0.0.iife.js.map
