<?php

use App\Http\Controllers\API\AccommodationApiController;
use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\BookingApiController;
use App\Http\Controllers\API\UserController;
use App\Http\Controllers\API\WidgetAuthController;
use Illuminate\Support\Facades\Route;

/**
 * @OA\Info(
 *     title="BookingBear API",
 *     version="1.0.0",
 *     description="API for BookingBear accommodation booking system",
 *     @OA\Contact(
 *         email="<EMAIL>",
 *         name="API Support"
 *     ),
 *     @OA\License(
 *         name="MIT",
 *         url="https://opensource.org/licenses/MIT"
 *     )
 * )
 *
 * @OA\Server(
 *     url="https://laravel-app.lndo.site/api",
 *     description="Local Development Server"
 * )
 *
 * @OA\SecurityScheme(
 *     securityScheme="sanctum",
 *     type="http",
 *     scheme="bearer",
 *     bearerFormat="JWT"
 * )
 */

/**
 * @group Authentication
 *
 * APIs for user authentication
 */

// Authentication routes
Route::post('/login', [AuthController::class, 'login']);

// Widget authentication endpoint (public)
Route::post('/widget-auth', [WidgetAuthController::class, 'authenticate']);

// User routes
Route::middleware('auth:sanctum')->get('/user', [UserController::class, 'getCurrentUser']);
Route::middleware(['auth:sanctum', 'ability:widget-access'])->get('/user/settings', [UserController::class, 'getUserSettings']);

// Notification routes
Route::get('/user/notifications', [\App\Http\Controllers\API\NotificationApiController::class, 'getNotifications'])->middleware(['auth:sanctum']);

// Public widget routes (protected by Sanctum with widget-access ability)
Route::middleware(['auth:sanctum', 'ability:widget-access'])->group(function () {
    // Public accommodation listing and details
    Route::get('accommodations', [AccommodationApiController::class, 'index']);
    Route::get('accommodations/{id}', [AccommodationApiController::class, 'show']);

    // Check availability endpoint
    Route::post('accommodations/{id}/check-availability', [AccommodationApiController::class, 'checkAvailability']);

    // Get unavailable dates for an accommodation
    Route::get('accommodations/{id}/unavailable-dates', [AccommodationApiController::class, 'getUnavailableDates']);

    // Get comprehensive unavailability data for an accommodation
    Route::get('accommodations/{id}/unavailability-data', [AccommodationApiController::class, 'getUnavailabilityData']);

    // Booking creation
    Route::post('bookings', [BookingApiController::class, 'store']);
});

// Admin/authenticated routes
Route::middleware(['auth:sanctum', 'ensure.plan'])->group(function () {
    // Admin accommodation management
    Route::post('accommodations', [AccommodationApiController::class, 'store']);
    Route::put('accommodations/{id}', [AccommodationApiController::class, 'update']);
    Route::delete('accommodations/{id}', [AccommodationApiController::class, 'destroy']);
});
