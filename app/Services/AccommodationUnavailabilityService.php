<?php

namespace App\Services;

use App\Models\Accommodation;
use App\Models\AccommodationUnavailablePeriod;
use App\Models\Booking;
use Carbon\Carbon;

class AccommodationUnavailabilityService
{
    /**
     * Get comprehensive unavailability data for an accommodation.
     * This includes unavailable periods, existing bookings, minimum stay, and minimum booking notice.
     *
     * @param Accommodation $accommodation
     * @param string|null $startDate Optional start date to filter results (Y-m-d format)
     * @param string|null $endDate Optional end date to filter results (Y-m-d format)
     * @return array
     */
    public function getUnavailabilityData(Accommodation $accommodation, ?string $startDate = null, ?string $endDate = null): array
    {
        $data = [
            'accommodation_id' => $accommodation->id,
            'minimum_stay' => $accommodation->minimum_stay,
            'minimum_booking_notice' => $accommodation->minimum_booking_notice,
            'unavailable_periods' => $this->getUnavailablePeriods($accommodation, $startDate, $endDate),
            'existing_bookings' => $this->getExistingBookings($accommodation, $startDate, $endDate),
            'minimum_booking_notice_cutoff' => $this->getMinimumBookingNoticeCutoff($accommodation),
        ];

        return $data;
    }

    /**
     * Get unavailable periods for the accommodation.
     *
     * @param Accommodation $accommodation
     * @param string|null $startDate
     * @param string|null $endDate
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getUnavailablePeriods(Accommodation $accommodation, ?string $startDate = null, ?string $endDate = null)
    {
        $query = AccommodationUnavailablePeriod::where('accommodation_id', $accommodation->id)
            ->where('active', true);

        // If date range is provided, filter periods that might overlap
        if ($startDate && $endDate) {
            $query->where(function ($q) use ($startDate, $endDate) {
                // Include date range periods that overlap with the requested range
                $q->where('type', 'date_range')
                    ->where(function ($dateQuery) use ($startDate, $endDate) {
                        $dateQuery->whereBetween('start_date', [$startDate, $endDate])
                            ->orWhereBetween('end_date', [$startDate, $endDate])
                            ->orWhere(function ($overlapQuery) use ($startDate, $endDate) {
                                $overlapQuery->where('start_date', '<=', $startDate)
                                    ->where('end_date', '>=', $endDate);
                            });
                    })
                    // Always include recurring days periods as they might affect any date range
                    ->orWhere('type', 'recurring_days');
            });
        }

        return $query->orderBy('start_date')->get();
    }

    /**
     * Get existing bookings for the accommodation.
     *
     * @param Accommodation $accommodation
     * @param string|null $startDate
     * @param string|null $endDate
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getExistingBookings(Accommodation $accommodation, ?string $startDate = null, ?string $endDate = null)
    {
        $query = Booking::where('accommodation_id', $accommodation->id)
            ->whereIn('booking_status_id', [1, 2, 3]); // Confirmed, Pending, Checked-in statuses

        // If date range is provided, filter bookings that overlap
        if ($startDate && $endDate) {
            $query->where(function ($q) use ($startDate, $endDate) {
                $q->whereBetween('start_date', [$startDate, $endDate])
                    ->orWhereBetween('end_date', [$startDate, $endDate])
                    ->orWhere(function ($overlapQuery) use ($startDate, $endDate) {
                        $overlapQuery->where('start_date', '<=', $startDate)
                            ->where('end_date', '>=', $endDate);
                    });
            });
        }

        return $query->orderBy('start_date')
            ->select(['id', 'start_date', 'end_date', 'booking_status_id', 'occupancy'])
            ->with('status:id,name')
            ->get();
    }

    /**
     * Get the minimum booking notice cutoff date.
     *
     * @param Accommodation $accommodation
     * @return string|null
     */
    private function getMinimumBookingNoticeCutoff(Accommodation $accommodation): ?string
    {
        if ($accommodation->minimum_booking_notice <= 0) {
            return null;
        }

        return Carbon::now()
            ->addDays($accommodation->minimum_booking_notice)
            ->format('Y-m-d');
    }

    /**
     * Check if a specific date range is available for booking.
     *
     * @param Accommodation $accommodation
     * @param string $startDate
     * @param string $endDate
     * @param int $occupancy
     * @return array
     */
    public function checkAvailability(Accommodation $accommodation, string $startDate, string $endDate, int $occupancy = 1): array
    {
        $result = [
            'available' => true,
            'conflicts' => [],
            'details' => []
        ];

        // Check minimum booking notice
        if ($accommodation->minimum_booking_notice > 0) {
            $today = Carbon::now()->startOfDay();
            $bookingStartDate = Carbon::parse($startDate)->startOfDay();
            $daysUntilBooking = $today->diffInDays($bookingStartDate);

            if ($daysUntilBooking < $accommodation->minimum_booking_notice) {
                $result['available'] = false;
                $result['conflicts'][] = 'minimum_notice';
                $result['details']['minimum_booking_notice'] = [
                    'required_days' => $accommodation->minimum_booking_notice,
                    'days_until_booking' => $daysUntilBooking,
                    'cutoff_date' => $this->getMinimumBookingNoticeCutoff($accommodation)
                ];
            }
        }

        // Check minimum stay requirement
        $nights = Carbon::parse($startDate)->diffInDays(Carbon::parse($endDate));
        if ($nights < $accommodation->minimum_stay) {
            $result['available'] = false;
            $result['conflicts'][] = 'minimum_stay';
            $result['details']['minimum_stay'] = [
                'required_nights' => $accommodation->minimum_stay,
                'requested_nights' => $nights
            ];
        }

        // Check existing bookings
        $existingBookings = $this->getExistingBookings($accommodation, $startDate, $endDate);
        if ($existingBookings->count() > 0) {
            $result['available'] = false;
            $result['conflicts'][] = 'existing_booking';
            $result['details']['existing_bookings'] = $existingBookings->toArray();
        }

        // Check unavailable periods
        $unavailabilityChecker = app(AvailabilityChecker::class);
        if ($unavailabilityChecker->hasAnyConflict($accommodation, $startDate, $endDate)) {
            $result['available'] = false;
            $result['conflicts'][] = 'blocked_period';
            $result['details']['blocked_periods'] = $this->getUnavailablePeriods($accommodation, $startDate, $endDate)->toArray();
        }

        return $result;
    }

    /**
     * Get unavailable dates in a simple array format for calendar widgets.
     *
     * @param Accommodation $accommodation
     * @param string|null $startDate
     * @param string|null $endDate
     * @return array
     */
    public function getUnavailableDatesArray(Accommodation $accommodation, ?string $startDate = null, ?string $endDate = null): array
    {
        $unavailableDates = [];

        // Get date range from parameters or default to next 2 years
        $start = $startDate ? Carbon::parse($startDate) : Carbon::now();
        $end = $endDate ? Carbon::parse($endDate) : Carbon::now()->addYears(2);

        // Add dates from unavailable periods
        $unavailablePeriods = $this->getUnavailablePeriods($accommodation, $start->format('Y-m-d'), $end->format('Y-m-d'));
        foreach ($unavailablePeriods as $period) {
            if ($period->type === 'date_range') {
                $periodStart = Carbon::parse($period->start_date);
                $periodEnd = Carbon::parse($period->end_date);
                
                while ($periodStart <= $periodEnd) {
                    if ($periodStart >= $start && $periodStart <= $end) {
                        $unavailableDates[] = $periodStart->format('Y-m-d');
                    }
                    $periodStart->addDay();
                }
            } elseif ($period->type === 'recurring_days') {
                $current = $start->copy();
                while ($current <= $end) {
                    $dayOfWeek = (int) $current->format('w'); // 0 (Sunday) to 6 (Saturday)
                    if (in_array($dayOfWeek, $period->days_of_week, true)) {
                        $unavailableDates[] = $current->format('Y-m-d');
                    }
                    $current->addDay();
                }
            }
        }

        // Add dates from existing bookings
        $existingBookings = $this->getExistingBookings($accommodation, $start->format('Y-m-d'), $end->format('Y-m-d'));
        foreach ($existingBookings as $booking) {
            $bookingStart = Carbon::parse($booking->start_date);
            $bookingEnd = Carbon::parse($booking->end_date);
            
            while ($bookingStart <= $bookingEnd) {
                if ($bookingStart >= $start && $bookingStart <= $end) {
                    $unavailableDates[] = $bookingStart->format('Y-m-d');
                }
                $bookingStart->addDay();
            }
        }

        // Add dates within minimum booking notice period
        if ($accommodation->minimum_booking_notice > 0) {
            $cutoffDate = Carbon::parse($this->getMinimumBookingNoticeCutoff($accommodation));
            $current = $start->copy();
            
            while ($current < $cutoffDate && $current <= $end) {
                $unavailableDates[] = $current->format('Y-m-d');
                $current->addDay();
            }
        }

        return array_unique($unavailableDates);
    }
}
