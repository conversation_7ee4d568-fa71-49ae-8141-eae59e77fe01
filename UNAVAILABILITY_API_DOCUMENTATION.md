# Accommodation Unavailability API Documentation

## Overview

The new Accommodation Unavailability API provides comprehensive unavailability data for accommodations, designed specifically for calendar widgets and booking systems. This API aggregates data from multiple sources to give a complete picture of when an accommodation is not available for booking.

## API Endpoint

```
GET /api/accommodations/{id}/unavailability-data
```

## Data Sources

The API aggregates unavailability data from the following sources:

1. **Accommodation Unavailable Periods** - Manually set blocked periods (date ranges and recurring days)
2. **Existing Bookings** - Confirmed, pending, and checked-in bookings
3. **Minimum Stay Requirements** - Accommodation-specific minimum night requirements
4. **Minimum Booking Notice** - Required advance booking periods

## Authentication

This endpoint requires authentication using Laravel Sanctum with `widget-access` ability:

```javascript
headers: {
  'Authorization': 'Bearer YOUR_TOKEN',
  'Content-Type': 'application/json',
  'Accept': 'application/json',
  'X-Widget-Version': 'v1.0.0'
}
```

## Request Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | integer | Yes | Accommodation ID (in URL path) |
| `start_date` | string | No | Start date filter (Y-m-d format) |
| `end_date` | string | No | End date filter (Y-m-d format) |
| `format` | string | No | Response format: `detailed` (default) or `dates_array` |

## Response Formats

### Detailed Format (default)

```json
{
  "data": {
    "accommodation_id": 1,
    "minimum_stay": 2,
    "minimum_booking_notice": 1,
    "minimum_booking_notice_cutoff": "2024-01-02",
    "unavailable_periods": [
      {
        "id": 1,
        "accommodation_id": 1,
        "type": "date_range",
        "start_date": "2024-01-15",
        "end_date": "2024-01-20",
        "days_of_week": null,
        "reason": "Maintenance",
        "active": true,
        "created_at": "2024-01-01T00:00:00.000000Z",
        "updated_at": "2024-01-01T00:00:00.000000Z"
      },
      {
        "id": 2,
        "accommodation_id": 1,
        "type": "recurring_days",
        "start_date": null,
        "end_date": null,
        "days_of_week": [0, 6],
        "reason": "Weekend maintenance",
        "active": true,
        "created_at": "2024-01-01T00:00:00.000000Z",
        "updated_at": "2024-01-01T00:00:00.000000Z"
      }
    ],
    "existing_bookings": [
      {
        "id": 1,
        "start_date": "2024-01-10",
        "end_date": "2024-01-12",
        "booking_status_id": 1,
        "occupancy": 2,
        "status": {
          "id": 1,
          "name": "Confirmed"
        }
      }
    ]
  }
}
```

### Simple Dates Array Format

```json
{
  "data": {
    "accommodation_id": 1,
    "unavailable_dates": [
      "2024-01-01",
      "2024-01-02",
      "2024-01-07",
      "2024-01-08",
      "2024-01-10",
      "2024-01-11",
      "2024-01-12",
      "2024-01-15",
      "2024-01-16",
      "2024-01-17",
      "2024-01-18",
      "2024-01-19",
      "2024-01-20"
    ]
  }
}
```

## Usage Examples

### JavaScript/Vue.js Example

```javascript
// Fetch detailed unavailability data
async function getUnavailabilityData(accommodationId, authToken) {
  const response = await fetch(`/api/accommodations/${accommodationId}/unavailability-data`, {
    headers: {
      'Authorization': `Bearer ${authToken}`,
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }
  });
  
  if (!response.ok) {
    throw new Error(`API error: ${response.status}`);
  }
  
  return await response.json();
}

// Fetch simple date array for calendar widgets
async function getUnavailableDates(accommodationId, authToken) {
  const params = new URLSearchParams({
    format: 'dates_array',
    start_date: '2024-01-01',
    end_date: '2024-12-31'
  });
  
  const response = await fetch(`/api/accommodations/${accommodationId}/unavailability-data?${params}`, {
    headers: {
      'Authorization': `Bearer ${authToken}`,
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }
  });
  
  const result = await response.json();
  return result.data.unavailable_dates;
}
```

### Vue Component Integration

```vue
<template>
  <div>
    <AvailabilityChecker 
      ref="availabilityChecker"
      :accommodation="accommodation"
      :api-base-url="apiBaseUrl"
      :auth-token="authToken"
      :component-version="componentVersion"
    />
    <button @click="loadCalendarData">Load Calendar Data</button>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import AvailabilityChecker from './AvailabilityChecker.vue';

const availabilityChecker = ref(null);

const loadCalendarData = async () => {
  if (availabilityChecker.value) {
    const data = await availabilityChecker.value.getUnavailabilityData('dates_array');
    console.log('Unavailable dates:', data.unavailable_dates);
  }
};
</script>
```

## Error Handling

The API returns standard HTTP status codes:

- `200` - Success
- `401` - Unauthenticated
- `404` - Accommodation not found or no permission
- `500` - Server error

Error response format:
```json
{
  "message": "Accommodation not found or you do not have permission to access it"
}
```

## Performance Considerations

1. **Date Range Filtering**: Use `start_date` and `end_date` parameters to limit the data returned
2. **Format Selection**: Use `dates_array` format for simple calendar widgets to reduce payload size
3. **Caching**: Consider caching the response on the client side for frequently accessed accommodations
4. **Rate Limiting**: Be mindful of API rate limits when making frequent requests

## Integration with Existing Systems

This new API endpoint complements the existing `/api/accommodations/{id}/check-availability` endpoint:

- Use `/unavailability-data` for calendar displays and date picker restrictions
- Use `/check-availability` for specific date range availability checks with pricing

## Service Class

The API uses the `AccommodationUnavailabilityService` class which provides:

- `getUnavailabilityData()` - Comprehensive unavailability data
- `checkAvailability()` - Availability check for specific dates
- `getUnavailableDatesArray()` - Simple array of unavailable dates

## Migration from Existing Endpoints

If you're currently using `/api/accommodations/{id}/unavailable-dates`, you can migrate to the new endpoint:

**Old endpoint:**
```javascript
// Only returns accommodation unavailable periods
const response = await fetch(`/api/accommodations/${id}/unavailable-dates`);
```

**New endpoint:**
```javascript
// Returns comprehensive unavailability data including bookings, minimum stay, etc.
const response = await fetch(`/api/accommodations/${id}/unavailability-data`);
```

The new endpoint provides much more comprehensive data while maintaining backward compatibility through the detailed format structure.
